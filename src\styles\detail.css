.detail_container {
  display: flex;
  flex-direction: row;
  gap: 1rem;
  flex-wrap: wrap;
}

/* detail_container it self not first child */
/* border top */
.detail_container:not(:first-child) {
  margin-top: 0.6rem;
  border-top: 1px solid rgb(242, 242, 242);
  padding-top: 0.6rem;
}

.detail_container .title {
}

.detail_container .content {
  /* flex: 1 1 0%; */
  margin-left: auto;
}

.detail_container .text {
  font-weight: bold;
}

.detail_wrapper_grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.detail_container_grid {
  display: flex;
  flex-direction: row;
  gap: 1rem;
  flex-wrap: wrap;
}

.detail_container_grid .content {
  margin-left: auto;
}
.detail_container_grid .text {
  font-weight: bold;
}

@media (max-width: 880px) {
  .detail_wrapper_grid {
    grid-template-columns: 1fr;
  }
}
