import React from "react";
import { Modal, Box, Alert } from "@mui/material";
import Loading from "@/components/modal/Loading";
import MySnackbar from "@/components/MySnackbar";
import Image from "next/image";
import Constants from "@/utils/Constants";
import Table from "@/components/libs/Table";
import ImageViewer from "@/components/libs/ImageViewer";

export default class ExampleModalDetail extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showDialog: false,
      formType: "detail",
      formData: null,
      formIndex: -1,
      detail: null,
      selectedImageIndex: 0, // Track currently selected main image
    };
  }

  onShowDialog = (formType, formData = null, formIndex = -1) => {
    this.setState(
      {
        formType,
        formData,
        formIndex,
        showDialog: true,
        selectedImageIndex: 0, // Reset to first image
      },
      () => {
        this.onFetchDetail();
      }
    );
  };

  onCloseDialog = () => {
    this.setState({
      showDialog: false,
      formType: "detail",
      formData: null,
      formIndex: -1,
      detail: null,
      selectedImageIndex: 0, // Reset selected image
    });
  };

  onFetchDetail = async () => {
    this.ref_Loading.onShowDialog();
    // Simulate API call with dummy data
    setTimeout(() => {
      const dummyData = {
        id: 1,
        number: "EX-001",
        input_datetime: "2024-03-20 10:30:00",
        customer_name: "John Doe",
        customer_email: "<EMAIL>",
        customer_phone: "+6281234567890",
        status: "active",
        total_items: 3,
        grand_total: 1500000,
        notes: "This is a sample note for the example detail.",
        main_image_index: 0,
        main_image_image_url: Constants.image_default.empty,
        image_array: [
          {
            id: 1,
            image_url: Constants.image_default.empty,
          },
          {
            id: 2,
            image_url: Constants.image_default.empty,
          },
          {
            id: 3,
            image_url: Constants.image_default.empty,
          },
        ],
        items: [
          {
            id: 1,
            name: "Product A",
            code: "PRD-A",
            category: "Electronics",
            price: 500000,
            quantity: 2,
            total: 1000000,
            image_url: Constants.image_default.empty,
          },
          {
            id: 2,
            name: "Product B",
            code: "PRD-B",
            category: "Accessories",
            price: 250000,
            quantity: 1,
            total: 250000,
            image_url: Constants.image_default.empty,
          },
          {
            id: 3,
            name: "Product C",
            code: "PRD-C",
            category: "Gadgets",
            price: 250000,
            quantity: 1,
            total: 250000,
            image_url: Constants.image_default.empty,
          },
        ],
      };
      this.setState({ 
        detail: dummyData,
        selectedImageIndex: dummyData.main_image_index || 0,
      });
      this.ref_Loading.onCloseDialog();
    }, 1000);
  };

  onNotify = (message, severity = "info") => {
    this.ref_MySnackbar.onNotify(message, severity);
  };

  // Handle image selection from gallery
  onSelectImage = (imageIndex) => {
    this.setState({ selectedImageIndex: imageIndex });
  };

  // Handle opening image viewer
  onOpenImageViewer = (imageUrl) => {
    this.ref_ImageViewer.onShowDialog(imageUrl);
  };

  getItemTableHeadings = () => {
    return [
      {
        key: "name",
        label: "Nama Produk",
        className: "",
        sortable: false,
        sort: "",
      },
      {
        key: "code",
        label: "Kode",
        className: "wd120",
        sortable: false,
        sort: "",
      },
      {
        key: "category",
        label: "Kategori",
        className: "wd120",
        sortable: false,
        sort: "",
      },
      {
        key: "price",
        label: "Harga",
        className: "text-right wd120",
        sortable: false,
        sort: "",
      },
      {
        key: "quantity",
        label: "Jumlah",
        className: "text-center wd100",
        sortable: false,
        sort: "",
      },
      {
        key: "total",
        label: "Total",
        className: "text-right wd140",
        sortable: false,
        sort: "",
      },
    ];
  };

  renderItemTableItems = (item, index) => {
    return (
      <tr key={index}>
        <td>
          <div>
            <div
              style={{
                fontSize: "1rem",
                fontWeight: "600",
                color: "#2d3436",
                marginBottom: "0.25rem",
              }}
              className="flex flex-row gap-4 items-center"
            >
              <Image
                src={item.image_url}
                alt={item.name}
                width={60}
                height={60}
                style={{ objectFit: "contain" }}
              />
              {item.name}
            </div>
          </div>
        </td>
        <td>{item.code}</td>
        <td>{item.category}</td>
        <td className="text-right">
          Rp{new Intl.NumberFormat("id-ID").format(item.price)}
        </td>
        <td className="text-center">{item.quantity}</td>
        <td className="text-right">
          <span
            style={{
              color: "var(--base-color)",
              fontWeight: "600",
            }}
          >
            Rp{new Intl.NumberFormat("id-ID").format(item.total)}
          </span>
        </td>
      </tr>
    );
  };

  render() {
    let addModalClass = "large";
    return (
      <>
        <Modal open={this.state.showDialog} onClose={this.onCloseDialog}>
          <Box
            sx={{
              flex: 1,
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              display: "flex",
              width: "100wh",
              height: "100vh",
            }}
          >
            <div className={`modal-content ${addModalClass}`}>
              <div className="modal-header">{this.renderHeader()}</div>
              <div className="modal-body" style={{ height: "fit-content" }}>
                {this.renderBody()}
              </div>
              <div className="modal-footer">{this.renderFooter()}</div>
            </div>
          </Box>
        </Modal>

        <Loading ref={(value) => (this.ref_Loading = value)} />
        <MySnackbar ref={(value) => (this.ref_MySnackbar = value)} />
        <ImageViewer ref={(value) => (this.ref_ImageViewer = value)} />
      </>
    );
  }

  renderHeader() {
    return (
      <>
        <div className="title">Detail Example</div>
        <span className="close" onClick={() => this.onCloseDialog()}>
          &times;
        </span>
      </>
    );
  }

  renderBody() {
    return <>{this.renderExampleDetail()}</>;
  }

  renderExampleDetail() {
    let objData = this.state.detail;

    if (!objData) {
      return (
        <div className="flex-rows no-right">
          <div className="row no-border">
            <div className="input-form">
              <Alert severity="info">Memuat detail...</Alert>
            </div>
          </div>
        </div>
      );
    }

    return (
      <>
        <div className="flex-rows no-right">
          {/* Basic Information */}
          <div className="row wd60 no-border">
            <div className="box">
              <div className="title flex flex-row gap-4 flex-wrap">
                <div>Informasi Dasar</div>
                <div className="content">
                  <span
                    className={`status ${
                      objData.status === "active" ? "" : "unavailable"
                    }`}
                  >
                    {objData.status === "active" ? "Aktif" : "Tidak Aktif"}
                  </span>
                </div>
              </div>
              <div className="detail_wrapper_grid">
                <div className="detail_container_grid">
                  <em>Nomor</em>
                  <div className="content text">{objData.number || "-"}</div>
                </div>
                <div className="detail_container_grid">
                  <em>Tanggal Input</em>
                  <div className="content text">
                    {objData.input_datetime || "-"}
                  </div>
                </div>
                <div className="detail_container_grid">
                  <em>Nama Pelanggan</em>
                  <div className="content text">
                    {objData.customer_name || "-"}
                  </div>
                </div>
                <div className="detail_container_grid">
                  <em>Email</em>
                  <div className="content text">
                    {objData.customer_email || "-"}
                  </div>
                </div>
                <div className="detail_container_grid">
                  <em>Telepon</em>
                  <div className="content text">
                    {objData.customer_phone || "-"}
                  </div>
                </div>
                <div className="detail_container_grid">
                  <em>Total Item</em>
                  <div className="content text">
                    {objData.total_items || "0"}
                  </div>
                </div>
                <div className="detail_container_grid">
                  <em>Grand Total</em>
                  <div className="content text">
                    Rp{new Intl.NumberFormat("id-ID").format(
                      objData.grand_total || 0
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Notes */}
            {objData.notes && (
              <div className="box mt-4">
                <div className="title">Catatan</div>
                <div className="detail_wrapper_grid">
                  <div className="detail_container_grid">
                    <div
                      className="content text"
                      style={{ whiteSpace: "pre-wrap" }}
                    >
                      {objData.notes}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Image Gallery */}
            {objData.image_array && objData.image_array.length > 0 && (
              <div className="box mt-4">
                <div className="title">Galeri Gambar</div>
                <div className="input-form">
                  <div style={{ textAlign: "center", marginBottom: "1rem" }}>
                    <img
                      src={
                        objData.image_array[this.state.selectedImageIndex]
                          ?.image_url || objData.main_image_image_url
                      }
                      alt="Selected Image"
                      style={{
                        maxWidth: "100%",
                        height: "auto",
                        borderRadius: "8px",
                        border: "2px solid #e9e9e9",
                        cursor: "pointer",
                      }}
                      onClick={() =>
                        this.onOpenImageViewer(
                          objData.image_array[this.state.selectedImageIndex]
                            ?.image_url || objData.main_image_image_url
                        )
                      }
                    />
                    <div
                      style={{
                        marginTop: "0.5rem",
                        fontSize: "0.9rem",
                        color: "#666",
                      }}
                    >
                      {this.state.selectedImageIndex === objData.main_image_index
                        ? "Gambar Utama"
                        : `Gambar ${this.state.selectedImageIndex + 1}`}
                    </div>
                  </div>
                </div>

                <div className="modal-divider"></div>
                <div className="input-form">
                  <div
                    style={{
                      fontSize: "0.95rem",
                      fontWeight: "600",
                      marginBottom: "0.5rem",
                    }}
                  >
                    Galeri Gambar ({objData.image_array.length} gambar)
                  </div>
                  <div
                    style={{
                      display: "grid",
                      gridTemplateColumns:
                        "repeat(auto-fill, minmax(80px, 1fr))",
                      gap: "0.5rem",
                    }}
                  >
                    {objData.image_array.map((image, index) => (
                      <div
                        key={image.id || index}
                        style={{ position: "relative" }}
                      >
                        <img
                          src={image.image_url}
                          alt={`Image ${index + 1}`}
                          style={{
                            width: "100%",
                            height: "80px",
                            objectFit: "cover",
                            borderRadius: "4px",
                            border:
                              index === this.state.selectedImageIndex
                                ? "3px solid #e74c3c"
                                : index === objData.main_image_index
                                ? "2px solid #3498db"
                                : "1px solid #e9e9e9",
                            cursor: "pointer",
                            transition: "all 0.2s ease",
                          }}
                          onClick={() => this.onSelectImage(index)}
                        />
                        {index === objData.main_image_index && (
                          <div
                            style={{
                              position: "absolute",
                              top: "2px",
                              right: "2px",
                              backgroundColor: "#3498db",
                              color: "white",
                              fontSize: "0.7rem",
                              padding: "2px 4px",
                              borderRadius: "2px",
                            }}
                          >
                            Utama
                          </div>
                        )}
                        {index === this.state.selectedImageIndex && (
                          <div
                            style={{
                              position: "absolute",
                              top: "2px",
                              left: "2px",
                              backgroundColor: "#e74c3c",
                              color: "white",
                              fontSize: "0.7rem",
                              padding: "2px 4px",
                              borderRadius: "2px",
                            }}
                          >
                            Dipilih
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Items List */}
          <div className="row wd40 no-border">
            <div className="box">
              <div className="title">Daftar Item</div>
              {objData.items && objData.items.length > 0 ? (
                <div className="input-form">
                  <Table
                    title=""
                    disabledHeader={true}
                    disabledPage={true}
                    dataHeadings={this.getItemTableHeadings()}
                    dataTables={objData.items}
                    renderItems={(item, index) =>
                      this.renderItemTableItems(item, index)
                    }
                    isFetched={false}
                    customTableContainers="items-details-table"
                  />
                </div>
              ) : (
                <div className="input-form">
                  <Alert severity="info">
                    Tidak ada item dalam data ini
                  </Alert>
                </div>
              )}
            </div>
          </div>
        </div>
      </>
    );
  }

  renderFooter() {
    return (
      <>
        <button
          className="button cancel ml-0"
          onClick={() => this.onCloseDialog()}
        >
          <i className="ph ph-bold ph-x-circle"></i>
          <span>Tutup</span>
        </button>
      </>
    );
  }
}
