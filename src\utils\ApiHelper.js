/*
Created by esoda
Created on Nov, 2023
Contact esoda.id
*/

import Constants from "./Constants";

const baseUrl = () => {
  if (window.location.hostname.includes("localhost")) {
    return Constants.apiLocalUrl;
  } else {
    return `${window.location.origin}/api/`;
  }
};

const getHeader = async () => {
  return {
    Accept: "application/json",
    "Content-Type": "application/json",
  };
};

const generateParams = (params) => {
  let rand = Math.random();
  let queryString = "";
  for (let key in params) {
    if (!params.hasOwnProperty(key)) continue;
    if (typeof params[key] === "object") {
      params[key].forEach((item, index) => {
        for (let keyItem in item) {
          queryString += `${key}[${index}][${keyItem}]=${encodeURI(
            item[keyItem]
          )}&`;
        }
      });
    } else {
      queryString += `${key}=${encodeURIComponent(params[key])}&`;
    }
  }
  // return queryString === '' ? `?t=${rand}` : `?${queryString.replace(/&+$/, '')}&t=${rand}`;
  return queryString === "" ? `` : `?${queryString.replace(/&+$/, "")}`;
};

const logOut = async () => {
  let url = "";
  let endPoint = "";
  if (window.location.pathname.startsWith("/adm/")) {
    url = Constants.webUrl.adm.login;
    endPoint = "kiosk/admin/auth/logout";
  } else {
    url = Constants.webUrl.login;
    endPoint = "kiosk/auth/logout";
  }
  let response = await ApiHelper.post(endPoint, {});
  // Router.replace(Constants.webUrl.login);

  // let form = document.createElement("form")
  // form.method = "POST";
  // // form.action = "/api/logout";
  // form.action = Constants.webUrl.login;
  // document.body.appendChild(form);
  // form.submit();
  // window.location.href = Constants.webUrl.login;
  // console.log(window.location);

  if (window !== undefined && window?.location) {
    window.location.replace(url);
  }
};

const ApiHelper = {
  get: async (url, params, signal = null) => {
    let Header = await getHeader();
    const usedSignal = signal?.signal || signal;

    return new Promise((resolve) => {
      let uri = baseUrl() + url + generateParams(params);
      fetch(uri, {
        method: "GET",
        credentials: "include",
        headers: {
          ...Header,
        },
        signal: usedSignal,
      })
        .then((response) => {
          return response.json();
        })
        .then((responseData) => {
          if (responseData.status === 401) {
            logOut();
          } else {
            if (responseData.status === 404) {
              return resolve({
                status: 404,
                message: "Page Not Found.",
              });
            } else {
              return resolve(responseData);
            }
          }
        })
        .catch((error) => {
          // get status
          if (error.name === "AbortError") {
            // return error
            return resolve({
              status: 205,
              message: "Aborted Request.",
              response: { results: {} },
            });
          }
          return resolve({
            status: 500,
            message: "Terjadi Kesalahan Server.",
          });
        });
    });
  },
  post: async (url, body) => {
    let Header = await getHeader();
    return new Promise((resolve) => {
      fetch(baseUrl() + url, {
        method: "POST",
        credentials: "include",
        headers: {
          ...Header,
        },
        body: JSON.stringify(body),
      })
        .then((response) => {
          return response.json();
        })
        .then((responseData) => {
          if (responseData.status === 401) {
            logOut();
          } else {
            if (responseData.status === 404) {
              return resolve({
                status: 404,
                message: "Page Not Found.",
              });
            } else {
              return resolve(responseData);
            }
          }
        })
        .catch((error) => {
          return resolve({
            status: 500,
            message: "Terjadi Kesalahan Server.",
            error,
          });
        });
    });
  },
  uploadImage: async (body) => {
    return new Promise((resolve) => {
      let uploadImageUrl = Constants.Api.apiUrlUploadImage;
      if (
        window.location.hostname &&
        (window.location.hostname.includes(Constants.baseUrlDev) ||
          window.location.hostname.includes("localhost"))
      ) {
        uploadImageUrl = Constants.Api.apiDevUrlUploadImage;
      }

      fetch(uploadImageUrl, {
        method: "POST",
        body: body,
      })
        .then((response) => {
          return response.json();
        })
        .then((responseData) => {
          return resolve(responseData);
        })
        .catch((error) => {
          return resolve({
            status: 500,
            message: "Terjadi Kesalahan Server.",
          });
        });
    });
  },
  downloadGET: async (url, params, filename, mime) => {
    return new Promise((resolve) => {
      let uri = baseUrl() + "download/" + url + generateParams(params);
      fetch(uri, {
        method: "GET",
        credentials: "include",
        headers: {},
        encoding: "binary",
        responseType: "blob",
      })
        .then(async (response) => {
          // const res2 = response.clone();
          // console.log(await res2.text());

          return response.blob();
        })
        .then((blob) => {
          const url = window.URL.createObjectURL(new Blob([blob]));
          const link = document.createElement("a");
          link.href = url;
          link.setAttribute("download", `${filename}.${mime}`);
          // 3. Append to html page
          document.body.appendChild(link);
          // 4. Force download
          link.click();
          // 5. Clean up and remove the link
          link.parentNode.removeChild(link);
          return resolve();
        })
        .catch((error) => {
          return resolve({
            status: 500,
            message: "Terjadi Kesalahan Server.",
          });
        });
    });
  },
  importPOST: async (url, formData) => {
    return new Promise((resolve) => {
      let uri = baseUrl() + "import/" + url;
      fetch(uri, {
        method: "POST",
        credentials: "include",
        headers: {
          // 'Content-Type': 'form-data'
          // 'Content-Type': 'multipart/form-data'
          // 'Content-Type': 'application/x-www-form-urlencoded',
        },
        // body: formData,
        body: JSON.stringify(formData),
      })
        .then(async (response) => {
          const res2 = response.clone();
          return response.json();
        })
        .then((responseData) => {
          if (responseData.status === 401) {
            logOut();
          } else {
            if (responseData.status === 404) {
              return resolve({
                status: 404,
                message: "Page Not Found.",
              });
            } else {
              return resolve(responseData);
            }
          }
        })
        .catch((error) => {
          return resolve({
            status: 500,
            message: "Terjadi Kesalahan Server.",
            error,
          });
        });
    });
  },
  uploadPOST: async (url, formData) => {
    return new Promise((resolve) => {
      // let uri = baseUrl() + "upload/" + url;
      let uri = baseUrl() + url;

      formData.append("_type", "upload");
      fetch(uri, {
        method: "POST",
        credentials: "include",
        headers: {
          // 'Content-Type': 'form-data'
          // 'Content-Type': 'multipart/form-data'
          // 'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: formData,
        // body: JSON.stringify(formData),
      })
        .then(async (response) => {
          const res2 = response.clone();
          return response.json();
        })
        .then((responseData) => {
          if (responseData.status === 401) {
            logOut();
          } else {
            if (responseData.status === 404) {
              return resolve({
                status: 404,
                message: "Page Not Found.",
              });
            } else {
              return resolve(responseData);
            }
          }
        })
        .catch((error) => {
          return resolve({
            status: 500,
            message: "Terjadi Kesalahan Server.",
            error,
          });
        });
    });
  },
};

export default ApiHelper;
