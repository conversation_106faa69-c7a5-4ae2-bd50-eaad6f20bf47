/**
 * @link https://github.com/exceljs/exceljs
 */

export const EXPORT_EXCEL_STYLE_NUM_FMT = {
  number: "#,##0",
  number2Decimal: "#,##0.00",
  number4Decimal: "#,##0.0000",
  currencyRp: "Rp#,##0",
};

export function numberToColumnLabel(number) {
  let label = "";
  while (number > 0) {
    let remainder = (number - 1) % 26;
    label = String.fromCharCode("A".charCodeAt(0) + remainder) + label;
    number = Math.floor((number - 1) / 26);
  }
  return label;
}

export default class ExportExcel {
  _COL_MAX_WIDTH = 150;
  _FILE_TYPE =
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8";
  _EXCEL_EXTENSION = ".xlsx";

  _ExcelJS = require("exceljs");
  _column = [];
  _colWidth = {};
  _data = [];
  _title = "";
  _subTitle = "";
  _filename = "";
  _option = {};

  _defaultStyle = {
    header: {
      border: "thin",
      alignment: {
        vertical: "middle",
        horizontal: "center",
      },
      font: {
        color: { argb: "00000000" },
        bold: true,
        size: 14,
      },
      fgColor: "DEDEDE",
    },
    column: {
      border: "thin",
      alignment: {
        vertical: "middle",
        horizontal: "left",
      },
      font: {
        color: { argb: "00000000" },
        size: 12,
      },
      fgColor: "FFFFFF",
    },
  };

  _currentRow = 1;
  _rowHeaderStart = 1;
  _rowDataStart = 1;

  _workbook = null;
  _sheet = null;
  _worksheet = null;

  constructor(
    params = {
      column: {
        key: {
          merge: {},
          style: {},
        },
      },
      header: [
        {
          startCol: "",
          column: [],
        },
      ],
      data: [
        {
          keyColumn: {
            value: "",
            merge: {},
            style: {},
          },
        },
      ],
      title: "",
      subTitle: [
        {
          value: "",
          style: {},
        },
      ],
      filename: "",
    }
  ) {
    const {
      column = [],
      header = [],
      data = [],
      title = "",
      subTitle = [],
      filename = "",
      option = {},
    } = params;

    // Column
    this._column = column;
    // Header
    this._header = header;
    // Data
    this._data = data;
    // Option
    this._option = option;

    // Format Date
    const dateTIme = new Date();
    const formatter = new Intl.DateTimeFormat("id-ID", {
      dateStyle: "full",
      timeStyle: "short",
    });
    const formattedDateTime = formatter.format(dateTIme);

    // Title
    this._title = title;
    subTitle.unshift({
      value: `Tanggal Ekspor: ${formattedDateTime}`,
    });
    this._subTitle = subTitle;

    // File Name
    this._filename = filename;

    // Create Work Book
    this._workbook = new this._ExcelJS.Workbook();
    // Create Work Sheet
    this._sheet = this._workbook.addWorksheet(this._title.substring(0, 30), {
      views: [{ showGridLines: false }],
    });
    // Set Work Sheet
    this._worksheet = this._workbook.getWorksheet(this._sheet.name);

    // Set Option Freeze
    if (this._option.freeze !== undefined) {
      let colLabel = this.numberToColumnLabel(this._option.freeze.startCol);
      let freezeCoordinate = `${colLabel}${this._option.freeze.startRow}`;
      this._worksheet.views = [
        {
          state: "frozen",
          xSplit: this._option.freeze.startCol,
          ySplit: this._option.freeze.startRow,
          topLeftCell: freezeCoordinate,
        },
      ];
    }
  }

  writeTitleAndSubTitle() {
    // write title
    this._worksheet.insertRow(this._currentRow, [this._title]);
    this._currentRow++;

    // write sub title
    if (this._subTitle.length > 0) {
      this._subTitle.forEach((item) => {
        if (item?.value) {
          this._worksheet.insertRow(this._currentRow, [item.value]);
          if (item.style !== undefined && Object.keys(item.style).length > 0) {
            this._worksheet.getRow(this._currentRow).getCell(1).style =
              item.style;
          }
          this._currentRow++;
        }
      });
    }

    // write empty space
    this._worksheet.insertRow(this._currentRow, []);
    this._currentRow++;

    this._rowHeaderStart = this._currentRow;
    this._rowDataStart = this._header.length + 1;
  }

  writeHeader() {
    if (this._header.length > 0) {
      for (let i = 0; i < this._header.length; i++) {
        const header = this._header[i];

        let colNumber = this.columnLabelToNumber(header.startCol);
        for (let h = 0; h < header.column.length; h++) {
          const item = header.column[h];

          const colLabel = this.numberToColumnLabel(colNumber);
          const cellLabel = `${colLabel}${this._currentRow}`;

          let cell = this._worksheet.getCell(cellLabel);

          // Already Merged Skip Write & Styling
          if (cell._mergeCount === 0) {
            // Write Content
            cell.value = item.label;

            // Merge Header
            if (
              item.merge !== undefined &&
              Object.keys(item.merge).length > 0
            ) {
              this._worksheet.mergeCells(
                `${item.merge.mergeStart}:${item.merge.mergeEnd}`
              );
              colNumber += item.merge.mergeCol - 1;
            }

            cell = this._worksheet.getCell(cellLabel);

            // Start Styling Header
            if (
              item.style !== undefined &&
              Object.keys(item.style).length > 0
            ) {
              // Set Border
              if (item.style.border !== undefined) {
                // Use Custom Border
                cell.border = {
                  top: { style: item.style.border },
                  left: { style: item.style.border },
                  bottom: { style: item.style.border },
                  right: { style: item.style.border },
                };
              } else {
                // Use Default Border
                cell.border = {
                  top: { style: this._defaultStyle.header.border },
                  left: { style: this._defaultStyle.header.border },
                  bottom: { style: this._defaultStyle.header.border },
                  right: { style: this._defaultStyle.header.border },
                };
              }

              // Set Alignment
              if (item.style.alignment !== undefined) {
                // Use Custom Alignment
                cell.alignment = item.style.alignment;
              } else {
                // Use Default Alignment
                cell.alignment = this._defaultStyle.header.alignment;
              }

              // Set Font
              if (item.style.font !== undefined) {
                // Use Custom Font
                cell.font = item.style.font;
              } else {
                // Use Default Font
                cell.font = this._defaultStyle.header.font;
              }

              // Set Background Color
              if (item.style.fgColor !== undefined) {
                // Use Custom Background
                cell.fill = {
                  fgColor: { argb: item.style.fgColor },
                };
              } else {
                // Use Default Background
                cell.fill = {
                  fgColor: { argb: this._defaultStyle.header.fgColor },
                };
              }
            } else {
              // Use Default Border
              cell.border = {
                top: { style: this._defaultStyle.header.border },
                left: { style: this._defaultStyle.header.border },
                bottom: { style: this._defaultStyle.header.border },
                right: { style: this._defaultStyle.header.border },
              };

              // Use Default Alignment
              cell.alignment = this._defaultStyle.header.alignment;

              // Use Default Font
              cell.font = this._defaultStyle.header.font;

              // Use Default Background
              cell.fill = {
                type: "pattern",
                pattern: "solid",
                fgColor: { argb: this._defaultStyle.header.fgColor },
              };
            }

            cell._column.width = item.label.length * 2;
          }

          colNumber++;
        }
        this._currentRow++;
      }
    }
  }

  writeData() {
    if (this._data.length > 0) {
      let number = 1;
      for (let i = 0; i < this._data.length; i++) {
        const data = this._data[i];
        let colNumber = 1;
        for (const colKey in this._column) {
          const column = this._column[colKey];

          const colLabel = this.numberToColumnLabel(colNumber);

          // Set Curent Cell
          const cellLabel = `${colLabel}${this._currentRow}`;
          let cell = this._worksheet.getCell(cellLabel);

          // Write Content
          if (colKey === "no") {
            // Number Column
            cell.value = number;

            // Set Default Border
            cell.border = {
              top: { style: this._defaultStyle.column.border },
              left: { style: this._defaultStyle.column.border },
              bottom: { style: this._defaultStyle.column.border },
              right: { style: this._defaultStyle.column.border },
            };

            // Use Default Alignment
            cell.alignment = {
              vertical: "middle",
              horizontal: "left",
            };

            // Use Default Font
            cell.font = this._defaultStyle.column.font;

            // Use Default Background
            cell.fill = {
              type: "pattern",
              pattern: "solid",
              fgColor: { argb: this._defaultStyle.column.fgColor },
            };

            // Width Calc
            if (this._colWidth[colKey] !== undefined) {
              let colWidth = data[colKey].value.length * 1.5;
              if (colWidth > this._colWidth[colKey]) {
                // Set Limit Width
                if (colWidth >= this._COL_MAX_WIDTH) {
                  colWidth = this._COL_MAX_WIDTH;
                }

                // Set Column Width
                if (colWidth > cell._column.width) {
                  this._colWidth[colKey] = colWidth;
                  cell._column.width = colWidth;
                }
              }
            } else {
              let colWidth = data[colKey].value.length * 1.5;

              // Set Limit Width
              if (colWidth >= this._COL_MAX_WIDTH) {
                colWidth = this._COL_MAX_WIDTH;
              }

              // Set Column Width
              if (colWidth > cell._column.width) {
                this._colWidth[colKey] = colWidth;
                cell._column.width = colWidth;
              }
            }
            number++;
          } else {
            // Data Column
            if (data[colKey] !== undefined) {
              if (data[colKey].type !== undefined) {
                if (data[colKey].type === "datetime") {
                  if (data[colKey].value) {
                    const valueData = new Date(data[colKey].value);
                    cell.value = new Date(
                      valueData.getTime() -
                        valueData.getTimezoneOffset() * 60_000
                    );
                    // cell.numFmt = 'd/mm/yyyy h:mm:ss'
                    cell.numFmt = "[$-id]dddd, d mmmm yyyy h:mm:ss";
                  } else {
                    cell.value = "-";
                  }
                } else if (data[colKey].type === "date") {
                  if (data[colKey].value) {
                    const valueData = new Date(data[colKey].value);
                    cell.value = new Date(
                      valueData.getTime() -
                        valueData.getTimezoneOffset() * 60_000
                    );
                  } else {
                    cell.value = "-";
                  }
                  // cell.numFmt = 'd/mm/yyyy'
                  cell.numFmt = "[$-id]dddd, d mmmm yyyy";
                } else if (data[colKey].type === "numeric") {
                  cell.value = Number(data[colKey].value);
                  cell.numFmt = "#,##0";
                  if (data[colKey].numFmt !== undefined) {
                    cell.numFmt = data[colKey].numFmt;
                  }                  
                }
              } else {
                cell.value = data[colKey].value;
              }

              // Already Merged Skip Styling
              if (cell._mergeCount === 0) {
                // Merge
                if (
                  data[colKey].merge !== undefined &&
                  Object.keys(data[colKey].merge).length > 0
                ) {
                  this._worksheet.mergeCells(
                    `${data[colKey].merge.mergeStart}:${data[colKey].merge.mergeEnd}`
                  );
                  if (data[colKey].merge.mergeCol !== undefined) {
                    colNumber += data[colKey].merge.mergeCol - 1;
                  }
                }

                // Start Styling Header
                if (
                  data[colKey].style !== undefined &&
                  Object.keys(data[colKey].style).length > 0
                ) {
                  // Set Border
                  if (data[colKey].style.border !== undefined) {
                    // Use Custom Border
                    cell.border = {
                      top: { style: data[colKey].style.border },
                      left: { style: data[colKey].style.border },
                      bottom: { style: data[colKey].style.border },
                      right: { style: data[colKey].style.border },
                    };
                  } else {
                    // Use Default Border
                    cell.border = {
                      top: { style: this._defaultStyle.column.border },
                      left: { style: this._defaultStyle.column.border },
                      bottom: { style: this._defaultStyle.column.border },
                      right: { style: this._defaultStyle.column.border },
                    };
                  }

                  // Set Alignment
                  if (data[colKey].style.alignment !== undefined) {
                    // Use Custom Alignment
                    cell.alignment = data[colKey].style.alignment;
                  } else {
                    if (data[colKey].type !== undefined) {
                      if (data[colKey].type === "numeric") {
                        cell.alignment = {
                          vertical: "middle",
                          horizontal: "right",
                        };
                      }
                    } else {
                      // Use Default Alignment
                      cell.alignment = this._defaultStyle.column.alignment;
                    }
                  }

                  // Set Font
                  if (data[colKey].style.font !== undefined) {
                    // Use Custom Font
                    cell.font = data[colKey].style.font;
                  } else {
                    // Use Default Font
                    cell.font = this._defaultStyle.column.font;
                  }

                  // Set Background Color
                  if (data[colKey].style.fgColor !== undefined) {
                    // Use Custom Background
                    cell.fill = {
                      type: "pattern",
                      pattern: "solid",
                      fgColor: { argb: data[colKey].style.fgColor },
                    };
                  } else {
                    // Use Default Background
                    cell.fill = {
                      type: "pattern",
                      pattern: "solid",
                      fgColor: { argb: this._defaultStyle.column.fgColor },
                    };
                  }
                } else {
                  // Use Default Border
                  cell.border = {
                    top: { style: this._defaultStyle.column.border },
                    left: { style: this._defaultStyle.column.border },
                    bottom: { style: this._defaultStyle.column.border },
                    right: { style: this._defaultStyle.column.border },
                  };

                  // Use Default Alignment
                  if (data[colKey].type !== undefined) {
                    if (data[colKey].type === "numeric") {
                      cell.alignment = {
                        vertical: "middle",
                        horizontal: "right",
                      };
                    } else {
                      cell.alignment = this._defaultStyle.column.alignment;
                    }
                  } else {
                    // Use Default Alignment
                    cell.alignment = this._defaultStyle.column.alignment;
                  }

                  // Use Default Font
                  cell.font = this._defaultStyle.column.font;

                  // Use Default Background
                  cell.fill = {
                    type: "pattern",
                    pattern: "solid",
                    fgColor: { argb: this._defaultStyle.column.fgColor },
                  };
                }

                // Width Calc
                if (this._colWidth[colKey] !== undefined) {
                  let colWidth = data[colKey].value.length * 1.25;
                  if (colWidth > this._colWidth[colKey]) {
                    // Set Limit Width
                    if (colWidth >= this._COL_MAX_WIDTH) {
                      colWidth = this._COL_MAX_WIDTH;
                    }

                    // Set Column Width
                    if (colWidth > cell._column.width) {
                      this._colWidth[colKey] = colWidth;
                      cell._column.width = colWidth;
                    }
                  }
                } else {
                  let colWidth = data[colKey].value.length * 1.25;

                  // Set Limit Width
                  if (colWidth >= this._COL_MAX_WIDTH) {
                    colWidth = this._COL_MAX_WIDTH;
                  }

                  // Set Column Width
                  if (colWidth > cell._column.width) {
                    this._colWidth[colKey] = colWidth;
                    cell._column.width = colWidth;
                  }
                }
              }
            } else {
              // Already Merged Skip Styling
              if (cell._mergeCount === 0) {
                // Empty Cell
                cell.value = "-";

                // Use Default Border
                cell.border = {
                  top: { style: this._defaultStyle.column.border },
                  left: { style: this._defaultStyle.column.border },
                  bottom: { style: this._defaultStyle.column.border },
                  right: { style: this._defaultStyle.column.border },
                };

                // Use Default Alignment
                cell.alignment = this._defaultStyle.column.alignment;

                // Use Default Font
                cell.font = this._defaultStyle.column.font;

                // Use Default Background
                cell.fill = {
                  type: "pattern",
                  pattern: "solid",
                  fgColor: { argb: this._defaultStyle.column.fgColor },
                };
              }
            }
          }          

          colNumber++;
        }
        this._currentRow++;
      }
    }
  }

  async downloadFile() {
    this.writeTitleAndSubTitle();
    this.writeHeader();
    this.writeData();

    // download file
    const buffer = await this._workbook.xlsx.writeBuffer();
    const blob = new Blob([buffer], { type: this._FILE_TYPE });
    if (navigator?.msSaveBlog) {
      navigator.msSaveBlog(blob, `${this._filename}${this._EXCEL_EXTENSION}`);
    } else {
      var a = document.createElement("a");
      document.body.appendChild(a);
      a.style = "display: none";
      var url = window.URL.createObjectURL(blob);
      a.href = url;
      a.download = this._filename;
      a.click();
      window.URL.revokeObjectURL(url);
    }
  }

  numberToColumnLabel(number) {
    let label = "";
    while (number > 0) {
      let remainder = (number - 1) % 26;
      label = String.fromCharCode("A".charCodeAt(0) + remainder) + label;
      number = Math.floor((number - 1) / 26);
    }
    return label;
  }

  columnLabelToNumber(label) {
    let number = 0;
    for (let i = 0; i < label.length; i++) {
      number = number * 26 + (label.charCodeAt(i) - "A".charCodeAt(0) + 1);
    }
    return number;
  }
}
