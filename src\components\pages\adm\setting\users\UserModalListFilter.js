import React from "react";
import { Modal, Box, Checkbox, Chip, Radio } from "@mui/material";
import Loading from "@/components/modal/Loading";
import ApiHelper from "@/utils/ApiHelper";
import CommonHelper from "@/utils/CommonHelper";
import MySnackbar from "@/components/MySnackbar";
import InputAutoComplete from "@/components/libs/InputAutoComplete2";
import Input from "@/components/libs/Input";
import FilterHelper, {
  FILTER_TYPE_STRING,
  FILTER_COMP_EQUAL,
} from "@/utils/FilterHelper";

const DEFAULT_INPUTS_FILTER = {
  role: null,
  type: null,
};

class UserModalListFilter extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showDialog: false,
      formType: "filter",
      formData: null,
      formIndex: -1,

      inputs: structuredClone(DEFAULT_INPUTS_FILTER),
      errors: {},
    };
  }

  // ====================================================================================
  // ========== INITIALIZE, GET DATA ====================================================
  // ====================================================================================
  onShowDialog = (formType, formData = null, formIndex = -1) => {
    let inputs = structuredClone(DEFAULT_INPUTS_FILTER);
    if (formData?.inputs) {
      inputs = { ...structuredClone(inputs), ...formData.inputs };
    }

    this.setState(
      {
        showDialog: true,
        formType,
        formData,
        formIndex,
        inputs,
      },
      () => {
        if (inputs.role) {
          setTimeout(() => {
            this.ref_InputRole.setDefaultInputs(inputs.role.name);
          }, 250);
        }
      }
    );
  };
  onCloseDialog = () => {
    this.setState({
      showDialog: false,
      formType: "filter",
      formData: null,
      formIndex: -1,

      inputs: structuredClone(DEFAULT_INPUTS_FILTER),
      errors: {},
    });
  };
  onTextInputListeners = (val, name) => {
    this.setState((prevState) => ({
      ...prevState,
      inputs: { ...prevState.inputs, [name]: val },
    }));
  };
  onTextErrorListeners = (error, input) => {
    this.setState((prevState) => ({
      ...prevState,
      errors: { ...prevState.errors, [input]: error },
    }));
  };

  onSubmitFilter = () => {
    const { onFilter } = this.props;
    if (onFilter) {
      onFilter(this.state.inputs);
    }
    this.onCloseDialog();
  };

  // ====================================================================================
  // ========== RENDER ==================================================================
  // ====================================================================================
  render() {
    let addModalClass = "dialog";

    return (
      <>
        <Modal open={this.state.showDialog} onClose={this.onCloseDialog}>
          <Box
            sx={{
              flex: 1,
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              display: "flex",
              width: "100wh",
              height: "100vh",
            }}
          >
            <div className={`modal-content ${addModalClass}`}>
              <div className="modal-header">{this.renderHeader()}</div>
              <div className="modal-body">{this.renderBody()}</div>
              <div className="modal-footer">{this.renderFooter()}</div>
            </div>
          </Box>
        </Modal>

        {/* Loading dialog */}
        <Loading ref={(value) => (this.ref_Loading = value)} />
        <MySnackbar ref={(value) => (this.ref_MySnackbar = value)} />
      </>
    );
  }

  renderHeader() {
    return (
      <>
        <div className="title">Filter Data</div>
        <span className="close" onClick={() => this.onCloseDialog()}>
          &times;
        </span>
      </>
    );
  }

  renderBody() {
    return <>{this.renderForm()}</>;
  }

  renderFooter() {
    return (
      <>
        <button
          className="button"
          onClick={() => {
            this.onSubmitFilter();
          }}
        >
          <i className="ph ph-bold ph-check-circle"></i>
          <span>Filter</span>
        </button>
        <button className="button cancel" onClick={() => this.onCloseDialog()}>
          <i className="ph ph-bold ph-x-circle"></i>
          <span>{this.state.formType === "info" ? "Tutup" : "Batal"}</span>
        </button>
      </>
    );
  }

  renderForm() {
    return (
      <>
        <div className="flex-rows no-right">
          <div className="row no-border">
            <div className="input-form">
              <InputAutoComplete
                ref={(value) => (this.ref_InputRole = value)}
                label="Hak Akses"
                placeholder="Tuliskan nama hak akses"
                inputName="modal-list-user-filter-input-role"
                dataUrl="kiosk/admin/user/role/data"
                dataParams={{
                  ...(this.state.formData?.dataParams ?? {}),
                  sort: "name",
                }}
                displayTitle="name"
                onChange={(item, type) => {
                  this.onTextInputListeners(item, "role");
                }}
                error={
                  this.state.errors.role !== undefined &&
                  this.state.errors.role !== null
                    ? true
                    : false
                }
                onFocus={() => this.onTextErrorListeners(null, "role")}
              />
            </div>
            <div className="input-form">
              <Input
                label="Tipe Pengguna"
                inputType="select"
                value={this.state.inputs.type}
                defaultValue={this.state.inputs.type}
                onChange={(event) => {
                  if (
                    event.target !== undefined &&
                    event.target.value !== undefined
                  ) {
                    this.onTextInputListeners(event.target.value, "type");
                  }
                }}
                error={
                  this.state.errors.type !== undefined &&
                  this.state.errors.type !== null
                    ? true
                    : false
                }
                onFocus={() => this.onTextErrorListeners(null, "type")}
                options={[
                  { value: "", label: "Pilih Tipe" },
                  { value: "kiosk", label: "Kiosk" },
                  { value: "admin", label: "Admin" },
                ]}
              />
            </div>
          </div>
        </div>
      </>
    );
  }
}

export default UserModalListFilter;
