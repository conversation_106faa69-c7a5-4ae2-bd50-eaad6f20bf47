class PageContextHelper {
  #context;
  // constructor(state, setState) {
  //   this.#state = state;
  //   this.#setState = setState;
  // }
  constructor(context) {
    this.#context = context;
  }

  onLoading = (show, timout = 300) => {
    if (this.#context?.ref_LoadingDialog) {
      if (show) {
        this.#context.ref_LoadingDialog.onShowDialog();
        return;
      } else {
        setTimeout(() => {
          this.#context.ref_LoadingDialog.onCloseDialog();
        }, timout);
      }
    }
  };

  onNotify = (messageSnackbar, severitySnackbar) => {
    if (this.#context?.ref_MySnackbar) {
      this.#context?.ref_MySnackbar.onNotify(messageSnackbar, severitySnackbar);
    }
  };

  onConfirm = (data, onConfirm) => {
    if (this.#context?.ref_ConfirmationModal?.onSetData) {
      this.#context?.ref_ConfirmationModal?.onSetData(data, onConfirm);
    }
  };
}

export default PageContextHelper;
