/*
Created by esoda
Created on Apr, 2025
Contact esoda.id
-- Styled for DatePicker.js libs components
-- Styled for Input.js libs components
-- Styled for FileUploadSingle.js libs components
-- Styled for ImageUploadSingle.js libs components
-- Styled for InputAutoComplete.js libs components
USE: Import on _app.js
*/

.form-input,
.form-image,
.form-radio {
    width: 100%;
}

.form-input.wd-50,
.form-image.wd-50 {
    width: 50%;
}

.form-input.wd-50.rows,
.form-image.wd-50.rows {
    padding-right: .75rem;
}

.form-input label,
.form-image .title,
.form-radio label {
    color: var(--input-label-color);
    font-weight: 600;
    font-size: .95rem;
}

.form-input .inputs,
.form-image .inputs {
    flex-grow: 1;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    border: solid 1px var(--input-border-color);
    border-radius: var(--input-border-radius);
    overflow: hidden;
    margin-top: .3rem;
}

.form-radio .inputs {
    margin-top: .1rem;
    cursor: pointer;
    display: grid;
    grid-template-columns: 1em auto;
    gap: 0.5em;
    font-size: 1rem;
}

.form-radio .inputs input[type="radio"] {
    cursor: pointer;
    appearance: none;
    background-color: #fff;
    margin: 0;
    font: inherit;
    color: var(--text-foreign-color);
    width: 1rem;
    height: 1rem;
    border: 1px solid currentColor;
    border-radius: 50%;
    transform: translateY(.23em);
    -webkit-transform: translateY(.23em);
    -moz-transform: translateY(.23em);
    -ms-transform: translateY(.23em);
    -o-transform: translateY(.23em);
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    display: grid;
    place-content: center;
}

.form-radio .inputs input[type="radio"]::before {
    content: "";
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    transform: scale(0);
    transition: 120ms transform ease-in-out;
    box-shadow: inset 1em 1em var(--third-color);
    background-color: CanvasText;
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    -o-transform: scale(0);
    -webkit-transition: 120ms transform ease-in-out;
    -moz-transition: 120ms transform ease-in-out;
    -ms-transition: 120ms transform ease-in-out;
    -o-transition: 120ms transform ease-in-out;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
}

.form-radio .inputs input[type="radio"]:checked::before {
    transform: scale(1);
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
}

.form-input:not(:first-child),
.form-radio:not(:first-child) {
    margin-top: 1rem;
}

.form-input .inputs:has(input:hover),
.form-input .inputs:has(textarea:hover),
.form-input .inputs:has(select:hover),
.form-image .inputs:has(input:hover) {
    border-color: var(--input-border-hover-color);
}

.form-input .inputs:has(input:focus),
.form-input .inputs:has(textarea:focus),
.form-input .inputs:has(select:focus),
.form-image .inputs:has(input:focus) {
    border-color: var(--input-border-focus-color);
}

.form-input .inputs:has(input:hover)>.icon i,
.form-input .inputs:has(select:hover)>.icon i,
.form-image .inputs:has(input:hover) {
    color: var(--input-border-hover-color);
}

.form-input .inputs input:disabled,
.form-input .inputs textarea:disabled,
.form-input .inputs select:disabled,
.form-input .inputs:has(input:disabled)>.icon,
.form-input .inputs:has(select:disabled)>.icon,
.form-input .inputs:has(input:disabled)>.symbol,
.form-input .inputs:has(select:disabled)>.symbol {
    background-color: var(--input-disabled-color);
}

.form-input .inputs:has(input:focus)>.icon i,
.form-input .inputs:has(select:focus)>.icon i,
.form-image .inputs:has(input:focus) {
    color: var(--input-border-focus-color);
}

.form-input .inputs .icon,
.form-input .inputs input,
.form-input .inputs select,
.form-input .inputs symbol,
.form-image .inputs .icon,
.form-image .inputs input {
    height: 40px;
    border: none;
    border-radius: 0;
    background-color: #FFFFFF;
}

.form-input .inputs .icon,
.form-input .inputs .symbol,
.form-image .inputs .icon,
.form-image .inputs input {
    width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.form-input .inputs .symbol {
    width: fit-content;
    height: 40px;
    padding: 0 .8rem;
    color: #b7b7b7;
    font-size: .8rem;
    font-weight: 600;
}

.form-input .inputs .symbol i {
    font-size: 1.2rem;
}

.form-input .inputs .icon i {
    font-size: 1.1rem;
    color: #808080;
}

.form-input .inputs input,
.form-input .inputs textarea,
.form-input .inputs select,
.form-image .inputs input {
    flex-grow: 1;
    color: #2f3640;
    font-size: 1rem;
    border: none;
    outline: none;
}

.form-input .inputs textarea {
    padding: .5rem .8rem;
    resize: none;
    height: 130px;
}

.form-input .inputs.no-icon>input {
    padding-left: .8rem;
}

.form-input .inputs input,
.form-input .inputs select,
.form-image .inputs input {
    padding-left: .6rem;
    padding-right: .6rem;
}

.form-input .inputs input::placeholder,
.form-input .inputs textarea::placeholder,
.form-image .inputs input::placeholder {
    color: #c1c1c1;
}

/* Custom styles for select element */
.form-input .inputs select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23808080' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.6rem center;
    background-size: 1em;
    padding-right: 2rem;
}

.form-input .inputs.error>.symbol {
    color: #e74c3c;
}

.form-input .inputs.error,
.form-image .inputs.error {
    border-color: #e74c3c;
}

.form-input:has(.inputs.error) .placeholder .note,
.form-image:has(.inputs.error) .placeholder .note {
    color: #e74c3c;
}

.form-input:has(.inputs.error) .placeholder .note::before,
.form-image:has(.inputs.error) .placeholder .note::before {
    content: "\2A\2A";
    margin-right: 3px;
}

.form-input .inputs button,
.form-image .inputs label {
    cursor: pointer;
    width: fit-content;
    background-color: var(--input-button-color) !important;
    padding: 0 .8rem;
    border-radius: 0 !important;
    border: none !important;
    font-size: .95rem !important;
    height: 40px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
}

.form-input:has(.data-list) {
    position: relative;
    width: 100%;
}

.form-input .data-list {
    display: none;
    position: absolute;
    background-color: white;
    border: solid 1px #e8e8e8;
    width: 100%;
    max-height: 350px;
    overflow-y: auto;
    z-index: 9999; /* Increased z-index to ensure it appears above modal content */
}

.form-input .data-list.show {
    display: block;
}

/* Specific styling for data-list inside modals */
.modal-body .form-input .data-list,
.modal-content .form-input .data-list {
    z-index: 9999;
}

.form-input .data-list .items:not(:last-child) {
    border-bottom: solid 1px #e8e8e8;
}

.form-input .data-list .items {
    padding: .5rem 1rem;
    color: #7e7e7e;
    cursor: pointer;
}

.form-input .data-list .items:hover {
    color: var(--text-primary-color);
}

.form-input .data-list .items .title {
    font-size: 1rem;
}

.form-input .data-list .items .subtitle {
    font-size: .8rem;
    line-height: 1;
}

/* InputAutoComplete2 Pagination Styles */
.form-input .data-list .loading-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    color: #7a7979;
    font-size: 0.9rem;
}

.form-input .data-list .loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #16a085;
    border-radius: 50%;
    margin-right: 10px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.form-input .data-list .load-more {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    background-color: #f9f9f9;
    color: #16a085;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s;
    border-top: 1px solid #eeeeee;
}

.form-input .data-list .load-more:hover {
    background-color: #f0f0f0;
}

.form-input:has(.date-picker) {
    position: relative;
    width: 100%;
}

.form-input .date-picker {
    display: none;
    position: absolute;
    background-color: white;
    border: solid 1px #e8e8e8;
    border-top: none;
    width: 282px;
    min-width: 282px;
    max-width: 100%;
    overflow: hidden;
    z-index: 1;
}

.form-input .date-picker.show {
    display: block;
}

.form-input .date-picker .month {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: .7rem;
    background-color: #fcfcfc;
}

.form-input .date-picker .month .value {
    flex-grow: 1;
    text-align: center;
    font-weight: 600;
    color: #1b222c;
}

.form-input .date-picker .month .selector {
    cursor: pointer;
    width: 26px;
    height: 26px;
    background-color: #eeeeee;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -ms-border-radius: 3px;
    -o-border-radius: 3px;
    color: #b9b9b9;
}

.form-input .date-picker .month .selector.active {
    color: white;
    background-color: #1b222c;
}

.form-input .date-picker .weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    border-bottom: solid 1px rgb(236, 236, 236);
    border-top: solid 1px rgb(236, 236, 236);
    background-color: #f0f0f0;
}

.form-input .date-picker .weekdays .items {
    width: 40px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: .75rem;
    color: #1b222c;
    font-weight: 600;
    text-transform: uppercase;
}

.form-input .date-picker .weekdays .items:last-child {
    color: #9d1a21;
}

.form-input .date-picker .date {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
}

.form-input .date-picker .date .items {
    text-align: center;
    font-size: .9rem;
    cursor: pointer;
    color: #1b222c;
    width: 40px;
    height: 40px;
    justify-self: center;
    display: flex;
    align-items: center;
    justify-content: center;
    border: solid 1px rgb(251, 251, 251);
    line-height: 1;
}

.form-input .date-picker .date .items:nth-child(7n) {
    color: #9d1a21;
}

.form-input .date-picker .date .items.selected {
    background-color: #9d1a21 !important;
    color: white !important;
    font-size: 1.1rem;
    font-weight: 600;
    border-color: #9d1a21;
}

.form-input .date-picker .date .items:hover {
    border-color: #9d1a21;
}

.form-input .date-picker .date .items.empty {
    border-color: rgb(245 245 245);
    background-color: rgb(245 245 245);
}

.form-input .date-picker .date:has(.today) .items {
    position: relative;
}

.form-input .date-picker .date .today {
    position: absolute;
    width: 4px;
    height: 4px;
    background-color: #9d1a21;
    bottom: 5px;
    left: 45%;
    border-radius: 4px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    -ms-border-radius: 4px;
    -o-border-radius: 4px;
}

.form-input .date-picker .actions {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    border-top: solid 1px rgb(244, 244, 244);
    padding: .5rem .7rem;
    background-color: #fcfcfc;
}

.form-input .date-picker .actions .clear,
.form-input .date-picker .actions .today {
    cursor: pointer;
    font-size: .8rem;
    padding: .3rem .6rem;
    background-color: #1b222c;
    border: solid 1px #1b222c;
    color: white;
    font-weight: 600;
    border-radius: 2px;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    -ms-border-radius: 2px;
    -o-border-radius: 2px;
    line-height: 1;
    text-transform: uppercase;
}

.form-input .date-picker .actions .clear {
    background-color: #fcfcfc;
    color: #1b222c;
    border-color: #7d7d7d;
}

.form-input .inputs button i,
.form-image .inputs label i {
    margin-right: .5rem;
}

.form-input .inputs:has(input:hover)>button,
.form-image .inputs:has(input:hover)>label {
    background-color: var(--input-border-hover-color) !important;
    color: #fff !important;
}

.form-input .inputs:has(input:focus)>button,
.form-image .inputs:has(input:focus)>label {
    background-color: var(--input-border-focus-color) !important;
    color: #fff !important;
}

.form-input .inputs:has(input:focus)>button i,
.form-image .inputs:has(input:focus)>label i,
.form-input .inputs:has(input:hover)>button i,
.form-image .inputs:has(input:hover)>label i {
    color: #fff !important;
}

.form-input .placeholder,
.form-image .placeholder {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding-top: .2rem;
}

.form-input .placeholder .required,
.form-input .placeholder .optional,
.form-input .placeholder .read-only,
.form-image .placeholder .required,
.form-image .placeholder .optional,
.form-image .placeholder .read-only,
.form-input .placeholder .note,
.form-image .placeholder .note {
    font-size: .7rem;
    color: #aaaaaa;
    text-transform: capitalize;
}

.form-input .placeholder .required,
.form-input .placeholder .optional,
.form-input .placeholder .read-only,
.form-image .placeholder .required,
.form-image .placeholder .optional,
.form-image .placeholder .read-only {
    font-weight: 600;
    flex-grow: 1;
}

.form-input .placeholder .required,
.form-image .placeholder .required {
    color: #e74c3c;
}

.form-input .placeholder .optional,
.form-image .placeholder .optional,
.form-input .inputs .symbol.valid {
    color: #27ae60;
}

.form-input .placeholder .read-only,
.form-image .placeholder .read-only {
    color: #3498db;
}

.form-input .placeholder .note b,
.form-image .placeholder .note b {
    font-size: .7rem;
    color: #808080;
}

.form-image .image {
    width: 100%;
    height: 240px;
    border: solid 1px #e6e6e6;
    border-radius: 3px;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    overflow: hidden;
    background-color: white;
}

.form-image .image img {
    width: 100%;
    height: 100%;
    object-fit: scale-down;
    padding: .5rem;
}

.form-image .image .file-preview {
    width: 100%;
    height: 100%;
}

.form-image .inputs {
    margin-top: -2px;
}

.form-image .title {
    color: var(--input-label-color);
    font-weight: 600;
    margin-bottom: .3rem;
}

.form-image .inputs .copy {
    background-color: #e6e6e6;
    border: none;
    height: 40px;
    width: 40px;
    cursor: pointer;
    color: #333333;
    border-radius: 0;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    -ms-border-radius: 0;
    -o-border-radius: 0;
    border-right: solid 1px #e3e3e3;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
}

.form-image .inputs .copy:hover {
    background-color: #d8d8d8;
}

.form-image .inputs .copy i {
    margin-right: 0;
}

@media only screen and (max-width: 720px) {
    .form-input.wd-50,
    .form-image.wd-50 {
        width: 100%;
    }

    .form-input.wd-50.rows,
    .form-image.wd-50.rows {
        padding-right: 0;
    }
}