/*
Created by esoda
Created on Nov, 2024
Contact esoda.id
*/

import React from "react";
import { Modal, Box } from "@mui/material";
import Loading from "@/components/modal/Loading";

// formData: {
//  onConfirmed
//  activeType
//  render
//  text:{
//      title,
//      action,
//      info,
//      yes
//  }
// }
export default class ModalConfirmation extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showDialog: false,
      formType: "",
      formData: null,
      formIndex: -1,
    };
  }

  // ====================================================================================
  // ========== INITIALIZE, GET DATA ====================================================
  // ====================================================================================
  onShowDialog = (formType, formData = null, formIndex = -1) => {
    this.setState({
      formType,
      formData,
      formIndex,
      showDialog: true,
    });
  };
  onCloseDialog = () => {
    this.setState({
      showDialog: false,
      formType: "",
      formData: null,
      formIndex: -1,
    });
  };

  // ====================================================================================
  // ========== ACTION LISTENERS ========================================================
  // ====================================================================================
  onValidateListeners = () => {
    this.actOnConfirmListeners();
  };
  actOnConfirmListeners = () => {
    if (this.state.formData?.onConfirmed) {
      this.state.formData?.onConfirmed(
        this.state.formType,
        this.state.formData,
        this.state.formIndex
      );
    }
  };

  // ====================================================================================
  // ========== RENDER SECTIONS =========================================================
  // ====================================================================================
  render() {
    const { classBody = "", styleBody = {} } = this.state.formData || {};

    let addModalClass = "dialog";
    return (
      <>
        <Modal open={this.state.showDialog} onClose={this.onCloseDialog}>
          <Box
            sx={{
              flex: 1,
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              display: "flex",
              width: "100wh",
              height: "100vh",
            }}
          >
            <div className={`modal-content ${addModalClass}`}>
              <div className="modal-header">{this.renderHeader()}</div>
              <div className={`modal-body ${classBody}`} style={styleBody}>
                {this.renderBody()}
              </div>
              <div className="modal-footer">
                {this.renderFooter()}
                <button
                  className="button cancel"
                  onClick={() => this.onCloseDialog()}
                >
                  <i className="ph ph-bold ph-x-circle"></i>
                  <span>
                    {this.state.formType === "info" ? "Tutup" : "Batal"}
                  </span>
                </button>
              </div>
            </div>
          </Box>
        </Modal>

        {/* Loading dialog */}
        <Loading ref={(value) => (this.ref_Loading = value)} />
      </>
    );
  }

  renderHeader() {
    const { formData } = this.state;

    return (
      <>
        <div className="title">
          {this.state.formType === "delete" &&
            `Hapus Data ${formData.text.title}`}
          {this.state.formType === "activated" &&
            `${formData.text.action} Data ${formData.text.title}`}
          {this.state.formType === "custom" && formData.text.title}
        </div>
        <span className="close" onClick={() => this.onCloseDialog()}>
          &times;
        </span>
      </>
    );
  }

  renderBody() {
    const { formData } = this.state;

    if (formData?.render) {
      return <>{formData.render(this.state.formData)}</>;
    }

    return (
      <>
        {(this.state.formType === "delete" ||
          this.state.formType === "activated") && (
          <div style={{ textAlign: "center" }}>
            Apakah Anda yakin akan {formData.text.action} data
            <br />
            <b>{formData.text.info}</b>?
          </div>
        )}
        {this.state.formType === "custom" && (
          <div style={{ textAlign: "center" }}>{formData.text.info}</div>
        )}
      </>
    );
  }

  renderFooter() {
    return (
      <>
        <button
          className={`button ${this.state.formType === "edit" && "warning"} ${
            this.state.formType === "delete" && "danger"
          }`}
          onClick={() => {
            this.onValidateListeners();
          }}
        >
          {this.state.formType === "delete" && (
            <i className="ph ph-bold ph-trash-simple"></i>
          )}
          {this.state.formType === "activated" && (
            <i className="ph ph-bold ph-check-circle"></i>
          )}
          {this.state.formType === "custom" && <>{this.state.formData.icon}</>}
          <span>
            {this.state.formType === "delete" && "Hapus Data"}
            {this.state.formType === "activated" &&
              `${this.state.formData.text.action}`}
            {this.state.formType === "custom" &&
              `${this.state.formData.text.action}`}
          </span>
        </button>
      </>
    );
  }
}
