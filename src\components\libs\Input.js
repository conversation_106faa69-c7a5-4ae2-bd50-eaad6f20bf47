/*
Created by esoda
Created on Mar, 2025
Contact esoda.id
*/

import React, { useImperativeHandle, useEffect } from "react";
import { useNumberFormat } from "@react-input/number-format";

export const numberFormatIdToNumber = (value) => {
  return Number(value.replace(/\./g, ""));
};

const Input = ({
  formClass = "",
  inputContainerClass = "",
  inputType = "text", // Add 'select' as a possible value
  valueType = "text",
  label,
  value,
  error,
  icon, // This seems to be used for a start adornment icon
  minLength,
  maxLength,
  onFocus = () => {},
  onChange = () => {},
  optional,
  required,
  readonly,
  readonlyMessage,
  disablePlaceholder,
  options, // New prop for select options
  startAdornment, // New prop for start adornment
  endAdornment, // New prop for end adornment
  type, // Allow passing type attribute for input (e.g., password)
  inputNumberOptions = null,
  styleInputs = {},
  style = {},
  defaultValue,
  ...props
}) => {
  const ref_InputSelect = React.useRef(null);
  const ref_InputText = React.useRef(null);
  const ref_InputNumber = useNumberFormat(
    inputNumberOptions
      ? inputNumberOptions
      : {
          locales: "id",
          maximumFractionDigits: 0,
        }
  );

  useEffect(() => {
    if (ref_InputSelect.current) {
      // set select value
      ref_InputSelect.current.value = defaultValue;
    }

    return () => {};
  }, [ref_InputSelect, defaultValue]);

  // useEffect(() => {
  //   if (ref_InputSelect.current) {
  //     // set select value
  //     ref_InputSelect.current.value = defaultValue;
  //   }

  //   return () => {};
  // }, [ref_InputSelect, defaultValue]);

  // ====================================================================================
  // ========== RENDER SECTIONS =========================================================
  // ====================================================================================
  return (
    <>
      <div className={`form-input ${formClass}`}>
        {label && <label>{label}</label>}
        <div className={`inputs ${error && "error"} ${inputContainerClass}`} style={styleInputs}>
          {/* Render start adornment if provided */}
          {startAdornment && (
            <div className={"adornment start"}>{startAdornment}</div>
          )}
          {/* Existing icon logic, could potentially be replaced by startAdornment */}
          {inputType === "text" && icon && !startAdornment && (
            <div className={"icon"}>
              <i className={`ph ${icon}`}></i>
            </div>
          )}

          {/* Conditionally render input, textarea, or select */}
          {inputType === "text" && (
            <input
              ref={ref_InputText}
              type={type || inputType} // Use passed type or default to inputType
              value={value || ""}
              onFocus={onFocus}
              onChange={(e) => onChange(e)}
              maxLength={maxLength}
              readOnly={readonly} // Add readonly attribute
              {...props}
            />
          )}
          {inputType === "number" && (
            <input
              ref={ref_InputNumber}
              // type={type || inputType} // Use passed type or default to inputType
              value={value || ""}
              onFocus={onFocus}
              onChange={(e) => onChange(e)}
              maxLength={maxLength}
              readOnly={readonly} // Add readonly attribute
              {...props}
            />
          )}
          {inputType === "textarea" && (
            <textarea
              value={value || ""}
              onFocus={onFocus}
              onChange={(e) => onChange(e)}
              maxLength={maxLength}
              readOnly={readonly} // Add readonly attribute
              {...props}
            ></textarea>
          )}
          {inputType === "select" && (
            <select
              ref={ref_InputSelect}
              // value={value || ""}
              value={value || null}
              onFocus={onFocus}
              onChange={(e) => onChange(e)}
              disabled={readonly} // Use disabled for select readonly
              {...props}
            >
              {/* Render options for select */}
              {options &&
                options.map((option, index) => (
                  <option
                    key={index}
                    value={option.value}
                    disabled={option.disabled}
                  >
                    {option.label}
                  </option>
                ))}
            </select>
          )}

          {/* Render end adornment if provided */}
          {endAdornment && (
            <div className={"adornment end"}>{endAdornment}</div>
          )}

          {/* Existing symbol logic */}
          {inputType !== "select" && (
            <>
              {error ? (
                <div className={"symbol"}>
                  <i className="ph ph-warning-circle"></i>
                </div>
              ) : (
                value &&
                value.length >= minLength && (
                  <div className={"symbol valid"}>
                    <i className="ph ph-check-circle"></i>
                  </div>
                )
              )}
            </>
          )}
        </div>
        {!disablePlaceholder && (
          <div className="placeholder">
            {required && !readonly && <span className="required">Wajib</span>}
            {optional && !readonly && (
              <span className="optional">Opsional</span>
            )}
            {readonly && <span className="read-only">tidak dapat diubah</span>}
            {!readonly && inputType !== "select" && (
              <span className="note">
                {!error ? (
                  <>
                    {valueType !== "date" ? (
                      <>
                        <b>{value ? value.length : 0}</b>{" "}
                        <span
                          style={{
                            textTransform: "lowercase",
                            fontSize: ".7rem",
                          }}
                        >
                          dari
                        </span>{" "}
                        {maxLength} karakter
                      </>
                    ) : (
                      "Pilih tanggal"
                    )}
                  </>
                ) : (
                  error
                )}
              </span>
            )}
            {!readonly && inputType === "select" && (
              <span className="note">
                {!error ? "Pilih dari pilihan" : error}
              </span>
            )}
            {readonlyMessage && <span className="note">{readonlyMessage}</span>}
          </div>
        )}
      </div>
    </>
  );
};

export default Input;
