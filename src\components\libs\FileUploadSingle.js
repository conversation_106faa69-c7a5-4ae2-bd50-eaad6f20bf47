/*
Created by esoda
Created on Apr, 2025
Contact esoda.id
*/

import React, { useRef } from 'react';
import Loading from "../modal/Loading";
import Constants from "@/utils/Constants";
import Tooltip from '@mui/material/Tooltip';
import Snackbar from '@mui/material/Snackbar';
import ApiHelpers from '@/utils/ApiHelper';

const FileUploadSingle = ({ id, title, placeholder, alt, file_url, error, onCompleted = () => { }, optional, ...props }) => {
    const ref_loading = useRef(null)
    const [openSnackbar, setOpenSnackbar] = React.useState(false)

    // ====================================================================================
    // ========== ACTION LISTENERS ========================================================
    // ====================================================================================
    const onUploadImageListeners = async (e) => {
        console.log(e.target.files[0])
        
        if (e.target.files[0].size > 50000000) {
            setOpenSnackbar(true)
            setMessageSnackbar("Ukuran file terlalu besar.")
            setSeveritySnackbar("error")
        } else {
            ref_loading.current.onShowDialog()
            
            let formData = new FormData();
            formData.append("file", e.target.files[0]);

            let response = await ApiHelpers.uploadFile(formData)
            console.log(response)
            
            if (response.status === 200) {
                ref_loading.current.onCloseDialog()
                onCompleted(response.data)
            } else {
                ref_loading.current.onCloseDialog()
                setOpenSnackbar(true)
                setMessageSnackbar(response.message)
                setSeveritySnackbar("error")
            }
        }
    }
    const onCopyImageUrlListeners = () => {
        navigator.clipboard.writeText(file_url);
        setOpenSnackbar(true)
    }
    const onCloseSnackbarListeners = (event, reason) => {
        if (reason === 'clickaway') { return; }
        setOpenSnackbar(false);
    }

    // ====================================================================================
    // ========== RENDER SECTIONS =========================================================
    // ====================================================================================
    return (
        <>
            <div className="form-image">
                {title &&
                    <div className="title">{title}</div>
                }
                <div className="image" {...props}>
                    {!file_url &&
                        <img alt={alt}
                            src={Constants.image_default.file}
                        />
                    }
                    {file_url &&
                        <div className="file-preview">
                            <object data={file_url} type="application/pdf" style={{ height: "100%", width: "100%" }} aria-label="Web browser definition from Wikipedia in PDF format">
                                <iframe src={file_url} style={{ height: "100%", width: "100%" }} frameborder="0"></iframe>
                            </object>
                        </div>
                    }
                </div>
                <div className={`inputs ${error && "error"}`}>
                    <div className={"icon"}><i className="ph ph-link-simple"></i></div>
                    <input
                        placeholder={placeholder || "Pilih file..."}
                        value={file_url || ""}
                        disabled
                    />
                    <input
                        id={id}
                        type="file"
                        accept="image/*,.pdf"
                        onChange={(e) => { onUploadImageListeners(e) }}
                        style={{ display: "none" }}
                    />

                    {file_url && <Tooltip title="Salin URL File">
                        <button className="copy" onClick={onCopyImageUrlListeners}>
                            <i className="ph ph-copy"></i>
                        </button>
                    </Tooltip>}
                    <label for={id}>
                        <i className="ph ph-magnifying-glass"></i><span>Pilih File</span>
                    </label>
                </div>
                <div className="placeholder">
                    {optional && <span className="optional">Opsional</span>}
                    {!optional && <span className="required">Wajib</span>}
                    <span className="note">
                        {!error ?
                            <>Ukuran file: <b>50MB</b> Maksimal</>
                            : error
                        }
                    </span>
                </div>
            </div>
            <Loading ref={ref_loading} />
            <Snackbar
                open={openSnackbar}
                autoHideDuration={1500}
                onClose={onCloseSnackbarListeners}
                sx={{ '&.MuiSnackbar-root': { top: '20%' } }}
                anchorOrigin={{ vertical: "top", horizontal: "center" }}
                message={"URL file berhasil disalin."}
                action={
                    <i className='ph ph-bold ph-x-circle' 
                        style={{ cursor: "pointer" }} onClick={onCloseSnackbarListeners}>
                    </i>
                }
            />
        </>
    )
}

export default FileUploadSingle