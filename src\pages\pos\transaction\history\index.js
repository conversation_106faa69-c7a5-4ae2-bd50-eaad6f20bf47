import React, { useEffect } from "react";
import styles from "@/styles/Kiosk.module.css";
import useTrackedPersistKioskPosStore from "@/store/kiosk/pos/storePersist";
import useTransactionHistoryStore from "@/store/kiosk/pos/transaction/history/store";
import CommonHelper from "@/utils/CommonHelper";
import MomentHelper from "@/utils/MomentHelper";
import Loading from "@/components/modal/Loading";
import MySnackbar from "@/components/MySnackbar";
import Router from "next/router";
import Image from "next/image";

const ARR_TAB = [
  {
    label: "Semua",
    value: "all",
  },
  {
    label: "Menunggu Diproses",
    value: "Menunggu Diproses",
  },
  {
    label: "Menunggu Pembayaran",
    value: "Menunggu pembayaran",
  },
  {
    label: "Terbayar",
    value: "Terbayar",
  },
];

export default function TransactionHistory() {
  const { objCustomer } = useTrackedPersistKioskPosStore();
  const {
    transactions,
    isLoading,
    page,
    totalPages,
    searchQuery,
    selectedTabIdx,
    selectedTabValue,
    setPage,
    setSearchQuery,
    setSelectedTab,
    fetchTransactions,
  } = useTransactionHistoryStore();

  useEffect(() => {
    if (objCustomer?.id) {
      console.log(objCustomer);
      
      fetchTransactions(objCustomer.id);
    }
  }, [selectedTabValue, page, objCustomer?.id]);

  const handleTabChange = (index) => {
    setSelectedTab(index, ARR_TAB[index].value);
    setPage(1);
  };

  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
    setPage(1);
  };

  const handlePageChange = (newPage) => {
    setPage(newPage);
  };

  const renderTransactionItem = (transaction) => {
    return (
      <div className={styles.payment_detail}>
        <div className={styles.section}>
          <div className={styles.header}>
            <div className={styles.left}>
              <div className={styles.title}>
                <h2>Transaksi #{transaction.number}</h2>
                <p>
                  {MomentHelper.format(
                    transaction.input_datetime,
                    MomentHelper.datetimeFormatReadable2
                  )}
                </p>
              </div>
            </div>
            <div className={`${styles.status} ${styles[transaction.status]}`}>
              {transaction.status}
            </div>
          </div>

          <div className={styles.info_grid}>
            <div className={styles.info_item}>
              <label>Total Item</label>
              <div className={styles.value}>
                {transaction.total_product_item}
              </div>
            </div>
            <div className={styles.info_item}>
              <label>Total Pembayaran</label>
              <div className={`${styles.value} ${styles.total}`}>
                {CommonHelper.formatNumber(transaction.grand_total, "idr")}
              </div>
            </div>
          </div>

          <div className={styles.product_section}>
            <div className={styles.title}>Detail Produk</div>
            <div className={styles.items}>
              {transaction.product_array?.map((product, index) => (
                <div key={index} className={styles.item}>
                  <div className={styles.product_image}>
                    <div className={styles.image}>
                      <img
                        src={product.image_url || "/images/no-image.png"}
                        alt={product.name}
                      />
                    </div>
                  </div>
                  <div className={styles.product_info}>
                    <div className={styles.product_header}>
                      <div className={styles.name}>{product.name}</div>
                      <div className={styles.code}>{product.code}</div>
                    </div>
                    <div className={styles.product_details}>
                      <div className={styles.quantity_price}>
                        <div className={styles.quantity}>
                          {product.quantity} {product.unit_name}
                        </div>
                        <div className={styles.total_price}>
                          {CommonHelper.formatNumber(
                            product.total_selling_price,
                            "idr"
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={styles.kiosk_payment}>
      <div className={styles.catalogs}>
        <div className={styles.topbar}>
          <div className={styles.store}>
            <a
              className={styles.back_button}
              onClick={() => {
                Router.replace({
                  pathname: "/pos",
                  query: { from: "payment_detail" },
                });
              }}
            >
              <i className="ph ph-arrow-left"></i>
            </a>
            <div className={styles.info}>
              <div className={styles.title}>Riwayat Transaksi</div>
              <div className={styles.subtitle}>Lihat semua transaksi Anda</div>
            </div>
            <div className={styles.searchbar_base}>
              <div className={styles.input_search}>
                <label>
                  <i className="ph ph-bold ph-magnifying-glass"></i>
                </label>
                <input
                  type="text"
                  placeholder="Cari transaksi..."
                  value={searchQuery}
                  onChange={handleSearch}
                />
              </div>
            </div>
          </div>
        </div>

        <div className="tab-bar" style={{ overflow: "auto" }}>
          {ARR_TAB.map((item, index) => (
            <div
              key={index}
              className={`item ${selectedTabIdx === index ? "selected" : ""}`}
              onClick={() => handleTabChange(index)}
            >
              {item.label}
            </div>
          ))}
        </div>

        <div className={styles.carts}>
          {isLoading ? (
            <div className={styles.empty}>
              <div className={styles.title}>Memuat data...</div>
            </div>
          ) : transactions.length > 0 ? (
            <>
              {transactions.map((transaction, index) => (
                <div key={index}>{renderTransactionItem(transaction)}</div>
              ))}

              <div className={styles.pagination}>
                {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                  (pageNum) => (
                    <button
                      key={pageNum}
                      className={`button ${
                        page === pageNum ? "primary" : "outlined"
                      }`}
                      onClick={() => handlePageChange(pageNum)}
                    >
                      {pageNum}
                    </button>
                  )
                )}
              </div>
            </>
          ) : (
            <div className={styles.empty}>
              <Image
                src="/assets/images/img_empty.png"
                alt="Tidak Ada Data"
                width={150}
                height={150}
              />
              <div className={styles.title}>Tidak ada transaksi</div>
              <div className={styles.subtitle}>
                Belum ada transaksi yang ditemukan
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
