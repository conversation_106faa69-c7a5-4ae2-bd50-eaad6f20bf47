/*
Created by esoda
Created on Apr, 2025
Contact esoda.id
*/

import React from 'react';
import { debounce } from '@mui/material';
import ApiHelpers from '@/utils/ApiHelper';

const InputAutoComplete = ({
    inputName,
    formClass,
    dataUrl,
    dataParams,
    label,
    error, 
    icon,
    onFocus = () => {},
    onChange = () => {},
    optional,
    required,
    readonly,
    readonlyMessage,
    defaultData,
    displayTitle = "name",
    displaySubtitle = "number",
    ...props 
}) => {
    // ====================================================================================
    // ========== RENDER SECTIONS =========================================================
    // ====================================================================================
    const [arrData, setData] = React.useState([])
    const [changeValue, setChangeValue] = React.useState("")
    const [showDataList, setShowDataList] = React.useState(false)
    
    const handleChange = async event => {
        //const params = event.target.value
        // fetch(`https://jsonplaceholder.typicode.com/users?s=${params}`)
        //     .then(res => res.json())
        //     .then(setData)
        //     .catch(console.error)

        let apiEndPoint = dataUrl
        let paramSearch = {
            search: event.target.value
        }
        let params = { ...dataParams, ...paramSearch }
    
        let response = await ApiHelpers.get(apiEndPoint, params);
        console.log('response', response)

        if (
            response.status == 200 &&
            response.results.data !== undefined &&
            response.results.data
          ) {
            setData(response.results.data)
          }

        onChange(null)
        setChangeValue(event.target.value)
        //if(event.target.value.length >= 1) {
            setShowDataList(true)
        //}
    }
    const renderCaret = () => {
        if(changeValue.length >= 1 || arrData.length > 1) {
            return (
                <div className={"symbol"}>< i className="ph ph-caret-down"></i></div>
            )
        }
    }

    // ====================================================================================
    // ========== RENDER SECTIONS =========================================================
    // ====================================================================================
    return (
        <>
            <div className={`form-input ${formClass}`}>
                <label>{label}</label>
                <div className={`inputs ${error && "error"}`}>
                    <div className={"icon"}><i className={`ph ${icon}`}></i></div>
                    <input
                        type='text'
                        name={inputName || "input-auto-complete"}
                        onFocus={onFocus}
                        onChange={debounce(handleChange, 300)}
                        list="data-list"
                        autoComplete='off'
                        {...props}
                    />
                    {error ? <div className={"symbol"}>< i className="ph ph-warning-circle"></i></div>
                        : renderCaret()
                    }
                </div>
                <div id="data-list" className={`data-list ${showDataList && "show"}`}>
                    {arrData.map((item, index) => {
                        //if (item.name.includes(changeValue)) {
                            return (
                                <div key={index} className="items"
                                    onClick={() => { 
                                        onChange(item)
                                        let input = document.getElementsByName(inputName || "input-auto-complete")[0]
                                        input.value = item[displayTitle]
                                        setShowDataList(false)
                                    }}>
                                    <div className='title'>{item[displayTitle]}</div>
                                    <div className='subtitle'>{item[displaySubtitle]}</div>
                                </div>
                            )
                        //}
                    })}
                </div>
                <div className="placeholder">
                    {required && !readonly && <span className="required">Wajib</span>}
                    {optional && !readonly && <span className="optional">Opsional</span>}
                    {readonly && <span className="read-only">tidak dapat diubah</span>}
                    {!readonly && <span className="note">
                        {!error ? <>Pilih dari pilihan yang ada</>
                            : error
                        }
                    </span>}
                    {readonlyMessage && <span className="note">{readonlyMessage}</span>}
                </div>
            </div>
        </>
    )
}

export default InputAutoComplete