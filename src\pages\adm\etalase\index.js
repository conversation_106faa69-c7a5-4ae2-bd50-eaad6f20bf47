/*
Created by esoda
Created on Apr, 2025
Contact esoda.id
*/

import React from "react";
import styles from "@/styles/Pos.module.css";
import stylesTable from "@/styles/Table.module.css";

import Loading from "@/components/modal/Loading";
import PosComponentTopbar from "@/components/pages/adm/_topbar";
import Router from "next/router";

import Constants from "@/utils/Constants";
import AuthWrapperAdmin from "@/components/wrapper/AuthWrapperAdmin";
import AdminSideBar from "@/components/pages/adm/AdminSideBar";

import CommonHelper from "@/utils/CommonHelper";
import ModalConfirmation from "@/components/modal/ModalConfirmation";
import MySnackbar from "@/components/MySnackbar";
import EtalaseList from "@/components/pages/adm/etalase/EtalaseList";

class AdminEtalase extends React.Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  // ====================================================================================
  // ========== INITIALIZE, GET DATA ====================================================
  // ====================================================================================
  componentDidUpdate(prevProps, prevState) {
    if (prevProps.isAuthLoading !== this.props.isAuthLoading) {
      if (!this.props.isAuthLoading && this.props.isLoggedIn) {
        this.onRefresh();
      }
    }
  }

  onRefresh = () => {
    if (this.ref_EtalaseList?.onInit) {
      this.ref_EtalaseList?.onInit();
    }
  };
  // ====================================================================================
  // ========== ACTION LISTENERS ========================================================
  // ====================================================================================

  // ====================================================================================
  // ========== RENDER SECTIONS =========================================================
  // ====================================================================================
  render() {
    const { isAuthLoading, user, menu } = this.props;
    return (
      <div
        className={`${styles.ct_containers}`}
        style={{ backgroundColor: "rgb(252, 252, 252)" }}
      >
        <PosComponentTopbar
          hideButton
          title={"Kategori Produk"}
          subtitle={"Atur data kategori produk"}
          user={user}
          isAuthLoading={isAuthLoading}
        />
        <div className={`${styles.contents} ${styles.sidebar}`}>
          <AdminSideBar isAuthLoading={isAuthLoading} menu={menu} />
          {this.renderContent()}
        </div>

        <Loading ref={(value) => (this.ref_Loading = value)} />
        <MySnackbar ref={(value) => (this.ref_MySnackbar = value)} />
        <ModalConfirmation
          ref={(value) => (this.ref_ModalConfirmation = value)}
        />
      </div>
    );
  }

  renderContent() {
    return (
      <div className={styles.ctx}>
        <EtalaseList
          ref={(value) => (this.ref_EtalaseList = value)}
          selectedTabTitle={"Data Etalase"}
          isAuthLoading={this.props.isAuthLoading}
        />
      </div>
    );
  }
}

export default AuthWrapperAdmin(AdminEtalase, {
  redirectTo: Constants.webUrl.adm.login,
});
