/*
Created for fullscreen image viewing with zoom and pan functionality
*/

import React, { Component } from "react";
import { Modal, Box, IconButton, Tooltip } from "@mui/material";
import CommonHelper from "@/utils/CommonHelper";

export default class ImageViewer extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showModal: false,
      isFullscreen: false,
      scale: 1,
      position: { x: 0, y: 0 },
      dragging: false,
      startPosition: { x: 0, y: 0 },
      imageUrl: "",
    };
    this.containerRef = React.createRef();
    this.imageRef = React.createRef();
  }

  componentDidMount() {
    document.addEventListener("fullscreenchange", this.onFullscreenChange);
    document.addEventListener(
      "webkitfullscreenchange",
      this.onFullscreenChange
    );
    document.addEventListener("mozfullscreenchange", this.onFullscreenChange);
    document.addEventListener("MSFullscreenChange", this.onFullscreenChange);

    // Add keyboard event listener for Escape key
    document.addEventListener("keydown", this.handleKeyDown);
  }

  componentWillUnmount() {
    document.removeEventListener("fullscreenchange", this.onFullscreenChange);
    document.removeEventListener(
      "webkitfullscreenchange",
      this.onFullscreenChange
    );
    document.removeEventListener(
      "mozfullscreenchange",
      this.onFullscreenChange
    );
    document.removeEventListener("MSFullscreenChange", this.onFullscreenChange);
    document.removeEventListener("keydown", this.handleKeyDown);
  }

  handleKeyDown = (e) => {
    if (e.key === "Escape" && this.state.showModal) {
      this.onCloseDialog();
    }
  };

  onFullscreenChange = () => {
    const isFullscreen =
      document.fullscreenElement ||
      document.webkitFullscreenElement ||
      document.mozFullScreenElement ||
      document.msFullscreenElement;

    if (!isFullscreen) {
      this.setState({ isFullscreen: false });
    }
  };

  onShowDialog = (imageUrl) => {
    this.setState({
      showModal: true,
      imageUrl,
      scale: 1,
      position: { x: 0, y: 0 },
    });
  };

  onCloseDialog = () => {
    if (this.state.isFullscreen) {
      CommonHelper.exitFullscreen();
    }
    this.setState({
      showModal: false,
      isFullscreen: false,
      scale: 1,
      position: { x: 0, y: 0 },
    });
  };

  toggleFullscreen = () => {
    if (!this.state.isFullscreen) {
      if (this.containerRef.current) {
        CommonHelper.requestFullscreen(this.containerRef.current);
        this.setState({ isFullscreen: true });
      }
    } else {
      CommonHelper.exitFullscreen();
      this.setState({ isFullscreen: false });
    }
  };

  handleZoomIn = () => {
    this.setState((prevState) => ({
      scale: prevState.scale + 0.25,
    }));
  };

  handleZoomOut = () => {
    this.setState((prevState) => ({
      scale: Math.max(0.5, prevState.scale - 0.25),
    }));
  };

  handleReset = () => {
    this.setState({
      scale: 1,
      position: { x: 0, y: 0 },
    });
  };

  handleWheel = (e) => {
    e.preventDefault();
    const delta = e.deltaY * -0.01;
    this.setState((prevState) => ({
      scale: Math.max(0.5, Math.min(5, prevState.scale + delta)),
    }));
  };

  handleMouseDown = (e) => {
    if (this.state.scale > 1) {
      this.setState({
        dragging: true,
        startPosition: {
          x: e.clientX - this.state.position.x,
          y: e.clientY - this.state.position.y,
        },
      });
    }
  };

  handleMouseMove = (e) => {
    if (this.state.dragging) {
      this.setState({
        position: {
          x: e.clientX - this.state.startPosition.x,
          y: e.clientY - this.state.startPosition.y,
        },
      });
    }
  };

  handleMouseUp = () => {
    this.setState({ dragging: false });
  };

  handleMouseLeave = () => {
    if (this.state.dragging) {
      this.setState({ dragging: false });
    }
  };

  render() {
    const { showModal, scale, position, isFullscreen, imageUrl } = this.state;

    return (
      <Modal
        open={showModal}
        onClose={this.onCloseDialog}
        aria-labelledby="image-viewer-modal"
        aria-describedby="fullscreen-image-viewer-with-zoom"
      >
        <Box
          ref={this.containerRef}
          sx={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            bgcolor: "rgba(0, 0, 0, 0.9)",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            outline: "none",
          }}
        >
          {/* Controls */}
          <Box
            sx={{
              position: "absolute",
              top: 16,
              right: 16,
              display: "flex",
              gap: 1,
              zIndex: 10,
            }}
          >
            <Tooltip title="Perbesar">
              <IconButton onClick={this.handleZoomIn} sx={{ color: "white" }}>
                <i className="ph ph-bold ph-magnifying-glass-plus"></i>
              </IconButton>
            </Tooltip>
            <Tooltip title="Perkecil">
              <IconButton onClick={this.handleZoomOut} sx={{ color: "white" }}>
                <i className="ph ph-bold ph-magnifying-glass-minus"></i>
              </IconButton>
            </Tooltip>
            <Tooltip title="Reset">
              <IconButton onClick={this.handleReset} sx={{ color: "white" }}>
                <i className="ph ph-bold ph-arrows-left-right"></i>
              </IconButton>
            </Tooltip>
            <Tooltip
              title={isFullscreen ? "Keluar dari Layar Penuh" : "Layar Penuh"}
            >
              <IconButton
                onClick={this.toggleFullscreen}
                sx={{ color: "white" }}
              >
                {/* <BaseIcon>{isFullscreen ? "fullscreen_exit" : "fullscreen"}</BaseIcon> */}
                {isFullscreen ? (
                  <i className="ph ph-bold ph-square"></i>
                ) : (
                  <i className="ph ph-bold ph-square"></i>
                )}
              </IconButton>
            </Tooltip>
            <Tooltip title="Tutup">
              <IconButton onClick={this.onCloseDialog} sx={{ color: "white" }}>
                <i className="ph ph-bold ph-x"></i>
              </IconButton>
            </Tooltip>
          </Box>

          {/* Image Container */}
          <Box
            sx={{
              width: "100%",
              height: "100%",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              overflow: "hidden",
              cursor: scale > 1 ? "grab" : "default",
              "&:active": {
                cursor: scale > 1 ? "grabbing" : "default",
              },
            }}
            onWheel={this.handleWheel}
            onMouseDown={this.handleMouseDown}
            onMouseMove={this.handleMouseMove}
            onMouseUp={this.handleMouseUp}
            onMouseLeave={this.handleMouseLeave}
          >
            <img
              ref={this.imageRef}
              src={imageUrl}
              alt="Fullscreen view"
              style={{
                maxWidth: "90%",
                maxHeight: "90%",
                transform: `scale(${scale}) translate(${
                  position.x / scale
                }px, ${position.y / scale}px)`,
                transformOrigin: "center",
                transition: this.state.dragging ? "none" : "transform 0.1s",
              }}
              draggable="false"
            />
          </Box>
        </Box>
      </Modal>
    );
  }
}
