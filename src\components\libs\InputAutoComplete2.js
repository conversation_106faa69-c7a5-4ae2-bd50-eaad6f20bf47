/*
Created by esoda
Created on Mei, 2025
Contact esoda.id
*/

import React, { useImperativeHandle } from "react";
import { debounce } from "@mui/material";
import ApiHelpers from "@/utils/ApiHelper";
import MySnackbar from "../MySnackbar";

const InputAutoComplete = ({
  inputName,
  formClass,
  dataUrl,
  dataParams,
  label,
  error,
  icon,
  onFocus = () => {},
  onChange = () => {},
  optional,
  required,
  readonly,
  readonlyMessage,
  defaultData,
  displayTitle = "name",
  displaySubtitle = "number",
  pageSize = 10, // Number of items per page
  displayRenderCustom = false,
  ref,
  disablePagination = false,
  ...props
}) => {
  useImperativeHandle(
    ref,
    () => {
      return {
        setDefaultInputs,
      };
    },
    []
  );
  // ====================================================================================
  // ========== RENDER SECTIONS =========================================================
  // ====================================================================================
  const [arrData, setData] = React.useState([]);
  const [changeValue, setChangeValue] = React.useState("");
  const [showDataList, setShowDataList] = React.useState(false);
  const [page, setPage] = React.useState(1);
  const [hasMore, setHasMore] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(false);
  const dataListRef = React.useRef(null);
  const inputRef = React.useRef(null);
  const ref_MySnackbar = React.useRef(null);
  const abortControllerRef = React.useRef(null);

  const setDefaultInputs = (value) => {
    let input = document.getElementsByName(
      inputName || "input-auto-complete"
    )[0];
    input.value = value;
  };

  const handleChange = async (event) => {
    // Cancel any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new AbortController for this request
    abortControllerRef.current = new AbortController();
    const signal = abortControllerRef.current.signal;

    setIsLoading(true);
    // Reset pagination when search term changes
    setPage(1);

    let apiEndPoint = dataUrl;
    let paramSearch = {
      search: event.target.value,
      page: 1,
      limit: pageSize,
    };
    let params = { ...dataParams, ...paramSearch };
    let response = null;
    try {
      response = await ApiHelpers.get(apiEndPoint, params, {
        signal,
      });

      if (
        response.status == 200 &&
        response.results.data !== undefined &&
        response.results.data
      ) {
        setData(response.results.data);

        // Check if there are more items to load
        if (response.results.pagination) {
          const total = response.results.pagination.total_page || 0;
          setHasMore(response.results.pagination.current < total);
          // empty data
          if (response.results.pagination.total_data === 0) {
            if (ref_MySnackbar.current) {
              ref_MySnackbar.current.onNotify(
                "Data tidak ditemukan.",
                "warning"
              );
            }
          }
        } else {
          // If pagination info is not available, estimate based on pageSize
          setHasMore(response.results.data.length >= pageSize);
        }
      } else {
        if (ref_MySnackbar.current && response.status !== 205) {
          ref_MySnackbar.current.onNotify(
            response?.message || "Gagal memuat data.",
            "error"
          );
        }
      }
    } catch (error) {
      // Only show error if it's not an abort error
      if (error.name !== "AbortError" && ref_MySnackbar.current) {
        ref_MySnackbar.current.onNotify("Gagal memuat data.", "error");
      }
    }

    if (response?.status === 205) {
      return;
    }

    setIsLoading(false);
    onChange(null, "input");
    setChangeValue(event.target.value);
    setShowDataList(true);
  };

  const handleDataListVisibility = () => {
    setShowDataList(!showDataList);
  };

  const renderCaret = () => {
    if (changeValue.length >= 1 || arrData.length > 1) {
      return (
        <div
          className={"symbol"}
          style={{ cursor: "pointer" }}
          onClick={handleDataListVisibility}
        >
          <i className={`ph ph-caret-${showDataList ? "down" : "up"}`}></i>
        </div>
      );
    }
  };

  // Function to load more data when scrolling or clicking "Load More"
  const loadMoreData = async () => {
    if (isLoading || !hasMore || disablePagination) return;

    setIsLoading(true);
    const nextPage = page + 1;

    let apiEndPoint = dataUrl;
    let params = {
      ...dataParams,
      search: changeValue,
      page: nextPage,
      limit: pageSize,
    };

    let response = await ApiHelpers.get(apiEndPoint, params);

    if (
      response.status == 200 &&
      response.results.data !== undefined &&
      response.results.data
    ) {
      // Append new data to existing data
      setData((prevData) => [...prevData, ...response.results.data]);
      setPage(nextPage);

      // Check if there are more items to load
      if (response.results.pagination) {
        const total = response.results.pagination.total_page || 0;
        setHasMore(response.results.pagination.current < total);
      } else {
        // If pagination info is not available, estimate based on response size
        setHasMore(response.results.data.length >= pageSize);
      }
    } else {
      setHasMore(false);
    }

    setIsLoading(false);
  };

  // Handle scroll event to implement infinite scroll
  const handleScroll = (e) => {
    if (!dataListRef.current) return;

    const { scrollTop, scrollHeight, clientHeight } = e.target;
    // Load more data when user scrolls to 80% of the list
    if (
      scrollTop + clientHeight >= scrollHeight * 0.8 &&
      !isLoading &&
      hasMore
    ) {
      loadMoreData();
    }
  };

  // Check if component is inside a modal
  React.useEffect(() => {
    if (!showDataList || !dataListRef.current || !inputRef.current) return;

    // Find closest modal parent
    const isInsideModal = (element) => {
      if (!element) return false;
      if (
        element.classList &&
        (element.classList.contains("modal-content") ||
          element.classList.contains("modal-body"))
      ) {
        return true;
      }
      return element.parentElement
        ? isInsideModal(element.parentElement)
        : false;
    };

    const insideModal = isInsideModal(inputRef.current);

    if (insideModal) {
      // Ensure the data-list is positioned correctly within the modal
      const dataList = dataListRef.current;
      dataList.style.position = "relative";
      dataList.style.zIndex = "9999";

      // Prevent the data-list from being clipped by overflow settings
      const modalBody = inputRef.current.closest(".modal-body");
      if (modalBody) {
        const originalOverflow = modalBody.style.overflow;
        if (showDataList) {
          modalBody.style.overflow = "auto";
        }

        return () => {
          modalBody.style.overflow = originalOverflow;
        };
      }
    }
  }, [showDataList]);

  // ====================================================================================
  // ========== RENDER SECTIONS =========================================================
  // ====================================================================================
  return (
    <>
      <div className={`form-input ${formClass}`}>
        <label>{label}</label>
        <div className={`inputs ${error && "error"}`}>
          {icon && (
            <div className={"icon"}>
              <i className={`ph ${icon}`}></i>
            </div>
          )}
          <input
            type="text"
            name={inputName || "input-auto-complete"}
            onFocus={() => {
              onFocus();

              if (inputRef.current.value.length === 0) {
                handleChange({ target: { value: "" } });
              }
            }}
            onChange={debounce(handleChange, 300)}
            list="data-list"
            autoComplete="off"
            ref={inputRef}
            {...props}
          />
          {error ? (
            <div className={"symbol"}>
              <i className="ph ph-warning-circle"></i>
            </div>
          ) : (
            renderCaret()
          )}
          {/* Loading Indicator Icon*/}
          {isLoading && (
            <div
              className={"symbol"}
              style={{ animation: "spin 2s linear infinite" }}
            >
              <i className="ph ph-spinner ph-spin"></i>
            </div>
          )}
        </div>
        <div
          id="data-list"
          className={`data-list ${showDataList && "show"}`}
          ref={dataListRef}
          onScroll={handleScroll}
        >
          {arrData.map((item, index) => {
            return (
              <div
                key={index}
                className="items"
                onClick={() => {
                  let input = document.getElementsByName(
                    inputName || "input-auto-complete"
                  )[0];
                  input.value = item[displayTitle];
                  onChange(item, "select");
                  //   if (!displayRenderCustom) {
                  //   }
                  setShowDataList(false);
                }}
              >
                {!displayRenderCustom && (
                  <>
                    <div className="title">{item[displayTitle]}</div>
                    <div className="subtitle">{item[displaySubtitle]}</div>
                  </>
                )}
                {displayRenderCustom && <>{displayRenderCustom(item, index)}</>}
              </div>
            );
          })}

          {isLoading && (
            <div className="loading-indicator">
              <div className="loading-spinner"></div>
              <span>Memuat data...</span>
            </div>
          )}

          {!isLoading && hasMore && !disablePagination && (
            <div className="load-more" onClick={loadMoreData}>
              <span>Muat selanjutnya</span>
            </div>
          )}
        </div>
        <div className="placeholder">
          {required && !readonly && <span className="required">Wajib</span>}
          {optional && !readonly && <span className="optional">Opsional</span>}
          {readonly && <span className="read-only">tidak dapat diubah</span>}
          {!readonly && (
            <span className="note">
              {!error ? <>Pilih dari pilihan yang ada</> : error}
            </span>
          )}
          {readonlyMessage && <span className="note">{readonlyMessage}</span>}
        </div>
      </div>

      <MySnackbar ref={ref_MySnackbar} />
    </>
  );
};

export default InputAutoComplete;
