/*
Created by esoda
Created on Nov, 2024
Contact esoda.id
*/

import React from "react";
import {
  Modal,
  Box,
  TextField,
  InputAdornment,
  MenuItem,
  IconButton,
  Chip,
} from "@mui/material";
import Loading from "@/components/modal/Loading";
import Constants from "@/utils/Constants";
import ImageUploadSingle from "@/components/libs/ImageUploadSingle";
import AdminModalListBranch from "@/components/pages/adm/modal/AdminModalListBranch";
import AdminModalListEmployee from "@/components/pages/adm/modal/AdminModalListEmployee";
import ApiHelper from "@/utils/ApiHelper";
import MySnackbar from "@/components/MySnackbar";
import Input from "@/components/libs/Input";

const DEFAULT_INPUTS = {
  id: "",
  par_id: "",
  par_object: null,
  name: "",
  image_url: "",
};

export default class EtalaseModalAddUpdate extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showDialog: false,
      formType: "add",
      formData: null,
      formIndex: -1,

      inputs: structuredClone(DEFAULT_INPUTS),
      errors: {},
    };
  }

  // ====================================================================================
  // ========== INITIALIZE, GET DATA ====================================================
  // ====================================================================================
  onShowDialog = (
    formType,
    formData = null,
    formIndex = -1,
    formInputs = null
  ) => {
    let inputs = structuredClone(DEFAULT_INPUTS);
    if (formInputs) {
      inputs = formInputs;
    }
    this.setState(
      {
        formType,
        formData,
        formIndex,
        showDialog: true,

        inputs,
      },
      () => {
        if (formType === "edit") {
          this.onFetchDetail();
        } else if (formType === "add") {
          if (formData?.id && Number(formData?.id) > 0) {
            this.onFetchDetail();
          }
        }
        setTimeout(() => {
          var inputNameID = document.getElementById("input-name");
          if (inputNameID) {
            inputNameID.focus();
          }
        }, 250);
      }
    );
  };
  onCloseDialog = () => {
    this.setState({
      showDialog: false,
      formType: "add",
      formData: null,
      formIndex: -1,

      inputs: structuredClone(DEFAULT_INPUTS),
      errors: {},
    });
  };

  onFetchDetail = async () => {
    this.ref_Loading.onShowDialog();
    let response = await ApiHelper.get("kiosk/admin/etalase/detail", {
      id: this.state.formData.id,
    });

    let inputs = structuredClone(this.state.inputs);
    if (response.status === 200) {
      if (this.state.formType === "edit") {
        inputs = { ...structuredClone(inputs), ...response.results.data };
      } else if (this.state.formType === "add") {
        inputs.par_id = response.results.data.id;
        inputs.par_object = {
          id: response.results.data.id,
          name: response.results.data.name,
          image_url: response.results.data.image_url,
        };
      }
    } else {
      this.onNotify(response.message, "error");
      this.onCloseDialog();
    }
    this.ref_Loading.onCloseDialog();
    this.setState({ inputs });
  };

  // ====================================================================================
  // ========== ACTION LISTENERS ========================================================
  // ====================================================================================
  onNotify = (message, severity) => {
    if (this.ref_MySnackbar) {
      this.ref_MySnackbar.onNotify(message, severity);
    }
  };
  onTextInputListeners = (text, input) => {
    let inputs = this.state.inputs;
    inputs[input] = text;
    this.setState((prevState) => ({ ...prevState, inputs }));
  };
  onTextErrorListeners = (error, input) => {
    let errors = this.state.errors;
    errors[input] = error;
    this.setState((prevState) => ({ ...prevState, errors }));
  };
  onValidateListeners = () => {
    if (this.state.formType === "add" || this.state.formType === "edit") {
      let inputs = this.state.inputs;
      let isValid = true;

      let errorArr = [];
      if (!inputs.name) {
        this.onTextErrorListeners("Harus diisi", "name");
        errorArr.push("Nama Kategori Produk harus diisi.");
      }

      if (errorArr.length > 0) {
        this.onNotify(errorArr.join("\n"), "error");
        return;
      }
      this.actOnSaveListeners();
    }
  };
  actOnSaveListeners = async () => {
    this.ref_Loading.onShowDialog();

    let params = structuredClone(this.state.inputs);

    const response = await ApiHelper.post("kiosk/admin/etalase/save", params);

    if (response.status === 200) {
      const { onResults } = this.props;
      if (onResults) {
        onResults(
          this.state.formType,
          this.state.formData,
          this.state.formIndex
        );
      }
      this.onCloseDialog();

      this.onNotify(response?.message || "Berhasil Simpan Data", "success");
    } else {
      this.onNotify(response?.message || "Terjadi Kesalahan", "error");
    }

    this.ref_Loading.onCloseDialog();
  };

  // ====================================================================================
  // ========== RENDER SECTIONS =========================================================
  // ====================================================================================
  render() {
    let addModalClass = "small";
    return (
      <>
        <Modal open={this.state.showDialog} onClose={this.onCloseDialog}>
          <Box
            sx={{
              flex: 1,
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              display: "flex",
              width: "100wh",
              height: "100vh",
            }}
          >
            <div className={`modal-content ${addModalClass}`}>
              <div className="modal-header">{this.renderHeader()}</div>
              <div className="modal-body">{this.renderBody()}</div>
              <div className="modal-footer">
                {this.renderFooter()}
                <button
                  className="button cancel"
                  onClick={() => this.onCloseDialog()}
                >
                  <i className="ph ph-bold ph-x-circle"></i>
                  <span>
                    {this.state.formType === "info" ? "Tutup" : "Batal"}
                  </span>
                </button>
              </div>
            </div>
          </Box>
        </Modal>

        {/* Loading dialog */}
        <Loading ref={(value) => (this.ref_Loading = value)} />
        <MySnackbar ref={(value) => (this.ref_MySnackbar = value)} />
      </>
    );
  }

  renderHeader() {
    return (
      <>
        <div className="title">
          {this.state.formType === "add" && `Tambah Data Kategori Produk`}
          {this.state.formType === "edit" && `Ubah Data Kategori Produk`}
        </div>
        <span className="close" onClick={() => this.onCloseDialog()}>
          &times;
        </span>
      </>
    );
  }

  renderBody() {
    return (
      <>
        {(this.state.formType === "add" || this.state.formType === "edit") &&
          this.renderForm()}
      </>
    );
  }

  renderForm() {
    return (
      <>
        <div className="flex-rows no-right">
          <div className="row no-border">
            {/* === PARENT */}
            {Number(this.state.inputs.par_id) > 0 &&
              this.state.inputs.par_object && (
                <div className="input-form">
                  <div className="label">Kategori Induk</div>
                  <div className="mt-1 flex flex-col gap-4 py-4">
                    <div className="flex flex-col gap-4 ">
                      {/* Image */}
                      {/* <div className="detail_container">
                        <div className="form-image">
                          <div className="image">
                            <img
                              alt={"Kategori Induk"}
                              src={
                                this.state.inputs.par_object.image_url ||
                                Constants.image_default.empty
                              }
                            />
                          </div>
                        </div>
                      </div> */}
                      <div className="detail_container">
                        <em>Nama Kategori</em>
                        <div className="content text">
                          {this.state.inputs.par_object.name}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

            {/* === INPUT IMAGE_URL */}
            {/* <div className="input-form">
              <ImageUploadSingle
                id={"image_url"}
                title={"Foto Kategori Produk"}
                placeholder={"Foto Kategori Produk..."}
                image_url={this.state.inputs.image_url}
                error={this.state.errors.image_url}
                onCompleted={(image_url) => {
                  this.onTextInputListeners(image_url, "image_url");
                  this.onTextErrorListeners(null, "image_url");
                }}
                style={{ height: "200px" }}
                optional
              />
            </div> */}

            {/*=== INPUT NAME */}
            <div className="input-form">
              <Input
                id="input-name"
                label="Nama Kategori Produk"
                placeholder="Tuliskan disini..."
                value={this.state.inputs.name}
                onChange={(event) => {
                  if (
                    event.target !== undefined &&
                    event.target.value !== undefined
                  ) {
                    if (event.target.value.length <= 50) {
                      this.onTextInputListeners(event.target.value, "name");
                    }
                  }
                }}
                error={
                  this.state.errors.name !== undefined &&
                  this.state.errors.name !== null
                    ? this.state.errors.name
                    : null
                }
                onFocus={() => this.onTextErrorListeners(null, "name")}
                maxLength={50}
                required
              />
            </div>
          </div>
        </div>
      </>
    );
  }

  renderFooter() {
    return (
      <>
        <button
          className={`button ${this.state.formType === "edit" && "warning"} ${
            this.state.formType === "delete" && "danger"
          }`}
          onClick={() => {
            this.onValidateListeners();
          }}
        >
          {this.state.formType === "add" && (
            <i className="ph ph-bold ph-check-circle"></i>
          )}
          {this.state.formType === "edit" && (
            <i className="ph ph-bold ph-pencil"></i>
          )}
          <span>
            {this.state.formType === "add" && "Tambah Data"}
            {this.state.formType === "edit" && "Ubah Data"}
          </span>
        </button>
      </>
    );
  }
}
