/*
Created by esoda
Created on Apr, 2025
Contact esoda.id
*/

import React from "react";
import styles from "@/styles/Pos.module.css";
import Router from "next/router";
import Skeleton from "@mui/material/Skeleton";

export default class AdminSideBar extends React.Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  // ====================================================================================
  // ========== INITIALIZE, GET DATA ====================================================
  // ====================================================================================

  // ====================================================================================
  // ========== ACTION LISTENERS ========================================================
  // ====================================================================================

  // ====================================================================================
  // ========== RENDER SECTIONS =========================================================
  // ====================================================================================
  render() {
    const { isAuthLoading, menu } = this.props;
    return (
      <div className={styles.nav}>
        <div className={styles.items}>
          <div
            className={styles.menu}
            onClick={() => Router.replace("/adm/home")}
          >
            <i className="ph ph-bold ph-arrow-left"></i>Menu Utama
          </div>
        </div>
        {this.renderLoading()}
        {this.renderMenu()}
      </div>
    );
  }

  renderLoading() {
    if (!this.props.isAuthLoading) {
      return null;
    }

    return (
      <div className={styles.items}>
        {[...Array(4)].map((_, index) => (
          <div key={`skeleton-1-${index}`} className={styles.items}>
            <Skeleton variant="text" width={100} height={24} sx={{ mt: 1 }} />
            <ul>
              {[...Array(3)].map((_, index) => (
                <li key={`skeleton-2-${index}`}>
                  <Skeleton
                    variant="text"
                    width={100}
                    height={24}
                    sx={{ mt: 1 }}
                  />
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>
    );
  }

  renderMenu() {
    if (this.props.isAuthLoading) {
      return null;
    }

    return (
      <>
        {this.props.menu?.map((item, index) => {
          let isActive = false;
          if (window.location.pathname === item.link) {
            isActive = true;
          }
          return (
            <div key={`menu-${index}`} className={`${styles.items}`}>
              <div className={styles.items}>
                <div
                  className={`${styles.menu} ${isActive && styles.active}`}
                  onClick={() => {
                    if (item.children.length <= 0) {
                      Router.push(item.link);
                    }
                  }}
                >
                  <i className={item.icon_name}></i>
                  {item.name}
                </div>
                {item.children?.length > 0 && (
                  <>
                    <ul style={{ paddingLeft: "0px !important" }}>
                      {item.children?.map((child, indexChild) => (
                        <React.Fragment key={indexChild}>
                          {this.renderMenuChild(
                            child,
                            `${index}-${indexChild}`
                          )}
                        </React.Fragment>
                      ))}
                    </ul>
                  </>
                )}
              </div>
            </div>
          );
        })}
      </>
    );
  }

  renderMenuChild(item, index) {
    return (
      <>
        <li
          key={`child-${index}`}
          className={`${styles.items} ${
            this.state.selectedMenuId === item.id && styles.active
          }`}
          onClick={() => {
            this.setState({ selectedMenuId: item.id }, () => {
              if (item.children.length <= 0) {
                Router.push(item.link);
              }
            });
          }}
        >
          {item.name}
        </li>

        {item.children?.length > 0 && (
          <ul>
            {item.children?.map((child, indexChild) => (
              <>{this.renderMenuChild(child, `${index}-${indexChild}`)}</>
            ))}
          </ul>
        )}
      </>
    );
  }
}
