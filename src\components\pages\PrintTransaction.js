import CommonHelper from "@/utils/CommonHelper";
import { Avatar, Divider, Typography } from "@mui/material";
import moment from "moment";
import React, { Component, useRef, useState, useEffect } from "react";
import ReactToPrint, {
  PrintContextConsumer,
  useReactToPrint,
} from "react-to-print";

const ComponentToPrint = React.forwardRef((props, ref) => {
  const [checked, setChecked] = useState(false);
  const { data } = props;
  const transactionData = data || null;

  useEffect(() => {
    // componentDidMount equivalent
  }, []);

  if (!transactionData) {
    return <></>;
  }

  const renderItemList = (item, index) => {
    let itemName = item.name;
    if (item.code) {
      itemName = `[${item.code}] ${itemName}`;
    }

    let formatSellingPrice = CommonHelper.formatNumberSuffix(
      Number(item?.selling_price),
      { useShortForm: true },
      "Rp"
    );

    let formatTotal = CommonHelper.formatNumberSuffix(
      Number(item?.total_selling_price),
      { useShortForm: true },
      "Rp"
    );

    return (
      <div key={index} className="flex justify-between items-end gap-1 ml-2">
        <div className="flex-2">
          <Typography sx={{ ...inlineStyle.textItem }}>{itemName}</Typography>
        </div>
        <div className="flex-1 text-right">
          <div>
            <Typography sx={{ ...inlineStyle.textItem }}>
              {`@${formatSellingPrice}`}
            </Typography>
          </div>
        </div>
        <div className="flex-1 text-right">
          <Typography sx={{ ...inlineStyle.textItem }}>
            {`${CommonHelper.formatNumber(item.quantity)} ${item.unit_name}`}
          </Typography>
        </div>
        <div className="flex-1 text-right">
          <Typography sx={{ ...inlineStyle.textItem }}>
            {formatTotal}
          </Typography>
        </div>
      </div>
    );
  };

  const renderHeader = () => {
    if (!data) {
      return <></>;
    }

    return (
      <>
        <div className="flex justify-center">
          <div>
            <Typography sx={{ ...inlineStyle.textBig }}>ELS KIOSK</Typography>
          </div>
        </div>
        <div>
          <Divider sx={{ ...inlineStyle.divider }} />
        </div>
      </>
    );
  };

  const renderFooter = () => {
    return (
      <>
        <div className="flex justify-center">
          <div>
            <Typography sx={{ ...inlineStyle.textSubHeader }}>
              Terima Kasih
            </Typography>
          </div>
        </div>
        <div>
          <Divider sx={{ ...inlineStyle.divider }} />
        </div>
      </>
    );
  };

  const renderTotalTransaction = () => {
    let formatSubTotal = CommonHelper.formatNumber(
      Number(transactionData?.total_product_selling_price),
      "idr"
    );

    let formatDiscount = CommonHelper.formatNumberSuffix(
      Number(transactionData?.total_discount_nominal),
      { useShortForm: true },
      "Rp"
    );

    let formatService = CommonHelper.formatNumberSuffix(
      Number(transactionData?.charge_nominal),
      { useShortForm: true },
      "Rp"
    );

    let formatTax = CommonHelper.formatNumberSuffix(
      Number(transactionData?.tax_nominal),
      { useShortForm: true },
      "Rp"
    );

    let formatOtherPrice = CommonHelper.formatNumberSuffix(
      Number(transactionData?.other_cost_nominal),
      { useShortForm: true },
      "Rp"
    );

    let formatAddedRound = CommonHelper.formatNumberSuffix(
      Number(transactionData?.added_round_nominal_grand_total),
      { useShortForm: true },
      "Rp"
    );

    let formatGrandTotal = CommonHelper.formatNumber(
      Number(transactionData?.grand_total),
      "idr"
    );

    return (
      <>
        <div className="flex justify-between">
          <div>
            <Typography sx={{ ...inlineStyle.textLabel }}>Subtotal:</Typography>
          </div>
          <div>
            <Typography sx={{ ...inlineStyle.text }}>
              {formatSubTotal}
            </Typography>
          </div>
        </div>
        {transactionData.total_discount_nominal > 0 && (
          <div className="flex justify-between">
            <div>
              <Typography sx={{ ...inlineStyle.textLabel }}>Diskon:</Typography>
            </div>
            <div>
              <Typography sx={{ ...inlineStyle.text }}>
                {formatDiscount}
              </Typography>
            </div>
          </div>
        )}
        {transactionData.charge_nominal > 0 && (
          <div className="flex justify-between">
            <div>
              <Typography sx={{ ...inlineStyle.textLabel }}>
                Biaya Layanan:
              </Typography>
            </div>
            <div>
              <Typography sx={{ ...inlineStyle.text }}>
                {formatService}
              </Typography>
            </div>
          </div>
        )}
        {transactionData.tax_nominal > 0 && (
          <div className="flex justify-between">
            <div>
              <Typography sx={{ ...inlineStyle.textLabel }}>Pajak:</Typography>
            </div>
            <div>
              <Typography sx={{ ...inlineStyle.text }}>{formatTax}</Typography>
            </div>
          </div>
        )}
        {transactionData.other_cost_nominal > 0 && (
          <div className="flex justify-between">
            <div>
              <Typography sx={{ ...inlineStyle.textLabel }}>
                {transactionData.other_cost_label}:
              </Typography>
            </div>
            <div>
              <Typography sx={{ ...inlineStyle.text }}>
                {formatOtherPrice}
              </Typography>
            </div>
          </div>
        )}
        <div>
          <Divider sx={{ ...inlineStyle.divider }} />
        </div>
        <div className="flex flex-col items-center">
          <div>
            <Typography sx={{ ...inlineStyle.textBig }}>TOTAL</Typography>
          </div>
          <div>
            <Typography sx={{ ...inlineStyle.textBig }}>
              {formatGrandTotal}
            </Typography>
          </div>
        </div>
      </>
    );
  };

  const renderPayment = () => {
    if (!transactionData) {
      return <></>;
    }

    let formatPaid = CommonHelper.formatNumber(
      Number(transactionData?.grand_total) +
        Number(transactionData?.kiosk_mdr) +
        Number(transactionData?.kiosk_service_fee),
      "idr"
    );

    let textLabelBayar = "Dibayar";
    if (transactionData.kiosk_payment_method_label) {
      textLabelBayar = transactionData.kiosk_payment_method_label;
    }

    return (
      <>
        {Number(transactionData?.kiosk_mdr) > 0 && (
          <div className="flex justify-between">
            <div>
              <Typography sx={{ ...inlineStyle.textLabel }}>MDR:</Typography>
            </div>
            <div>
              <Typography sx={{ ...inlineStyle.text }}>
                {CommonHelper.formatNumber(
                  Number(transactionData?.kiosk_mdr) +
                    Number(transactionData?.kiosk_service_fee),
                  "idr"
                )}
              </Typography>
            </div>
          </div>
        )}
        <div className="flex justify-between items-center">
          <div>
            <Typography sx={{ ...inlineStyle.textPayment }}>
              {textLabelBayar}:
            </Typography>
          </div>
          <div>
            <Typography sx={{ ...inlineStyle.textPayment }}>
              {formatPaid}
            </Typography>
          </div>
        </div>
        {transactionData.kiosk_payment_method_object?.bank_transfer
          ?.va_number && (
          <div className="flex justify-between items-center">
            <div>
              <Typography sx={{ ...inlineStyle.textLabel }}>No. VA:</Typography>
            </div>
            <div>
              <Typography sx={{ ...inlineStyle.text }}>
                {
                  transactionData.kiosk_payment_method_object.bank_transfer
                    .va_number
                }
              </Typography>
            </div>
          </div>
        )}
        <div>
          <Divider sx={{ ...inlineStyle.divider }} />
        </div>
      </>
    );
  };

  return (
    <div ref={ref}>
      <div className="grid gap-1 p-4">
        {renderHeader()}
        {/* DETAIL TRANSAKSI */}
        <div className="flex justify-between">
          <div>
            <Typography sx={{ ...inlineStyle.textLabel }}>Tanggal:</Typography>
          </div>
          <div>
            <Typography sx={{ ...inlineStyle.text }}>
              {moment(transactionData.input_datetime).format(
                "DD MMM YYYY HH:mm"
              )}
            </Typography>
          </div>
        </div>
        <div className="flex justify-between">
          <div>
            <Typography sx={{ ...inlineStyle.textLabel }}>
              No. Transaksi:
            </Typography>
          </div>
          <div>
            <Typography sx={{ ...inlineStyle.text }}>
              {transactionData.number}
            </Typography>
          </div>
        </div>
        <div className="flex justify-between">
          <div>
            <Typography sx={{ ...inlineStyle.textLabel }}>Member:</Typography>
          </div>
          <div>
            <Typography sx={{ ...inlineStyle.text }}>
              {transactionData.customer_name || "-"}
            </Typography>
          </div>
        </div>
        <div className="flex justify-between">
          <div>
            <Typography sx={{ ...inlineStyle.textLabel }}>
              Kode Member:
            </Typography>
          </div>
          <div>
            <Typography sx={{ ...inlineStyle.text }}>
              {transactionData.customer_code || "-"}
            </Typography>
          </div>
        </div>
        <div className="flex justify-between">
          <div>
            <Typography sx={{ ...inlineStyle.textLabel }}>
              Affiliate:
            </Typography>
          </div>
          <div>
            <Typography sx={{ ...inlineStyle.text }}>
              {transactionData.affiliate_name || "-"} (
              {transactionData.affiliate_code || "-"})
            </Typography>
          </div>
        </div>
        <div>
          <Divider sx={{ ...inlineStyle.divider }} />
        </div>
        {/* ITEM TRANSAKSI */}
        <div className="flex flex-col gap-1">
          {transactionData.product_array &&
            transactionData.product_array.map((item, index) => {
              return renderItemList(item, index);
            })}
        </div>
        <div>
          <Divider sx={{ ...inlineStyle.divider }} />
        </div>
        {/* TOTAL TRANSAKSI */}
        {renderTotalTransaction()}
        <div>
          <Divider sx={{ ...inlineStyle.divider }} />
        </div>
        {renderPayment()}
        {renderFooter()}
      </div>
    </div>
  );
});

const PrintTransaction = ({ data, showPreview = false, children, button }) => {
  const componentRef = React.useRef(null);

  const reactToPrintContent = () => {
    return componentRef.current;
  };

  const handlePrint = useReactToPrint({
    // documentTitle: "SuperFileName",
    // fonts: CUSTOM_FONTS,
  });

  const onPrint = () => {
    handlePrint(reactToPrintContent);
  };

  return (
    <>
      {button && <>{React.cloneElement(button, { onClick: onPrint })}</>}
      <div style={{ display: showPreview ? "block" : "none" }}>
        <ComponentToPrint ref={componentRef} data={data} />
      </div>
    </>
  );

  return (
    <div>
      <ReactToPrint
        ref={reactToPrintRef}
        content={() => componentRef.current}
        trigger={() => {
          return <a href="#">{children}</a>;
        }}
        pageStyle="
          @media print {
            .scroll-container {
              overflow: visible;
              height: fit-content;
            }
          }
        "
      />
      <div style={{ display: showPreview ? "block" : "none" }}>
        <ComponentToPrint ref={componentRef} data={data} />
      </div>
    </div>
  );
};

export default PrintTransaction;

const inlineStyle = {
  containerText: {
    display: "flex",
    justifyContent: "space-between",
  },
  divider: {
    borderStyle: "dashed",
    borderColor: "black",
  },
  textLabel: {
    fontSize: "10px",
    color: "black",
  },
  text: {
    fontSize: "10px",
    color: "black",
  },
  textItem: {
    fontSize: "8px",
    color: "black",
  },
  textBig: {
    fontSize: "14px",
    fontWeight: "bold",
    color: "black",
  },
  textSubHeader: {
    fontSize: "12px",
    color: "black",
  },
  textPayment: {
    fontSize: "12px",
    fontWeight: "bold",
    color: "black",
  },
};
