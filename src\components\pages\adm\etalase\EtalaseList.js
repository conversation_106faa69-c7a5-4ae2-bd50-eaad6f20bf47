// import
import React from "react";
import stylesTable from "@/styles/Table.module.css";
import Table from "@/components/libs/Table";
import Loading from "@/components/modal/Loading";
import MySnackbar from "@/components/MySnackbar";
import ModalConfirmation from "@/components/modal/ModalConfirmation";
import EtalaseModalAddUpdate from "@/components/pages/adm/etalase/EtalaseModalAddUpdate";
import ApiHelper from "@/utils/ApiHelper";
import Constants from "@/utils/Constants";
import Tooltip from "@mui/material/Tooltip";
import IconButton from "@mui/material/IconButton";
import Switch from "@mui/material/Switch";
import CommonHelper from "@/utils/CommonHelper";
import Router from "next/router";

const ARR_HEADING = [
  {
    key: "",
    label: "",
    className: "text-center sticky_thead",
    sort: "",
    sortable: false,
  },
  {
    key: "name",
    label: "Nama Kategori Produk",
    className: "",
    sort: "asc",
    sortable: true,
  },
  {
    key: "product_count",
    label: "Produk Di Kategori Ini",
    className: "",
    sort: "",
    sortable: true,
  },
];

export default class EtalaseList extends React.Component {
  constructor(props) {
    super(props);
    this.ref_Table = React.createRef();
    this.state = {
      isFetched: true,
      fetchData: [],
      fetchDataShow: 0,
      fetchDataTotal: 0,
      fetchDataLimit: 10,
      fetchPageSelected: 1,
      fetchPageTotal: 0,
      inputSearch: "",
      inputSort: "name",
      inputFilter: [],

      sub: [],
    };
  }

  componentDidMount() {
    // ...existing code...
  }

  onInit = () => {
    let params = CommonHelper.getAllURLParams();
    if (params?.sub) {
      // decode base64
      params.sub = JSON.parse(CommonHelper.base64UrlDecode(params.sub));
    } else {
      params.sub = [];
    }

    this.setState(
      {
        inputSearch: "",
        inputSort: "",
        inputFilter: [],

        sub: params.sub,
      },
      () => {
        this.onRefresh();
      }
    );
  };

  onRefresh = () => {
    this.setState(
      {
        isFetched: true,
        fetchData: [],
        fetchDataShow: 0,
        fetchDataTotal: 0,
        fetchDataLimit: 10,
        fetchPageSelected: 1,
        fetchPageTotal: 1,
      },
      this.onFetchData
    );
  };

  onFetchData = async (isPageClicked = false) => {
    if (isPageClicked) {
      this.setState({ isFetched: true });
    }

    let params = {
      limit: this.state.fetchDataLimit,
      page: this.state.fetchPageSelected,
      search: this.state.inputSearch,
      sort: this.state.inputSort,
      pagination_bool: true,
    };
    if (this.state.sub.length > 0) {
      let lastSub = this.state.sub[this.state.sub.length - 1];
      params.par_id = lastSub.id;
    } else {
      params.par_id = 0;
    }
    let response = await ApiHelper.get("kiosk/admin/etalase/data", params);

    if (response.status === 200) {
      let fetchData = response.results.data;
      let pagination = response.results.pagination;

      this.setState(
        {
          isFetched: false,
          fetchData: response.results.data,
          fetchDataShow:
            this.state.fetchDataShow +
            response.results.pagination.total_display,
          fetchDataTotal: response.results.pagination.total_data,
        },
        () => {
          if (
            pagination.total_page !== 0 &&
            pagination.current > pagination.total_page
          ) {
            this.onRefresh();
          }
        }
      );
    } else {
      this.setState({
        isFetched: false,
        fetchData: [],
        fetchDataShow: 0,
        fetchDataTotal: 0,
      });
    }
  };

  handleSub;

  onSubmitActivate = async (item, index) => {
    this.ref_Loading.onShowDialog();
    const response = await ApiHelper.post("kiosk/admin/etalase/activate", {
      id: item.id,
      active_bool: !item.active_bool,
    });
    if (response.status === 200) {
      this.onRefresh();
      this.ref_ModalConfirmation.onCloseDialog();
    } else {
      this.ref_MySnackbar.onNotify(
        response?.message || "Terjadi Kesalahan",
        "error"
      );
    }
    this.ref_Loading.onCloseDialog();
  };

  onSubmitDelete = async (item, index) => {
    this.ref_Loading.onShowDialog();
    const response = await ApiHelper.post("kiosk/admin/etalase/delete", {
      id: item.id,
    });
    if (response.status === 200) {
      this.onRefresh();
      this.ref_ModalConfirmation.onCloseDialog();
    } else {
      this.ref_MySnackbar.onNotify(
        response?.message || "Terjadi Kesalahan",
        "error"
      );
    }
    this.ref_Loading.onCloseDialog();
  };

  onBackSubCategory = (item, index) => {
    let params = CommonHelper.getAllURLParams();
    if (params?.sub) {
      // decode base64
      params.sub = JSON.parse(atob(params.sub));
    } else {
      params.sub = [{ id: "0", name: "Kategori Utama" }];
    }

    params.sub = params.sub.slice(0, index + 1);
    console.log(params.sub);

    this.setState({ sub: params.sub }, () => {
      Router.push(
        {
          pathname: window.location.pathname,
          query: {
            sub: CommonHelper.base64UrlEncode(JSON.stringify(params.sub)),
          },
        },
        undefined,
        { shallow: true }
      );
      this.onRefresh();
    });
  };

  onViewSubCategory = (item, index) => {
    let params = CommonHelper.getAllURLParams();
    if (params?.sub) {
      // decode base64
      params.sub = JSON.parse(atob(params.sub));
    } else {
      params.sub = [{ id: "0", name: "Kategori Utama" }];
    }

    params.sub.push({ id: item.id, name: item.name });

    this.setState({ sub: params.sub }, () => {
      Router.push(
        {
          pathname: window.location.pathname,
          query: {
            sub: CommonHelper.base64UrlEncode(JSON.stringify(params.sub)),
          },
        },
        undefined,
        { shallow: true }
      );
      this.onRefresh();
    });
  };

  render() {
    return (
      <>
        <Table
          renderHeadTop={() => {
            return (
              <>
                {this.state.sub.length > 1 && (
                  <div
                    className="breadcrumb"
                    style={{
                      background: "white",
                      padding: "0rem 0rem 1rem 0rem",
                    }}
                  >
                    <ul>
                      {this.state.sub.map((item, index) => {
                        return (
                          <li
                            key={index}
                            onClick={() => {
                              this.onBackSubCategory(item, index);
                            }}
                          >
                            {item.name}
                          </li>
                        );
                      })}
                    </ul>
                  </div>
                )}
              </>
            );
          }}
          ref={(value) => (this.ref_Table = value)}
          customTableContainers={stylesTable.withTab}
          disableTitle={true}
          title={this.props.selectedTabTitle}
          subtitle={"List data dan kelola data etalase"}
          addTitle={"Data Baru"}
          addListeners={() => {
            if (this.ref_EtalaseModalAddUpdate?.onShowDialog) {
              let item = null;
              if (this.state.sub.length > 0) {
                item = this.state.sub[this.state.sub.length - 1];
              }
              this.ref_EtalaseModalAddUpdate.onShowDialog("add", item, -1);
            }
          }}
          searchListeners={(inputSearch) => {
            this.ref_Table.setSearchValue(inputSearch);
            this.setState({ inputSearch }, () => {
              this.onRefresh();
            });
          }}
          filterListeners={() => {
            alert(`Filter Callback`);
          }}
          exportListeners={() => {
            alert(`Export Callback`);
          }}
          inputSearch={this.state.inputSearch}
          onSearch={(inputSearch) => {
            this.ref_Table.setSearchValue(inputSearch);
            this.setState({ inputSearch }, () => {
              this.onRefresh();
            });
          }}
          disabledBtnFilter
          disabledBtnExport
          sortListeners={(inputSort) => {
            this.setState({ inputSort }, () => {
              this.onRefresh();
            });
          }}
          dataHeadings={ARR_HEADING}
          dataTables={this.state.fetchData}
          renderItems={this.renderItems}
          pageCount={this.state.fetchPageTotal}
          pageSelected={this.state.fetchPageSelected}
          pageListeners={(fetchPageSelected) => {
            this.setState({ fetchPageSelected }, () => this.onFetchData(true));
          }}
          pageRow={this.state.fetchDataLimit}
          pageRowListeners={(fetchDataLimit) => {
            this.setState({ fetchDataLimit }, () => this.onFetchData(true));
          }}
          onReload={this.onRefresh}
          isFetched={this.state.isFetched || this.props.isAuthLoading}
        />

        <Loading ref={(value) => (this.ref_Loading = value)} />
        <MySnackbar ref={(value) => (this.ref_MySnackbar = value)} />
        <ModalConfirmation
          ref={(value) => (this.ref_ModalConfirmation = value)}
        />

        <EtalaseModalAddUpdate
          ref={(value) => (this.ref_EtalaseModalAddUpdate = value)}
          onResults={(formType, formData, formIndex) => {
            this.onRefresh();
          }}
        />
      </>
    );
  }

  renderItems = (item, index) => {
    return (
      <tr key={index} style={{}}>
        <td className="text-center">
          <div className="actions">
            <Tooltip title="Ubah Data">
              <IconButton
                onClick={() => {
                  this.ref_EtalaseModalAddUpdate.onShowDialog(
                    "edit",
                    item,
                    index
                  );
                }}
              >
                <i className="ph ph-bold ph-pencil-simple text-blue"></i>
              </IconButton>
            </Tooltip>
            <Tooltip title="Hapus Data">
              <IconButton
                onClick={() => {
                  this.ref_ModalConfirmation.onShowDialog("delete", {
                    text: {
                      title: "Pengguna",
                      action: "Hapus",
                      info: item.name,
                    },
                    onConfirmed: (formType, formData, formIndex) => {
                      this.onSubmitDelete(item, index);
                    },
                  });
                }}
              >
                <i className="ph ph-bold ph-trash-simple text-red"></i>
              </IconButton>
            </Tooltip>
            {/* Sub Kategori */}
            <Tooltip title="Tambah Sub Kategori">
              <IconButton
                onClick={() => {
                  this.ref_EtalaseModalAddUpdate.onShowDialog(
                    "add",
                    item,
                    index
                  );
                }}
              >
                {/* add circle */}
                <i className="ph ph-bold ph-plus-circle text-green"></i>
              </IconButton>
            </Tooltip>
            {/* Lihat Sub Kategori */}
            {item.have_child_bool && (
              <Tooltip title="Lihat Sub Kategori">
                <IconButton
                  onClick={() => {
                    this.onViewSubCategory(item, index);
                  }}
                >
                  <i className="ph ph-bold ph-eye text-blue"></i>
                </IconButton>
              </Tooltip>
            )}
          </div>
        </td>
        <td style={{ width: "100%" }}>
          {/* <img
            src={item.image_url || Constants.image_default.empty}
            style={{
              border: "none",
              backgroundColor: "transparent",
              width: 30,
              height: 30,
              objectFit: "contain",
            }}
          /> */}
          {item.name || "-"}
        </td>
        <td style={{ textAlign: "end" }}>
          {item.product_count
            ? `${CommonHelper.formatNumber(item.product_count)} produk`
            : "0 produk"}
        </td>
      </tr>
    );
  };
}
