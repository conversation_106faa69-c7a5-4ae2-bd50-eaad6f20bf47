import { create } from "zustand";
import ApiHelper from "@/utils/ApiHelper";

const useTransactionHistoryStore = create((set, get) => ({
  // State
  transactions: [],
  isLoading: false,
  page: 1,
  totalPages: 1,
  searchQuery: "",
  selectedTabIdx: 0,
  selectedTabValue: "all",

  // Actions
  setLoading: (isLoading) => set({ isLoading }),
  setPage: (page) => set({ page }),
  setSearchQuery: (searchQuery) => set({ searchQuery }),
  setSelectedTab: (index, value) =>
    set({ selectedTabIdx: index, selectedTabValue: value }),

  // Fetch transactions
  fetchTransactions: async (customerId) => {
    const { page, selectedTabValue, searchQuery } = get();

    set({ isLoading: true });
    try {
      let params = {
        page,
        limit: 10,
        status: selectedTabValue !== "all" ? selectedTabValue : undefined,
        search: searchQuery,
        customer_id: customerId,
      };

      const response = await ApiHelper.get("kiosk/transaction/history", params);

      if (response.status === 200) {
        set({
          transactions: response.results.data,
          totalPages: Math.ceil(response.results.pagination.total_data / 10),
          isLoading: false,
        });
      } else {
      }
    } catch (error) {
      console.error("Error fetching transactions:", error);
      set({ isLoading: false });
    }
  },

  // Reset store
  reset: () =>
    set({
      transactions: [],
      isLoading: false,
      page: 1,
      totalPages: 1,
      searchQuery: "",
      selectedTabIdx: 0,
      selectedTabValue: "all",
    }),
}));

export default useTransactionHistoryStore;
