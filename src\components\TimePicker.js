import moment from "moment";
import React, { useEffect, useState } from "react";

const TimePicker = ({ addClass }) => {
  const [time, setTime] = useState(null);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
    setTime(new Date());

    const timerId = setInterval(() => {
      setTime(new Date());
    }, 1000);

    return () => {
      clearInterval(timerId);
    };
  }, []);
  if (!isClient || !time) {
    return (
      <div className={`time-picker ${addClass || ""}`}>Loading time...</div>
    );
  }

  return (
    <div className={`time-picker ${addClass || ""}`}>
      {moment(time).format("ll")}, {time.toLocaleTimeString()}
    </div>
  );
};

export default TimePicker;
