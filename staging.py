import os
import shutil
import subprocess

# Declare the DEPLOY_DIR and GIT_MESSAGE
CURRENT_DIR = "../els-kiosk"
DEPLOY_DIR = "../els-kiosk staging"
DEPLOY_RUN = "pnpm run build"
GIT_MESSAGE = "update"

# Clear dist folder first
dist_dir = 'dist'
if os.path.exists(dist_dir):
    shutil.rmtree(dist_dir)
    os.makedirs(dist_dir)

# Function to delete files and folders except specified ones
def delete_except(directory, exceptions):
    for item in os.listdir(directory):
        item_path = os.path.join(directory, item)
        if item not in exceptions:
            if os.path.isfile(item_path):
                os.unlink(item_path)
            elif os.path.isdir(item_path):
                shutil.rmtree(item_path)

# Run Next.js build command
subprocess.run(DEPLOY_RUN, shell=True, check=True)

# Add git pull before file operations
if os.path.exists(DEPLOY_DIR):
    os.chdir(DEPLOY_DIR)
    subprocess.run(["git", "pull", "origin", "staging"])
    os.chdir(CURRENT_DIR)  # Return to original directory

# Delete folder and file in DEPLOY_DIR except .git, .gitignore, and api
exceptions = ['.git', '.gitignore', 'api']
if os.path.exists(DEPLOY_DIR):
    delete_except(DEPLOY_DIR, exceptions)

# Move files and folders inside dist to DEPLOY_DIR and remove them from dist
if os.path.exists(dist_dir):
    for item in os.listdir(dist_dir):
        src = os.path.join(dist_dir, item)
        dst = os.path.join(DEPLOY_DIR, item)
        if os.path.isdir(src):
            shutil.copytree(src, dst, dirs_exist_ok=True)
            shutil.rmtree(src)
        else:
            shutil.copy2(src, dst)
            os.remove(src)

# Change to DEPLOY_DIR
os.chdir(DEPLOY_DIR)

# Run Git commands
subprocess.run(["git", "add", "."])
subprocess.run(["git", "commit", "-m", GIT_MESSAGE])
subprocess.run(["git", "push"])

print("Staging and Git operations completed successfully.")