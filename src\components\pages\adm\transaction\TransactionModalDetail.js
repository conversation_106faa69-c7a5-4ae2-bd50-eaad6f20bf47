import React from "react";
import { Modal, Box, Alert } from "@mui/material";
import Loading from "@/components/modal/Loading";
import ApiHelper from "@/utils/ApiHelper";
import MySnackbar from "@/components/MySnackbar";
import Image from "next/image";
import Constants from "@/utils/Constants";
import CommonHelper from "@/utils/CommonHelper";
import Table from "@/components/libs/Table";

export default class TransactionModalDetail extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showDialog: false,
      formType: "detail",
      formData: null,
      formIndex: -1,
      detail: null,
    };
  }

  onShowDialog = (formType, formData = null, formIndex = -1) => {
    this.setState(
      {
        formType,
        formData,
        formIndex,
        showDialog: true,
      },
      () => {
        this.onFetchDetail();
      }
    );
  };

  onCloseDialog = () => {
    this.setState({
      showDialog: false,
      formType: "detail",
      formData: null,
      formIndex: -1,
      detail: null,
    });
  };

  onFetchDetail = async () => {
    this.ref_Loading.onShowDialog();
    let response = await ApiHelper.get("kiosk/admin/transaction/detail", {
      id: this.state.formData.id,
    });

    let detail = null;
    if (response.status === 200) {
      detail = response.results.data;
    } else {
      this.onNotify(response.message, "error");
      this.onCloseDialog();
    }
    this.ref_Loading.onCloseDialog();
    this.setState({ detail });
  };

  onNotify = (message, severity = "info") => {
    this.ref_MySnackbar.onNotify(message, severity);
  };

  getProductTableHeadings = () => {
    return [
      {
        key: "serial_number",
        label: "Nomor Serial",
        className: "text-center wd80",
        sortable: false,
        sort: "",
      },
      {
        key: "name",
        label: "Nama Produk",
        className: "",
        sortable: false,
        sort: "",
      },
      {
        key: "code",
        label: "Kode",
        className: "wd120",
        sortable: false,
        sort: "",
      },
      {
        key: "category",
        label: "Kategori",
        className: "wd120",
        sortable: false,
        sort: "",
      },
      {
        key: "price",
        label: "Harga",
        className: "text-right wd120",
        sortable: false,
        sort: "",
      },
      {
        key: "quantity",
        label: "Jumlah",
        className: "text-center wd100",
        sortable: false,
        sort: "",
      },
      {
        key: "total",
        label: "Total",
        className: "text-right wd140",
        sortable: false,
        sort: "",
      },
    ];
  };

  renderProductTableItems = (product, index) => {
    return (
      <tr key={index}>
        <td className="text-left">{product.serial_number || "-"}</td>
        <td>
          <div>
            <div
              style={{
                fontSize: "1rem",
                fontWeight: "600",
                color: "#2d3436",
                marginBottom: "0.25rem",
              }}
              className="flex flex-row gap-4 items-center"
            >
              <Image
                src={product.image_url || Constants.image_default.empty}
                alt={product.name}
                width={60}
                height={60}
                style={{ objectFit: "contain" }}
              />
              {product.name}
            </div>
          </div>
        </td>
        <td>{product.code}</td>
        <td>{product.category_name}</td>
        <td className="text-right">
          {Number(product.selling_price) < 0 ? "-" : ""}
          Rp{CommonHelper.formatNumberDecimal(Math.abs(product.selling_price))}
        </td>
        <td className="text-center">
          {parseFloat(product.quantity)} {product.unit_name}
        </td>
        <td className="text-right">
          <span
            style={{
              color: "var(--base-color)",
              fontWeight: "600",
            }}
          >
            {Number(product.total_selling_price) < 0 ? "-" : ""}
            Rp
            {CommonHelper.formatNumberDecimal(
              Math.abs(product.total_selling_price)
            )}
          </span>
        </td>
      </tr>
    );
  };

  render() {
    let addModalClass = "large";
    return (
      <>
        <Modal open={this.state.showDialog} onClose={this.onCloseDialog}>
          <Box
            sx={{
              flex: 1,
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              display: "flex",
              width: "100wh",
              height: "100vh",
            }}
          >
            <div className={`modal-content ${addModalClass}`}>
              <div className="modal-header">{this.renderHeader()}</div>
              <div className="modal-body" style={{ height: "fit-content" }}>
                {this.renderBody()}
              </div>
              <div className="modal-footer">{this.renderFooter()}</div>
            </div>
          </Box>
        </Modal>

        <Loading ref={(value) => (this.ref_Loading = value)} />
        <MySnackbar ref={(value) => (this.ref_MySnackbar = value)} />
      </>
    );
  }

  renderHeader() {
    return (
      <>
        <div className="title">Detail Transaksi</div>
        <span className="close" onClick={() => this.onCloseDialog()}>
          &times;
        </span>
      </>
    );
  }

  renderBody() {
    return <>{this.renderTransactionDetail()}</>;
  }

  renderTransactionDetail() {
    let objData = this.state.detail;

    if (!objData) {
      return (
        <div className="flex-rows no-right">
          <div className="row no-border">
            <div className="input-form">
              <Alert severity="info">Memuat detail transaksi...</Alert>
            </div>
          </div>
        </div>
      );
    }

    return (
      <>
        <div className="flex-rows no-right">
          {/* Transaction Information */}
          <div className="row wd60 no-border">
            <div className="box">
              <div className="title flex flex-row gap-4 flex-wrap">
                <div>Informasi Transaksi</div>
                <div className="content">
                  <span
                    className={`status ${
                      objData.status === "verified" ? "" : "unavailable"
                    }`}
                  >
                    {objData.status === "verified"
                      ? "Terverifikasi"
                      : "Belum Verifikasi"}
                  </span>
                </div>
              </div>
              <div className="detail_wrapper_grid">
                <div className="detail_container_grid">
                  <em>Nomor Transaksi</em>
                  <div className="content text">{objData.number || "-"}</div>
                </div>
                <div className="detail_container_grid">
                  <em>Tanggal Input</em>
                  <div className="content text">
                    {objData.input_datetime || "-"}
                  </div>
                </div>
                <div className="detail_container_grid">
                  <em>Nama Pelanggan</em>
                  <div className="content text">
                    {objData.customer_name || "-"}
                  </div>
                </div>
                <div className="detail_container_grid">
                  <em>WhatsApp</em>
                  <div className="content text">
                    {objData.customer_whatsapp || "-"}
                  </div>
                </div>
                <div className="detail_container_grid">
                  <em>Afiliasi</em>
                  <div className="content text">
                    {objData.affiliate_name || "-"}
                  </div>
                </div>
                <div className="detail_container_grid">
                  <em>Kode Afiliasi</em>
                  <div className="content text">
                    {objData.affiliate_code || "-"}
                  </div>
                </div>
                <div className="detail_container_grid">
                  <em>Total Item</em>
                  <div className="content text">
                    {objData.total_product_item || "0"}
                  </div>
                </div>
                <div className="detail_container_grid">
                  <em>Grand Total</em>
                  <div className="content text">
                    {new Intl.NumberFormat("id-ID", {
                      style: "currency",
                      currency: "IDR",
                    }).format(objData.grand_total || 0)}
                  </div>
                </div>
              </div>
            </div>

            {/* Additional Transaction Details */}
            {objData.notes && (
              <div className="box mt-4">
                <div className="title">Catatan</div>
                <div className="detail_wrapper_grid">
                  <div className="detail_container_grid">
                    <div
                      className="content text"
                      style={{ whiteSpace: "pre-wrap" }}
                    >
                      {objData.notes}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Product Details */}
          <div className="row wd40 no-border">
            <div className="box">
              <div className="title">Detail Produk</div>
              {objData.product_array && objData.product_array.length > 0 ? (
                <div className="input-form">
                  <Table
                    title=""
                    disabledHeader={true}
                    disabledPage={true}
                    dataHeadings={this.getProductTableHeadings()}
                    dataTables={objData.product_array}
                    renderItems={(product, index) =>
                      this.renderProductTableItems(product, index)
                    }
                    isFetched={false}
                    customTableContainers="product-details-table"
                  />
                </div>
              ) : (
                <div className="input-form">
                  <Alert severity="info">
                    Tidak ada produk dalam transaksi ini
                  </Alert>
                </div>
              )}
            </div>
          </div>
        </div>
      </>
    );
  }

  renderFooter() {
    return (
      <>
        <button
          className="button cancel ml-0"
          onClick={() => this.onCloseDialog()}
        >
          <i className="ph ph-bold ph-x-circle"></i>
          <span>Tutup</span>
        </button>
      </>
    );
  }
}
