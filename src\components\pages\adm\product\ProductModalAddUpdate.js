import React from "react";
import {
  Modal,
  Box,
  TextField,
  InputAdornment,
  MenuItem,
  IconButton,
  Chip,
  Select,
  FormControl,
  InputLabel,
  Tooltip,
  Alert,
} from "@mui/material";
import Loading from "@/components/modal/Loading";
import Constants from "@/utils/Constants";
import ImageUploadSingle from "@/components/libs/ImageUploadSingle";
import ApiHelper from "@/utils/ApiHelper";
import MySnackbar from "@/components/MySnackbar";
import Input, { numberFormatIdToNumber } from "@/components/libs/Input";
import AdminModalListProductAccurate from "@/components/pages/adm/modal/AdminModalListProductAccurate";
import ImageUploadMultiple from "@/components/libs/ImageUploadMultiple";
import InputAutoComplete2 from "@/components/libs/InputAutoComplete2";
import Table from "@/components/libs/Table";

const DEFAULT_INPUTS = {
  id: "",
  product_id: "",
  product_object: null,
  par_id: "",
  par_object: null,
  main_image_id: "",
  main_image_index: "-1",
  main_image_image_url: "",
  image_array: [],
  etalase_array: [{ categories: [null] }],
  selling_price: "",
  selling_price_label: "0",
  onsale_bool: true,
  variant_array: [],
  etalase_suggestion: [],
};

export default class ProductModalAddUpdate extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showDialog: false,
      formType: "add",
      formData: null,
      formIndex: -1,

      inputs: structuredClone(DEFAULT_INPUTS),
      errors: {},
    };

    this.imageUploadIndex = -1;
  }

  onShowDialog = (
    formType,
    formData = null,
    formIndex = -1,
    formInputs = null
  ) => {
    let inputs = structuredClone(DEFAULT_INPUTS);
    if (formInputs) {
      inputs = formInputs;
    }
    this.setState(
      {
        formType,
        formData,
        formIndex,
        showDialog: true,

        inputs,
      },
      async () => {
        if (formType === "edit") {
          this.onFetchDetail();
        } else if (formType === "add") {
          this.onSelectProductListeners();
        }
      }
    );
  };

  onCloseDialog = () => {
    this.setState({
      showDialog: false,
      formType: "add",
      formData: null,
      formIndex: -1,

      inputs: structuredClone(DEFAULT_INPUTS),
      errors: {},
    });
  };

  onFetchDetail = async () => {
    this.ref_Loading.onShowDialog();
    let response = await ApiHelper.get("kiosk/admin/product/detail", {
      id: this.state.formData.id,
    });

    let inputs = structuredClone(this.state.inputs);
    if (response.status === 200) {
      if (this.state.formType === "edit") {
        inputs = { ...structuredClone(inputs), ...response.results.data };
      }
    } else {
      this.onNotify(response.message, "error");
      this.onCloseDialog();
    }
    this.ref_Loading.onCloseDialog();
    this.setState({ inputs }, async () => {
      await new Promise((resolve) => setTimeout(resolve, 200));
      // handle etalase
      if (inputs.etalase_array.length > 0) {
        inputs.etalase_array.map((item, index) => {
          item.categories.forEach((element, idx) => {
            if (element) {
              this[`ref_InputCategory${index}-${idx}`].setDefaultInputs(
                element.name
              );
            }
          });
          return item;
        });
      }
    });
  };

  onFetchDetailFromElsBySku = async (sku) => {
    this.onNotify("Sedang mengambil data dari ELS...", "info");

    this.ref_Loading.onShowDialog();
    let response = await ApiHelper.get(
      "kiosk/admin/product/els/detail/by-sku",
      {
        sku,
      }
    );

    let data = null;
    if (response.status === 200) {
      data = response.results.data;
      this.onNotify("Data dengan sku " + sku + " berhasil diambil.", "success");
    } else {
      this.onNotify("Data dengan sku " + sku + " tidak ditemukan.", "error");
    }
    this.ref_Loading.onCloseDialog();
    return data;
  };

  onNotify = (message, severity) => {
    if (this.ref_MySnackbar) {
      this.ref_MySnackbar.onNotify(message, severity);
    }
  };

  onTextInputListeners = (text, input) => {
    let inputs = this.state.inputs;
    inputs[input] = text;
    this.setState((prevState) => ({ ...prevState, inputs }));
  };

  onTextErrorListeners = (error, input) => {
    let errors = this.state.errors;
    errors[input] = error;
    this.setState((prevState) => ({ ...prevState, errors }));
  };

  onValidateListeners = () => {
    if (this.state.formType === "add" || this.state.formType === "edit") {
      let inputs = this.state.inputs;
      let isValid = true;

      let errorArr = [];

      if (!inputs.product_id) {
        this.onTextErrorListeners("Harus dipilih", "product_id");
        errorArr.push("Produk harus dipilih.");
      }
      if (inputs.etalase_array.length === 0) {
        this.onTextErrorListeners("Harus dipilih", "etalase_array");
        errorArr.push("Kategori Produk harus dipilih.");
      } else {
        inputs.etalase_array.map((etalase, index) => {
          if (etalase.categories.length === 0) {
            this.onTextErrorListeners("Harus dipilih", "etalase_array");
            errorArr.push("Kategori Produk harus dipilih.");
          } else if (etalase.categories[0] === null) {
            this.onTextErrorListeners("Harus dipilih", "etalase_array");
            errorArr.push("Kategori Produk harus dipilih.");
          }
        });
      }

      if (errorArr.length > 0) {
        this.onNotify(errorArr.join("\n"), "error");
        return;
      }
      this.actOnSaveListeners();
    }
  };

  actOnSaveListeners = async () => {
    this.ref_Loading.onShowDialog();

    let apiEndPoint = "kiosk/admin/product/create";
    if (this.state.formType === "edit") {
      apiEndPoint = "kiosk/admin/product/update";
    }

    let params = structuredClone(this.state.inputs);
    const response = await ApiHelper.post(apiEndPoint, params);

    if (response.status === 200) {
      const { onResults } = this.props;
      if (onResults) {
        onResults(
          this.state.formType,
          this.state.formData,
          this.state.formIndex
        );
      }
      this.onCloseDialog();

      this.onNotify(response?.message || "Berhasil Simpan Data", "success");
    } else {
      this.onNotify(response?.message || "Terjadi Kesalahan", "error");
    }

    this.ref_Loading.onCloseDialog();
  };

  onSelectProductListeners = () => {
    let selected = [];
    if (this.state.inputs.product_object !== null) {
      selected.push(this.state.inputs.product_object);
    }
    this.ref_AdminModalListProductAccurate.onShowDialog("list", {
      selected,
      apiParams: {
        aol_id: this.props.selected_aol.aol_id,
        aol_session_database: this.props.selected_aol.database,
        sort: "name",
        used_in_kiosk_bool: false,
      },
      showDatabase: false,
      propsInputs: {
        category: {
          apiParams: {
            aol_id: this.props.selected_aol.aol_id,
            aol_session_database: this.props.selected_aol.database,
            sort: "title,name",
          },
        },
        brand: {
          apiParams: {
            aol_id: this.props.selected_aol.aol_id,
            aol_session_database: this.props.selected_aol.database,
            sort: "name",
          },
        },
      },
      from: "product",
    });
  };

  onSelectProductVariantListeners = () => {
    let selected = [];
    let excludedProductIds = [this.state.inputs.product_id];
    this.state.inputs.variant_array.map((item) => {
      excludedProductIds.push(item.product_id);
      selected.push(item.product_object);
    });

    this.ref_AdminModalListProductAccurate.onShowDialog("list", {
      isMultiSelect: true,
      selected,
      apiParams: {
        aol_id: this.props.selected_aol.aol_id,
        aol_session_database: this.props.selected_aol.database,
        sort: "name",
        used_in_kiosk_bool: false,
        [`id[nin]`]: excludedProductIds.join(","),
      },
      showDatabase: false,
      propsInputs: {
        category: {
          apiParams: {
            aol_id: this.props.selected_aol.aol_id,
            aol_session_database: this.props.selected_aol.database,
            sort: "title,name",
          },
        },
        brand: {
          apiParams: {
            aol_id: this.props.selected_aol.aol_id,
            aol_session_database: this.props.selected_aol.database,
            sort: "name",
          },
        },
      },
      from: "variant",
    });
  };

  onProductVariantDeleteListeners = (item, index) => {
    let inputs = structuredClone(this.state.inputs);
    inputs.variant_array.splice(index, 1);
    this.setState({ inputs });
  };

  onChangeProductListeners = async (selectedProduct, formData) => {
    let inputs = structuredClone(this.state.inputs);

    if (formData?.from === "product") {
      let product_object = selectedProduct[0];

      let detail = null;
      if (product_object?.code) {
        detail = await this.onFetchDetailFromElsBySku(product_object.code);
        // on_sale
        // images
      }
      let image_array = [];
      if (detail?.images && detail?.images.length > 0) {
        detail.images.map((item) => {
          if (item?.src) {
            image_array.push({
              id: "",
              image_url: item.src,
            });
          }
        });
        inputs.main_image_id = "";
        inputs.main_image_index = 0;
        inputs.main_image_image_url = detail?.images[0]?.src;
      } else if (product_object?.image_url) {
        image_array.push({
          id: "",
          image_url: product_object.image_url,
        });
        inputs.main_image_id = product_object?.id;
        inputs.main_image_index = 0;
        inputs.main_image_image_url = product_object?.image_url;
      } else {
        inputs.main_image_id = "";
        inputs.main_image_index = -1;
        inputs.main_image_image_url = "";
      }
      let unit_selling_price = Number(product_object?.unit_selling_price);

      inputs.product_id = product_object?.id;
      inputs.product_object = product_object;
      inputs.par_id = "";
      inputs.par_object = null;
      inputs.image_array = image_array;
      inputs.etalase_array = [
        {
          categories: [null],
        },
      ];
      if (
        detail?.etalase_array[0] !== undefined &&
        detail?.etalase_array[0].categories.length > 0
      ) {
        inputs.etalase_array = detail?.etalase_array;
      }
      inputs.variant_array = [];
      inputs.selling_price = unit_selling_price;
      inputs.selling_price_label = unit_selling_price.toLocaleString("id-ID");
      inputs.onsale_bool = true;
      if (detail?.on_sale) {
        inputs.onsale_bool = detail?.on_sale;
      }
      inputs.etalase_suggestion = [];
      if (
        detail?.category_suggestions &&
        detail?.category_suggestions.length > 0
      ) {
        inputs.etalase_suggestion = detail?.category_suggestions;
      }
    } else if (formData?.from === "variant") {
      let variant_array = structuredClone(this.state.inputs.variant_array);
      selectedProduct.map((item) => {
        const findIndex = variant_array.findIndex((v) => v.id === item.id);
        if (findIndex === -1) {
          variant_array.push({
            id: "",
            product_id: item.id,
            product_object: item,
            par_id: this.state.inputs.product_id,
            par_object: structuredClone(this.state.inputs.product_object),
            main_image_id: "",
            main_image_index: "-1",
            main_image_image_url: "",
            selling_price: 0,
            selling_price_label: "0",
          });
        }
      });
      inputs.variant_array = variant_array;
    }

    this.setState({ inputs }, async () => {
      if (formData?.from === "product") {
        if (this[`ref_InputCategory0-0`] !== undefined) {
          this[`ref_InputCategory0-0`]?.setDefaultInputs("");
        }
      }
      if (inputs.etalase_array.length > 0) {
        inputs.etalase_array.map((item, index) => {
          item.categories.forEach((element, idx) => {
            if (element) {
              this[`ref_InputCategory${index}-${idx}`].setDefaultInputs(
                element.name
              );
            }
          });
          return item;
        });
      }
    });
  };

  onSelectProductCategory = (suggestion, index, suggestionIndex) => {
    let inputs = structuredClone(this.state.inputs);
    if (inputs.etalase_array[index].categories.length === 0) {
      return;
    }
    // last index
    let lastIndex = inputs.etalase_array[index].categories.length - 1;
    inputs.etalase_array[index].categories[lastIndex] = suggestion;
    inputs.etalase_array[index].categories.push(null);
    this[`ref_InputCategory${index}-${lastIndex}`].setDefaultInputs(
      suggestion.name
    );
    inputs.etalase_suggestion.splice(suggestionIndex, 1);

    this.setState({ inputs });
  };

  onPickImageListeners = (index) => {
    this.ref_ImageInput.click();
    this.imageUploadIndex = index;
  };
  onUploadImageListeners = async (file) => {
    if (file.size > 1000000) {
      this.onNotify("Ukuran gambar terlalu besar.", "error");
      return;
    }
    this.ref_Loading.onShowDialog();
    let formData = new FormData();
    formData.append("image", file);
    let response = await ApiHelper.uploadImage(formData);
    if (response.status === 200) {
      this.ref_Loading.onCloseDialog();

      let inputs = structuredClone(this.state.inputs);
      if (this.imageUploadIndex === -1) {
        inputs.image_array.push({
          id: "",
          image_url: response.data.fileuri,
        });
      } else {
        inputs.image_array[this.imageUploadIndex] = {
          id: "",
          image_url: response.data.fileuri,
        };
      }

      if (inputs.image_array.length === 1 && inputs.main_image_index === "") {
        inputs.main_image_id = inputs.image_array[0].id;
        inputs.main_image_index = 0;
        inputs.main_image_image_url = response.data.fileuri;
      }
      this.setState({ inputs });
    } else {
      this.ref_Loading.onCloseDialog();
      this.onNotify(response.message, "error");
    }

    // remove image from input
    this.ref_ImageInput.value = "";
  };

  onProductImageAsMainListeners = (img, imgIndex) => {
    let inputs = structuredClone(this.state.inputs);
    inputs.main_image_id = img.id;
    inputs.main_image_index = imgIndex;
    inputs.main_image_image_url = img.image_url;
    this.setState({ inputs });
  };

  onProductImageDeleteListeners = (img, imgIndex) => {
    let inputs = structuredClone(this.state.inputs);
    inputs.image_array.splice(imgIndex, 1);

    if (imgIndex == inputs.main_image_index) {
      if (inputs.image_array.length === 0) {
        inputs.main_image_id = "";
        inputs.main_image_index = "-1";
        inputs.main_image_image_url = "";
      } else {
        inputs.main_image_id = inputs.image_array[0].id;
        inputs.main_image_index = 0;
        inputs.main_image_image_url = inputs.image_array[0].image_url;
      }
    }

    this.setState({ inputs });
  };

  render() {
    let addModalClass = "large";
    return (
      <>
        <Modal open={this.state.showDialog} onClose={this.onCloseDialog}>
          <Box
            sx={{
              flex: 1,
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              display: "flex",
              width: "100wh",
              height: "100vh",
            }}
          >
            <div className={`modal-content ${addModalClass}`}>
              <div className="modal-header">{this.renderHeader()}</div>
              <div
                className="modal-body"
                style={{
                  height: "fit-content",
                }}
              >
                {this.renderBody()}
              </div>
              <div className="modal-footer">
                {this.renderFooter()}
                <button
                  className="button cancel"
                  onClick={() => this.onCloseDialog()}
                >
                  <i className="ph ph-bold ph-x-circle"></i>
                  <span>
                    {this.state.formType === "info" ? "Tutup" : "Batal"}
                  </span>
                </button>
              </div>
            </div>
          </Box>
        </Modal>

        <Loading ref={(value) => (this.ref_Loading = value)} />
        <MySnackbar ref={(value) => (this.ref_MySnackbar = value)} />

        <AdminModalListProductAccurate
          ref={(value) => (this.ref_AdminModalListProductAccurate = value)}
          onSelect={(selectedProduct, formData) => {
            this.onChangeProductListeners(selectedProduct, formData);
          }}
        />
      </>
    );
  }

  renderHeader() {
    return (
      <>
        <div className="title">
          {this.state.formType === "add" && `Tambah Data Produk`}
          {this.state.formType === "edit" && `Ubah Data Produk`}
        </div>
        <span className="close" onClick={() => this.onCloseDialog()}>
          &times;
        </span>
      </>
    );
  }

  renderBody() {
    return (
      <>
        {(this.state.formType === "add" || this.state.formType === "edit") &&
          this.renderForm()}
      </>
    );
  }

  renderForm() {
    return (
      <>
        <Box
          className="flex-rows no-right"
          sx={{ overflow: "auto", padding: { xs: "1rem 0rem", md: "0px 0px" } }}
        >
          <div
            className="row no-border"
            // Removed inline style for margin, will use utility classes on children
          >
            {/* note pilih produk dulu */}
            {Number(this.state.inputs.product_id) <= 0 &&
              this.state.formType === "add" && (
                <div className="input-form">
                  <Alert severity="info">
                    Silahkan pilih produk terlebih dahulu.
                  </Alert>
                </div>
              )}
            {/* Section: Product Selection / Information */}
            <div className="input-form mb-6">
              <div className="flex flex-row gap-4 items-center mb-3">
                {Number(this.state.inputs.product_id) > 0 && (
                  <h3>Informasi Produk</h3>
                )}
                {this.state.formType === "add" && (
                  <button
                    className="button"
                    onClick={() => {
                      this.onSelectProductListeners();
                    }}
                  >
                    <i
                      className={`ph ph-bold ${
                        this.state.inputs.product_object
                          ? "ph-pencil"
                          : "ph-plus"
                      }`}
                    ></i>
                    {this.state.inputs.product_object ? "Ubah" : "Pilih"} Produk
                  </button>
                )}
              </div>
              {Number(this.state.inputs.product_id) > 0 &&
                this.state.inputs.product_object && (
                  <div
                    className="box mt-1 flex flex-col gap-4 py-4 px-4"
                    style={{ marginLeft: 0, marginRight: 0 }}
                  >
                    {" "}
                    {/* Added px-4 for internal padding */}
                    <div className="detail_wrapper_grid">
                      <div className="detail_container_grid">
                        <em>Nama</em>
                        <div className="content text">
                          {this.state.inputs.product_object.name}
                        </div>
                      </div>
                      <div className="detail_container_grid">
                        <em>Kode/SKU</em>
                        <div className="content text">
                          {this.state.inputs.product_object.code || "-"}
                        </div>
                      </div>
                      <div className="detail_container_grid">
                        <em>Kategori</em>
                        <div className="content text">
                          {this.state.inputs.product_object.category_name ||
                            "-"}
                        </div>
                      </div>
                      <div className="detail_container_grid">
                        <em>Brand</em>
                        <div className="content text">
                          {this.state.inputs.product_object.brand_name || "-"}
                        </div>
                      </div>
                      <div className="detail_container_grid">
                        <em>Tipe</em>
                        <div className="content text">
                          {this.state.inputs.product_object.type || "-"}
                        </div>
                      </div>
                      <div className="detail_container_grid">
                        <em>Deskripsi</em>
                        <div className="content text">
                          {this.state.inputs.product_object.notes || "-"}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
            </div>

            {/* Section: Kategori Produk */}
            {Number(this.state.inputs.product_id) > 0 && (
              <div className="input-form mb-6">
                <div className="flex flex-row gap-4 items-center mb-4">
                  <h3>Kategori Produk</h3>
                  <button
                    className="button"
                    onClick={() => {
                      let inputs = structuredClone(this.state.inputs);
                      inputs.etalase_array.push({
                        categories: [null],
                      });
                      this.setState({ inputs });
                    }}
                  >
                    <i className="ph ph-bold ph-plus"></i>
                    Tambah Grup Kategori
                  </button>
                </div>
                <div className="mt-0 gap-4 flex flex-col">
                  {" "}
                  {/* Changed mt-4 to mt-0 */}
                  {this.state.inputs.etalase_array.length > 0 && (
                    <div className="flex flex-col gap-4">
                      {this.state.inputs.etalase_array.map((etalase, index) => {
                        return (
                          <div
                            key={index}
                            className="flex flex-col gap-4 mb-4 p-4 border border-gray-200 rounded-md" // Added padding and border for group visual
                          >
                            <div className="flex flex-row gap-4 items-center mb-2">
                              <div className="flex flex-row gap-4 items-center">
                                <div className="label font-semibold">
                                  - Grup Kategori {index + 1}
                                </div>
                                {index === 0 && (
                                  <Tooltip title="Kategori yang ditampilkan">
                                    <Chip label="Kategori Utama" size="small" />
                                  </Tooltip>
                                )}
                              </div>
                              <button
                                className="button danger ml-auto" // Pushed to the right
                                onClick={() => {
                                  let inputs = structuredClone(
                                    this.state.inputs
                                  );
                                  inputs.etalase_array.splice(index, 1);
                                  this.setState({ inputs });
                                }}
                              >
                                <i className="ph ph-bold ph-trash"></i>
                                <span>Hapus Grup</span>
                              </button>
                              {/* jadikan kategori utama */}
                              {index !== 0 && (
                                <button
                                  className="button primary ml-auto" // Pushed to the right
                                  onClick={() => {
                                    let inputs = structuredClone(
                                      this.state.inputs
                                    );

                                    // move this group to first
                                    inputs.etalase_array.splice(index, 1);
                                    inputs.etalase_array.unshift(etalase);
                                    this.setState({ inputs }, () => {
                                      inputs.etalase_array.forEach(
                                        (element, indexGroup) => {
                                          element.categories.forEach(
                                            (
                                              elementCategory,
                                              indexCategory
                                            ) => {
                                              if (elementCategory) {
                                                this[
                                                  `ref_InputCategory${indexGroup}-${indexCategory}`
                                                ].setDefaultInputs(
                                                  elementCategory.name
                                                );
                                              } else {
                                                this[
                                                  `ref_InputCategory${indexGroup}-${indexCategory}`
                                                ].setDefaultInputs("");
                                              }
                                            }
                                          );
                                        }
                                      );
                                    });
                                  }}
                                >
                                  <i className="ph ph-bold ph-star"></i>
                                  <span>Jadikan Kategori Utama</span>
                                </button>
                              )}
                            </div>
                            <div
                              className="grid grid-cols:1 sm:grid-cols-1 md:grid-cols-3 gap-4 ml-0 md:ml-4" // Adjusted ml for responsiveness
                            >
                              {etalase.categories.map((category, idx) => {
                                let dataParams = {};
                                if (idx <= 0) {
                                  dataParams.par_id = "0";
                                } else if (
                                  etalase.categories[idx - 1] !== undefined &&
                                  etalase.categories[idx - 1]?.id !== undefined
                                ) {
                                  dataParams.par_id =
                                    etalase.categories[idx - 1].id;
                                }

                                return (
                                  <div
                                    key={idx}
                                    className="flex gap-2" // Removed mb-2, grid gap handles it
                                  >
                                    <InputAutoComplete2
                                      ref={(value) =>
                                        (this[
                                          `ref_InputCategory${index}-${idx}`
                                        ] = value)
                                      }
                                      inputName={`etalase-${index}-${idx}`}
                                      dataUrl="kiosk/admin/etalase/data"
                                      dataParams={dataParams}
                                      label={`Kategori Produk ${idx + 1}`}
                                      onChange={(selected) => {
                                        if (selected) {
                                          let inputs = structuredClone(
                                            this.state.inputs
                                          );

                                          inputs.etalase_array[
                                            index
                                          ].categories[idx] = selected;
                                          inputs.etalase_array[
                                            index
                                          ].categories = inputs.etalase_array[
                                            index
                                          ].categories.slice(0, idx + 1);
                                          inputs.etalase_array[
                                            index
                                          ].categories.push(null);
                                          this.setState({ inputs });
                                        }
                                      }}
                                      placeholder="Tuliskan Kategori Produk"
                                    />
                                  </div>
                                );
                              })}
                            </div>
                            {/* Suggestion */}
                            {this.state.inputs.etalase_suggestion.length >
                              0 && (
                              <>
                                <div className="label">
                                  Atau pilih dari kategori di els.id
                                </div>
                                <div className="flex flex-row gap-4">
                                  {this.state.inputs.etalase_suggestion.map(
                                    (suggestion, idx) => {
                                      return (
                                        <Chip
                                          key={idx}
                                          label={suggestion.name}
                                          onClick={() =>
                                            this.onSelectProductCategory(
                                              suggestion,
                                              index,
                                              idx
                                            )
                                          }
                                        />
                                      );
                                    }
                                  )}
                                </div>
                              </>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Section: Gambar Produk */}
            {Number(this.state.inputs.product_id) > 0 && (
              <div className="input-form mb-6">
                <h3 className="mb-3">Gambar Produk</h3>
                <input
                  ref={(value) => (this.ref_ImageInput = value)}
                  style={{ display: "none" }}
                  className="input-hidden"
                  type="file"
                  accept=".jpg, .jpeg, .png"
                  onChange={(e) => {
                    this.onUploadImageListeners(e.target.files[0]);
                  }}
                ></input>
                <div
                  className="input-multi-image mt-0" // Changed from inline style
                >
                  {this.state.inputs.image_array.map((img, imgIndex) => (
                    <div key={imgIndex}>
                      {/* Added key here */}
                      <div
                        className="image no-border"
                        onClick={() => {
                          this.onPickImageListeners(imgIndex);
                        }}
                      >
                        <img alt="" src={img.image_url} />
                        <div
                          className={`title ${
                            this.state.inputs.main_image_index == imgIndex &&
                            "main"
                          }`}
                        >
                          Gambar {imgIndex + 1}
                        </div>
                        <i className="ph ph-camera-rotate"></i>
                      </div>
                      <div className="mt-2 flex flex-wrap justify-end items-center gap-2">
                        {" "}
                        {/* Added gap */}
                        {this.state.inputs.main_image_index != imgIndex && (
                          <Tooltip title="Jadikan gambar utama">
                            <button
                              className="button small"
                              onClick={() => {
                                this.onProductImageAsMainListeners(
                                  img,
                                  imgIndex
                                );
                              }}
                            >
                              <i
                                className="ph ph-star"
                                style={{ marginRight: "0px" }}
                              ></i>
                            </button>
                          </Tooltip>
                        )}
                        <Tooltip title="Hapus Gambar">
                          <button
                            className="button danger small"
                            onClick={() => {
                              this.onProductImageDeleteListeners(img, imgIndex);
                            }}
                          >
                            <i
                              className="ph ph-trash"
                              style={{ marginRight: "0px" }}
                            ></i>
                          </button>
                        </Tooltip>
                      </div>
                    </div>
                  ))}
                  <div>
                    <div
                      className="image"
                      onClick={() => {
                        this.onPickImageListeners(-1);
                      }}
                    >
                      <div className="title">Tambah Gambar</div>
                      <i className="ph ph-camera"></i>
                    </div>
                    <div className="placeholder">
                      <span className="note">Maksimal 1MB</span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Section: Harga Jual & Status Dijual */}
            {Number(this.state.inputs.product_id) > 0 && (
              <div className="input-form mb-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    className="text-right"
                    label="Harga Jual"
                    inputType="number"
                    value={this.state.inputs.selling_price_label}
                    onChange={(event) => {
                      if (
                        event.target !== undefined &&
                        event.target.value !== undefined
                      ) {
                        let inputs = structuredClone(this.state.inputs);
                        inputs.selling_price = numberFormatIdToNumber(
                          event.target.value
                        );
                        inputs.selling_price_label = event.target.value;
                        this.setState({ inputs }, () => {});
                      }
                    }}
                    error={
                      this.state.errors.selling_price !== undefined &&
                      this.state.errors.selling_price !== null
                        ? true
                        : false
                    }
                    onFocus={() =>
                      this.onTextErrorListeners(null, "selling_price")
                    }
                    startAdornment={<div className="px-2">Rp</div>}
                    required
                    maxLength={20}
                  />
                  <Input
                    formClass="mt-0" // This class applies to the Input component's wrapper
                    label="Status Dijual"
                    inputType="select"
                    value={this.state.inputs.onsale_bool}
                    defaultValue={this.state.inputs.onsale_bool}
                    onChange={(event) => {
                      if (
                        event.target !== undefined &&
                        event.target.value !== undefined
                      ) {
                        this.onTextInputListeners(
                          event.target.value === "true" ? true : false,
                          "onsale_bool"
                        );
                      }
                    }}
                    error={
                      this.state.errors.onsale_bool !== undefined &&
                      this.state.errors.onsale_bool !== null
                        ? true
                        : false
                    }
                    onFocus={() =>
                      this.onTextErrorListeners(null, "onsale_bool")
                    }
                    options={[
                      { value: true, label: "Ya" },
                      { value: false, label: "Tidak" },
                    ]}
                    required
                    startAdornment={
                      <i
                        className={`px-2 ph ph-bold ph-toggle-left ${
                          this.state.inputs.onsale_bool
                            ? "text-green"
                            : "text-red"
                        }`}
                      ></i>
                    }
                  />
                </div>
              </div>
            )}

            {/* {this.renderFormVariant()} */}
          </div>
        </Box>
      </>
    );
  }

  renderFormVariant() {
    const { variant_array, image_array } = this.state.inputs;

    const dataHeadings = [
      { label: "", key: "", className: "sticky_thead" },
      { label: "Nama Produk", key: "name", className: "" },
      { label: "Kode Produk", key: "name", className: "" },
      { label: "Harga Jual", key: "selling_price", className: "" },
      { label: "Gambar Utama", key: "main_image", className: "" },
    ];

    return (
      <div className="variant-section mt-6">
        {" "}
        {/* Changed mt-4 to mt-6 */}
        <div className="input-form flex flex-row gap-4 items-center mb-4">
          {" "}
          {/* Added mb-4 */}
          <h3>
            Produk Varian{" "}
            {this.state.inputs.variant_array.length > 0
              ? `(${this.state.inputs.variant_array.length} produk)`
              : ""}
          </h3>
          <button
            className="button"
            // style={{ marginLeft: "0px" }} // Removed inline style
            onClick={() => {
              this.onSelectProductVariantListeners();
            }}
          >
            <i className="ph ph-bold ph-plus"></i>
            Tambah Produk Varian
          </button>
        </div>
        <div className="mt-0">
          {" "}
          {/* Changed mt-4 to mt-0 */}
          <Table
            title="Daftar Variant Produk"
            dataHeadings={dataHeadings}
            dataTables={variant_array}
            renderItems={(item, index) => (
              <>{this.renderItemsProductVariant(item, index)}</>
            )}
            disabledHeader
            disabledPage
          />
        </div>
      </div>
    );
  }

  renderItemsProductVariant = (item, index) => (
    <tr key={index}>
      <td>
        <i
          className="ph ph-bold ph-x-circle"
          style={{ color: "#e74c3c", cursor: "pointer" }}
          onClick={() => {
            this.onProductVariantDeleteListeners(item, index);
          }}
        ></i>
      </td>
      <td>{item.product_object?.name || "-"}</td>
      <td>{item.product_object?.code || "-"}</td>
      <td>
        <Input
          inputContainerClass="bg-white"
          className="text-right"
          inputType="number"
          value={item.selling_price_label}
          onChange={(event) => {
            if (
              event.target !== undefined &&
              event.target.value !== undefined
            ) {
              let inputs = structuredClone(this.state.inputs);
              inputs.variant_array[index].selling_price =
                numberFormatIdToNumber(event.target.value);
              inputs.variant_array[index].selling_price_label =
                event.target.value;
              this.setState({ inputs }, () => {});
            }
          }}
          error={
            this.state.errors.selling_price !== undefined &&
            this.state.errors.selling_price !== null
              ? true
              : false
          }
          onFocus={() => this.onTextErrorListeners(null, "selling_price")}
          startAdornment={<div className="pl-4">Rp</div>}
          required
          maxLength={20}
        />
      </td>
      <td>
        <Input
          inputContainerClass="bg-white"
          inputType="select"
          value={item.main_image_index}
          onChange={(event) => {
            if (
              event.target !== undefined &&
              event.target.value !== undefined
            ) {
              const find = this.state.inputs.image_array.find(
                (item, index) => index == event.target.value
              );

              if (find) {
                let inputs = structuredClone(this.state.inputs);
                inputs.variant_array[index].main_image_id = find.id;
                inputs.variant_array[index].main_image_index =
                  event.target.value;
                inputs.variant_array[index].main_image_image_url =
                  find.image_url;
                this.setState({ inputs }, () => {});
              }
            }
          }}
          error={
            this.state.errors.main_image_id !== undefined &&
            this.state.errors.main_image_id !== null
              ? true
              : false
          }
          onFocus={() => this.onTextErrorListeners(null, "main_image_id")}
          options={this.state.inputs.image_array.map((item, index) => ({
            value: index,
            label: `Gambar ${index + 1}`,
          }))}
        />
      </td>
    </tr>
  );
  renderFooter() {
    return (
      <>
        <button
          className={`button ${this.state.formType === "edit" && "warning"} ${
            this.state.formType === "delete" && "danger"
          }`}
          onClick={() => {
            this.onValidateListeners();
          }}
        >
          {this.state.formType === "add" && (
            <i className="ph ph-bold ph-floppy-disk"></i>
          )}
          {this.state.formType === "edit" && (
            <i className="ph ph-bold ph-floppy-disk"></i>
          )}
          <span>Simpan</span>
        </button>
      </>
    );
  }
}
