import React from "react";
import stylesTable from "@/styles/Table.module.css";
import Table from "@/components/libs/Table";
import Loading from "@/components/modal/Loading";
import MySnackbar from "@/components/MySnackbar";
import ModalConfirmation from "@/components/modal/ModalConfirmation";
import AccessRolesModalAddUpdate from "@/components/pages/adm/setting/users/AccessRolesModalAddUpdate"; // Placeholder path, adjust as needed
import ApiHelper from "@/utils/ApiHelper";
// import Constants from "@/utils/Constants";
import Tooltip from "@mui/material/Tooltip";
import IconButton from "@mui/material/IconButton";
import Switch from "@mui/material/Switch";

// Adjusted ARR_HEADING for Hak Akses (Roles)
const ARR_HEADING = [
  {
    key: "",
    label: "",
    className: "text-center sticky_thead",
    sort: "",
    sortable: false,
  },
  {
    key: "name",
    label: "<PERSON>a <PERSON>",
    className: "",
    sort: "asc",
    sortable: true,
  },
];

// Consider renaming the class if this component is now dedicated to roles
// export default class AccessRoleList extends React.Component {
export default class RoleManagementList extends React.Component {
  // Renamed class for clarity
  constructor(props) {
    super(props);
    this.ref_Table = React.createRef();
    this.ref_Loading = React.createRef();
    this.ref_MySnackbar = React.createRef();
    this.ref_ModalConfirmation = React.createRef();
    this.ref_AccessRolesModalAddUpdate = React.createRef(); // Renamed ref for the modal

    this.state = {
      isFetched: true,
      fetchData: [],
      fetchDataShow: 0,
      fetchDataTotal: 0,
      fetchDataLimit: 10,
      fetchPageSelected: 1,
      fetchPageTotal: 0,
      inputSearch: "",
      inputSort: "name", // Default sort by role name
      inputFilter: [], // Filters might be different for roles
    };
  }

  onInit = () => {
    this.setState(
      {
        inputSearch: "",
        inputSort: "",
        inputFilter: [],
      },
      () => {
        this.onRefresh();
      }
    );
  };

  onRefresh = () => {
    this.setState(
      {
        isFetched: true,
        fetchData: [],
        fetchDataShow: 0,
        fetchDataTotal: 0,
        fetchPageSelected: 1,
        fetchPageTotal: 1,
      },
      this.onFetchData
    );
  };

  onFetchData = async (isPageClicked = false) => {
    if (isPageClicked) {
      this.setState({ isFetched: true });
    } else {
      this.setState({
        isFetched: true,
        fetchData: [],
        fetchDataShow: 0,
        fetchDataTotal: 0,
        fetchPageSelected: 1,
      });
    }

    // Adjusted API endpoint for roles
    let response = await ApiHelper.get("kiosk/admin/user/role/data", {
      limit: this.state.fetchDataLimit,
      page: this.state.fetchPageSelected,
      search: this.state.inputSearch,
      sort: this.state.inputSort,
      pagination_bool: true,
    });

    if (response.status === 200 && response.results) {
      this.setState({
        isFetched: false,
        fetchData: response.results.data,
        fetchDataShow: response.results.pagination.total_display,
        fetchDataTotal: response.results.pagination.total_data,
        fetchPageTotal: response.results.pagination.total_page,
      });
    } else {
      this.setState({
        isFetched: false,
        fetchData: [],
        fetchDataShow: 0,
        fetchDataTotal: 0,
        fetchPageTotal: 0,
      });
      if (this.ref_MySnackbar.current) {
        this.ref_MySnackbar.current.onNotify(
          response?.message || "Gagal memuat data hak akses.",
          "error"
        );
      }
    }
  };

  // Assuming roles can be activated/deactivated
  onSubmitActivate = async (item, index) => {
    this.ref_Loading.current.onShowDialog();
    // Adjusted API endpoint for activating/deactivating a role
    const response = await ApiHelper.post("kiosk/admin/user/role/activate", {
      id: item.id, // Role ID
      active_bool: !item.active_bool, // Assuming role object has 'active_bool'
    });
    if (response.status === 200) {
      this.onRefresh();
      this.ref_ModalConfirmation.current.onCloseDialog();
    } else {
      if (this.ref_MySnackbar.current) {
        this.ref_MySnackbar.current.onNotify(
          response?.message || "Terjadi Kesalahan",
          "error"
        );
      }
    }
    this.ref_Loading.current.onCloseDialog();
  };

  onSubmitDelete = async (item, index) => {
    this.ref_Loading.current.onShowDialog();
    // Adjusted API endpoint for deleting a role
    const response = await ApiHelper.post("kiosk/admin/user/role/delete", {
      id: item.id, // Role ID
    });
    if (response.status === 200) {
      this.onRefresh();
      this.ref_ModalConfirmation.current.onCloseDialog();
    } else {
      if (this.ref_MySnackbar.current) {
        this.ref_MySnackbar.current.onNotify(
          response?.message || "Terjadi Kesalahan",
          "error"
        );
      }
    }
    this.ref_Loading.current.onCloseDialog();
  };

  render() {
    return (
      <>
        <Table
          ref={this.ref_Table}
          customTableContainers={stylesTable.withTab}
          stylesContainer={{
            maxHeight: "calc(100% - 48px)",
          }}
          disableTitle={true}
          title={this.props.selectedTabTitle || "Daftar Hak Akses"} // Adjusted title
          subtitle={"List data dan kelola data hak akses"} // Adjusted subtitle
          addTitle={"Hak Akses Baru"} // Adjusted add button title
          addListeners={() => {
            // Assuming the modal ref is correctly set up
            if (this.ref_AccessRolesModalAddUpdate.current) {
              this.ref_AccessRolesModalAddUpdate.current.onShowDialog("add");
            } else {
              console.error("AccessRolesModalAddUpdate ref not available");
            }
          }}
          searchListeners={(inputSearch) => {
            if (this.ref_Table.current)
              this.ref_Table.current.setSearchValue(inputSearch);
            this.setState({ inputSearch, fetchPageSelected: 1 }, () => {
              this.onFetchData();
            });
          }}
          // filterListeners={() => {
          //   alert(`Filter Callback for Roles`); // Adjust if filtering is needed for roles
          // }}
          // exportListeners={() => {
          //   alert(`Export Callback for Roles`); // Adjust if export is needed for roles
          // }}
          inputSearch={this.state.inputSearch}
          onSearch={(inputSearch) => {
            if (this.ref_Table.current)
              this.ref_Table.current.setSearchValue(inputSearch);
            this.setState({ inputSearch, fetchPageSelected: 1 }, () => {
              this.onFetchData();
            });
          }}
          disabledBtnFilter // Disable if not used or re-evaluate for roles
          disabledBtnExport // Disable if not used
          sortListeners={(inputSort) => {
            this.setState({ inputSort, fetchPageSelected: 1 }, () => {
              this.onFetchData();
            });
          }}
          dataHeadings={ARR_HEADING}
          dataTables={this.state.fetchData}
          renderItems={this.renderItems}
          pageCount={this.state.fetchPageTotal}
          pageSelected={this.state.fetchPageSelected}
          pageListeners={(fetchPageSelected) => {
            this.setState({ fetchPageSelected }, () => this.onFetchData(true));
          }}
          pageRow={this.state.fetchDataLimit}
          pageRowListeners={(fetchDataLimit) => {
            this.setState({ fetchDataLimit, fetchPageSelected: 1 }, () =>
              this.onFetchData()
            );
          }}
          onReload={this.onRefresh}
          isFetched={this.state.isFetched || this.props.isAuthLoading}
        />

        <Loading ref={this.ref_Loading} />
        <MySnackbar ref={this.ref_MySnackbar} />
        <ModalConfirmation ref={this.ref_ModalConfirmation} />

        {/* Use the modal for Roles */}
        <AccessRolesModalAddUpdate
          ref={this.ref_AccessRolesModalAddUpdate}
          onResults={(formType, formData, formIndex) => {
            this.onRefresh(); // Refresh list after add/update
          }}
        />
      </>
    );
  }

  renderItems = (item, index) => {
    // `item` is now a role object, expected to have `id`, `name`, and `active_bool`
    return (
      <tr
        key={item.id || index}
        style={{ opacity: item.active_bool === false ? 0.4 : 1 }}
      >
        {/* Adjusted opacity check */}
        <td className="text-center">
          <div className="actions">
            <Tooltip title="Ubah Data Hak Akses">
              <IconButton
                onClick={() => {
                  if (this.ref_AccessRolesModalAddUpdate.current) {
                    this.ref_AccessRolesModalAddUpdate.current.onShowDialog(
                      "edit",
                      item, // Pass the role item
                      index
                    );
                  }
                }}
              >
                <i className="ph ph-bold ph-pencil-simple text-blue"></i>
              </IconButton>
            </Tooltip>
            <Tooltip title="Hapus Data Hak Akses">
              <IconButton
                onClick={() => {
                  if (this.ref_ModalConfirmation.current) {
                    this.ref_ModalConfirmation.current.onShowDialog("delete", {
                      text: {
                        title: "Hak Akses", // Adjusted title
                        action: "Hapus",
                        info: item.name, // Role name for confirmation
                      },
                      onConfirmed: () => {
                        this.onSubmitDelete(item, index);
                      },
                    });
                  }
                }}
              >
                <i className="ph ph-bold ph-trash-simple text-red"></i>
              </IconButton>
            </Tooltip>
          </div>
        </td>
        {/* Display only the role name */}
        <td style={{ width: "100%" }}>{item.name || "-"}</td>
      </tr>
    );
  };
}
