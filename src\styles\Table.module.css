/*! OVERRIDE CONTAINERS PADDING ==== */
.containers { padding-bottom: 0; }
.contents { position: relative; }

.tableContainer {
    height: calc(100vh - 137px);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    font: inherit;
    background-color: white;
    border-radius: 2px 2px 0 0;
    -webkit-border-radius: 2px 2px 0 0;
    -moz-border-radius: 2px 2px 0 0;
    -ms-border-radius: 2px 2px 0 0;
    -o-border-radius: 2px 2px 0 0;
    box-shadow: 0 .4rem 1rem #ededed55;
    border: solid 1px #eeeeee;
}
.tableContainer.withTab { height: calc(100vh - 185px); }

/*? SECTION FOR TABLE HEAD ==== */
.tableContainer .tableHead { padding: 1.2rem; }
.tableContainer .tableHead .topBar { padding-bottom: .8rem; margin-top: -5px; }
.tableContainer .tableHead .topBar .title { font-size: 1.1rem; color: #2d3436; font-weight: 600; text-transform: uppercase; }
.tableContainer .tableHead .topBar .subtitle { font-size: .8rem; color: #70727f; margin-top: -5px; }
.tableContainer .tableHead .searchBar { display: flex; flex-direction: row; align-items: center; }
.tableContainer .tableHead .searchBar .inputSearch { width: 30%; display: flex; margin-left: 1rem; }
.tableContainer .tableHead .searchBar .inputSearch label {
    flex-shrink: 0;
    height: 40px;
    width: 40px;
    background-color: #636e72;
    color: rgb(255, 255, 255);
    border-radius: 5px 0 0 5px;
    -webkit-border-radius: 5px 0 0 5px;
    -moz-border-radius: 5px 0 0 5px;
    -ms-border-radius: 5px 0 0 5px;
    -o-border-radius: 5px 0 0 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.2rem;
    font-weight: 500;
}
.tableContainer .tableHead .searchBar .inputSearch input {
    box-sizing: border-box;
    flex-grow: 1;
    min-width: 0;
    height: 40px;
    padding: 1em;
    font: inherit;
    border-radius: 0 5px 5px 0;
    -webkit-border-radius: 0 5px 5px 0;
    -moz-border-radius: 0 5px 5px 0;
    -ms-border-radius: 0 5px 5px 0;
    -o-border-radius: 0 5px 5px 0;
    border-left: none;
    transition: 150ms ease;
    -webkit-transition: 150ms ease;
    -moz-transition: 150ms ease;
    -ms-transition: 150ms ease;
    -o-transition: 150ms ease;
    background-color: #fff;
    border: 1px solid #636e72;
}
.tableContainer .tableHead .searchBar .inputSearch input:hover { border-color: #2d3436; }
.tableContainer .tableHead .searchBar .inputSearch input:focus { outline: none; border-color: #2d3436; }
.tableContainer .tableHead .searchBar .inputSearch input:disabled { background-color: #f7f7f7; border-color: #f7f7f7; }
.tableContainer .tableHead .searchBar .inputSearch:has(input:hover)>label { background-color: #2d3436; }
.tableContainer .tableHead .searchBar .inputSearch:has(input:focus)>label { background-color: #2d3436; }
.tableContainer .tableHead .searchBar .inputSearch input::placeholder { color: #d1d1d5; }
.tableContainer .tableHead .searchBar a { text-decoration: none; }
.tableContainer .tableHead .searchBar button {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0 .8rem;
    height: 40px;
    background-color: #636e72;
    border: none;
    color: #ffffff;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
    font: inherit;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 16px;
    margin-left: 1rem;
    cursor: pointer;
}
.tableContainer .tableHead .searchBar button:first-child { margin-left: 0rem; background-color: #E50040; }
.tableContainer .tableHead .searchBar button i { margin-right: 10px; font-size: 1.2rem }
.tableContainer .tableHead .searchBar button:hover,
.tableContainer .tableHead .searchBar button:focus { background-color: #2d3436; }
.tableContainer .tableHead .searchBar button:first-child:hover, 
.tableContainer .tableHead .searchBar button:first-child:focus { background-color: #cc0039; }
.tableContainer .tableHead .searchBar .disabledAddBtn { margin-left: 0; }
.tableContainer .tableHead .searchResults { display: flex; flex-direction: row; align-items: center; flex-wrap: wrap; gap: .6rem; padding-top: 1rem; }
.tableContainer .tableHead .searchResults .search, 
.tableContainer .tableHead .searchResults .filter {
    background-color: #8d8d8d;
    color: #fff;
    font-size: .8rem;
    display: flex;
    flex-direction: row;
    align-items: center;
    border-radius: 100px;
    -webkit-border-radius: 100px;
    -moz-border-radius: 100px;
    -ms-border-radius: 100px;
    -o-border-radius: 100px;
    padding: 0rem .7rem;
    text-transform: uppercase;
    font-weight: 600;
    cursor: pointer;
}
.tableContainer .tableHead .searchResults .filter { background-color: #3498db; }
.tableContainer .tableHead .searchResults .search i,
.tableContainer .tableHead .searchResults .filter i { font-size: 1.6rem; margin-right: -.7rem; margin-left: .5rem; }

/*? SECTION FOR TABLE BODY ==== */
.tableContainer .tableBody { padding: 0px; flex-grow: 1; background-color: #f2f2f2; width: 100%; margin: 0 auto; overflow: auto; font-size: 1rem; font-weight: 600; color: #5a5a5a; }
.tableContainer .tableBody table { width: 100%; }
.tableContainer .tableBody::-webkit-scrollbar { width: 0rem; height: 0rem; }
.tableContainer .tableBody::-webkit-scrollbar-thumb { border-radius: .2rem; -webkit-border-radius: .2rem; -moz-border-radius: .2rem; -ms-border-radius: .2rem; -o-border-radius: .2rem; background-color: red; visibility: hidden; }
.tableContainer .tableBody:hover::-webkit-scrollbar-thumb { visibility: visible; }
.tableContainer .tableBody td img { width: 28px; height: 28px; object-fit: cover; margin-right: .5rem; vertical-align: middle; }
.tableContainer .tableBody table,
.tableContainer .tableBody th,
.tableContainer .tableBody td { color: #2d3436; border-collapse: collapse; text-align: left; text-wrap: nowrap; font-size: .95rem; font-weight: normal; }
.tableContainer .tableBody thead th { padding: .65rem 1.2rem; z-index: 1; position: sticky; top: 0; left: 0; background-color: #636e72; }
.tableContainer .tableBody thead th>div { text-transform: uppercase; color: white; font-weight: 600; font-size: .85rem; }
.tableContainer .tableBody thead th>.sorts { display: flex; flex-direction: row; align-items: center; justify-content: space-between; cursor: pointer; }
.tableContainer .tableBody thead th>.sorts>i { margin-left: 1rem; margin-right: -5px; }
.tableContainer .tableBody table td { padding: .5rem 1.2rem; } 
.tableContainer .tableBody table td .rows { display: flex; flex-direction: row; align-items: center; justify-content: space-between; }
.tableContainer .tableBody table td .rows .actions { display: flex; flex-direction: row; align-items: center; margin-left: 10px; }
.tableContainer .tableBody table tr td:first-child { position: sticky; top: 0; left: 0; }
.tableContainer .tableBody table td .actions { display: flex; flex-direction: row; gap: 5px; }
.tableContainer .tableBody tbody tr:nth-child(odd), 
.tableContainer .tableBody table tr:nth-child(odd) td:first-child { background-color: #ffffff; }
.tableContainer .tableBody tbody tr:nth-child(even), 
.tableContainer .tableBody table tr:nth-child(even) td:first-child { background-color: #f9f9f9; }
.tableContainer .tableBody tbody tr:hover { background-color: rgb(236, 236, 236); cursor: pointer; }
.tableContainer .tableBody tbody tr:hover td:first-child { background-color: rgb(236, 236, 236); cursor: pointer; }

/*? SECTION FOR TABLE PAGE ==== */
.tableContainer .tablePage { background-color: white; display: flex; align-items: center; justify-content: space-between; padding: .8rem 1.2rem; border-top: solid 1px #f2f2f2; }
.tableContainer .tablePage .left { display: flex; align-items: center; }
.tableContainer .tablePage .right { display: flex; align-items: center; gap: 10px; }

@media (max-width:880px) {
    /*! OVERRIDE CONTAINERS PADDING ==== */
    .containers { padding-left: 0; padding-right: 0; }
    .contents { padding-top: 0; }
    .tableContainer {
        background-color: #fff;
        border-radius: 0;
        -webkit-border-radius: 0;
        -moz-border-radius: 0;
        -ms-border-radius: 0;
        -o-border-radius: 0;
        -webkit-border-radius: 0;
        box-shadow: none;
        height: calc(100vh - 60px);
        border: none;
    }
    .tableContainer.withTab {
        height: calc(100vh - 108px);
    }
    .tableContainer .tableHead { padding: 0rem; }
    .tableContainer .tableHead .searchBar .inputSearch { flex-grow: 1; margin-left: 0; }
    .tableContainer .tableHead .searchBar .inputSearch label { display: none; }
    .tableContainer .tableHead .searchBar .inputSearch input { border-radius: 0px; -webkit-border-radius: 0px; -moz-border-radius: 0px; -ms-border-radius: 0px; -o-border-radius: 0px; border: none; }
    .tableContainer .tableHead .searchBar button { background-color: #5a5a5a; margin-left: 0; border-radius: 0; -webkit-border-radius: 0; -moz-border-radius: 0; -ms-border-radius: 0; -o-border-radius: 0; }
    .tableContainer .tableHead .searchBar button:not(:first-child) {
        border-left: solid 1px rgb(122, 122, 122);
    }
    .tableContainer .tableHead .searchBar button i { margin-right: 0; }
    .tableContainer .tableHead .searchResults { padding: 1rem; }

    .tableContainer .tableBody table tr td:first-child { position: relative; }
    .tableContainer .tablePage { justify-content: center; }

    .tableContainer .tableHead .topBar, 
    .tableContainer .tablePage .left, 
    .tableContainer .tableHead .searchBar button span { display: none; }
}