import React from "react";
import { Mo<PERSON>, Box, Checkbox, Chip, Radio } from "@mui/material";
import Loading from "@/components/modal/Loading";
import Table from "@/components/libs/Table";
import ApiHelper from "@/utils/ApiHelper";
import CommonHelper from "@/utils/CommonHelper";
import MySnackbar from "@/components/MySnackbar";
import InputAutoComplete from "@/components/libs/InputAutoComplete2";
import Input from "@/components/libs/Input";
import FilterHelper, {
  FILTER_TYPE_STRING,
  FILTER_COMP_EQUAL,
} from "@/utils/FilterHelper";

const DEFAULT_INPUTS_FILTER = {
  etalase: null,
  brand: null,
  etalase_array: [null],
};

class ProductModalListFilter extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showDialog: false,
      formType: "filter",
      formData: null,
      formIndex: -1,

      inputs: structuredClone(DEFAULT_INPUTS_FILTER),
      errors: {},
    };
  }

  // ====================================================================================
  // ========== INITIALIZE, GET DATA ====================================================
  // ====================================================================================
  onShowDialog = (formType, formData = null, formIndex = -1) => {
    let inputs = structuredClone(DEFAULT_INPUTS_FILTER);
    if (formData?.inputs) {
      inputs = { ...structuredClone(inputs), ...formData.inputs };
    }

    this.setState({
      showDialog: true,
      formType,
      formData,
      formIndex,
      inputs,
    });
  };
  onCloseDialog = () => {
    this.setState({
      showDialog: false,
      formType: "filter",
      formData: null,
      formIndex: -1,

      inputs: structuredClone(DEFAULT_INPUTS_FILTER),
      errors: {},
    });
  };
  onTextInputListeners = (val, name) => {
    this.setState((prevState) => ({
      ...prevState,
      inputs: { ...prevState.inputs, [name]: val },
    }));
  };
  onTextErrorListeners = (error, input) => {
    this.setState((prevState) => ({
      ...prevState,
      errors: { ...prevState.errors, [input]: error },
    }));
  };

  onSubmitFilter = () => {
    const { onFilter } = this.props;
    if (onFilter) {
      onFilter(this.state.inputs);
    }
    this.onCloseDialog();
  };

  // ====================================================================================
  // ========== RENDER ==================================================================
  // ====================================================================================
  render() {
    let addModalClass = "dialog";

    return (
      <>
        <Modal open={this.state.showDialog} onClose={this.onCloseDialog}>
          <Box
            sx={{
              flex: 1,
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              display: "flex",
              width: "100wh",
              height: "100vh",
            }}
          >
            <div className={`modal-content ${addModalClass}`}>
              <div className="modal-header">{this.renderHeader()}</div>
              <div className="modal-body">{this.renderBody()}</div>
              <div className="modal-footer">{this.renderFooter()}</div>
            </div>
          </Box>
        </Modal>

        {/* Loading dialog */}
        <Loading ref={(value) => (this.ref_Loading = value)} />
        <MySnackbar ref={(value) => (this.ref_MySnackbar = value)} />
      </>
    );
  }

  renderHeader() {
    return (
      <>
        <div className="title">Filter Data</div>
        <span className="close" onClick={() => this.onCloseDialog()}>
          &times;
        </span>
      </>
    );
  }

  renderBody() {
    return <>{this.renderForm()}</>;
  }

  renderFooter() {
    return (
      <>
        <button
          className="button"
          onClick={() => {
            this.onSubmitFilter();
          }}
        >
          <i className="ph ph-bold ph-check-circle"></i>
          <span>Filter</span>
        </button>
        <button className="button cancel" onClick={() => this.onCloseDialog()}>
          <i className="ph ph-bold ph-x-circle"></i>
          <span>{this.state.formType === "info" ? "Tutup" : "Batal"}</span>
        </button>
      </>
    );
  }

  renderForm() {
    return (
      <>
        <div className="flex-rows no-right">
          <div className="row no-border">
            <div className="input-form">
              <InputAutoComplete
                ref={(value) => (this.ref_InputEtalase = value)}
                label="Kategori Produk"
                placeholder="Tuliskan nama kategori produk"
                inputName="modal-list-product-filter-input-etalase"
                dataUrl="kiosk/admin/etalase/data"
                dataParams={{
                  ...(this.state.formData?.dataParams ?? {}),
                  sort: "name",
                }}
                displayTitle="name"
                onChange={(item, type) => {
                  this.onTextInputListeners(item, "etalase");
                }}
                error={
                  this.state.errors.etalase !== undefined &&
                  this.state.errors.etalase !== null
                    ? true
                    : false
                }
                onFocus={() => this.onTextErrorListeners(null, "etalase")}
                displayRenderCustom={(item, index) => {
                  return (
                    <>
                      <div className="title">
                        {item.title && `${item.title} | `}
                        {item.name}
                      </div>
                      <div className="subtitle"></div>
                    </>
                  );
                }}
              />
            </div>
            <div className="input-form">
              <InputAutoComplete
                ref={(value) => (this.ref_InputBrand = value)}
                label="Brand"
                placeholder="Tuliskan nama brand"
                inputName="modal-list-product-filter-input-brand"
                dataUrl="kiosk/admin/product/brand/options"
                dataParams={{
                  ...(this.state.formData?.dataParams ?? {}),
                  sort: "name",
                }}
                displayTitle="name"
                onChange={(item, type) => {
                  this.onTextInputListeners(item, "brand");
                }}
                error={
                  this.state.errors.brand !== undefined &&
                  this.state.errors.brand !== null
                    ? true
                    : false
                }
                onFocus={() => this.onTextErrorListeners(null, "brand")}
              />
            </div>
          </div>
        </div>
      </>
    );
  }
}

export default ProductModalListFilter;
