import { useEffect, useRef } from "react";
import useS<PERSON> from "swr";
import Constants from "@/utils/Constants";
import { useRouter, usePathname } from "next/navigation";

export default function useAuth({
  redirectTo = "",
  redirectIfFound = false,
  checkUrl = "check/auth",
} = {}) {
  const Router = useRouter();
  const pathname = usePathname();

  const fetcher = (url) =>
    fetch(url, { credentials: "include" }).then((res) => res.json());
  let urlCheckAuth = Constants.apiLocalUrl + checkUrl;

  let host = "";
  try {
    host = window !== undefined ? window.location.origin : "localhost";
  } catch (error) {
    host = "localhost";
  }
  if (
    !host.includes("localhost") &&
    !host.includes("//10." && !host.includes("//192.168."))
  ) {
    urlCheckAuth = `${host}/api/` + checkUrl;
  } else {
    urlCheckAuth = Constants.apiLocalUrl + checkUrl;
  }

  // prevent cache
  const random = useRef(Date.now());
  const {
    data: user,
    mutate: mutateUser,
    isLoading: isAuthLoading,
  } = useSWR(
    `${urlCheckAuth}?t=${random.current}&pathname=${pathname}`,
    fetcher,
    { revalidateOnFocus: false, }
  );

  useEffect(() => {
    if (!redirectTo || !user) return;

    if (user?.redirect) {
      if (user?.redirect === "back") {
        Router.back();
      } else {
        Router.push(user?.redirect);
      }
      return;
    }

    if (
      // If redirectTo is set, redirect if the user was not found.
      (redirectTo && !redirectIfFound && !user?.isLoggedIn) ||
      // If redirectIfFound is also set, redirect if the user was found
      (redirectIfFound && user?.isLoggedIn)
    ) {
      if (user?.isLoggedIn) {
        if (user?.next !== redirectTo) {
          Router.push(user?.next);
        } else {
          Router.push(redirectTo);
        }
      } else {
        Router.push(redirectTo);
      }
    }
  }, [user, redirectIfFound, redirectTo]);

  return { user, mutateUser, isAuthLoading };
}
