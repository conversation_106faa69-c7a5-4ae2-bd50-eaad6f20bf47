/*
Created by esoda
Created on Apr, 2025
Contact esoda.id
*/

import React from "react";
import styles from "@/styles/Pos.module.css";
import Loading from "@/components/modal/Loading";
import Router from "next/router";
import Constants from "@/utils/Constants";
import TimePicker from "@/components/TimePicker";
import ApiHelper from "@/utils/ApiHelper";
import { Skeleton } from "@mui/material";

export default class PosComponentTopbar extends React.Component {
  // ====================================================================================
  // ========== INITIALIZE, GET DATA ====================================================
  // ====================================================================================

  // ====================================================================================
  // ========== ACTION LISTENERS ========================================================
  // ====================================================================================
  onAccountListeners = async () => {
    this.ref_Loading.onShowDialog();
    setTimeout(() => {
      this.ref_Loading.onCloseDialog();
    }, 500);
  };
  onLogoutListeners = async () => {
    this.ref_Loading.onShowDialog();
    let response = await ApiHelper.post("kiosk/admin/auth/logout", {});
    this.ref_Loading.onCloseDialog();
    Router.replace(Constants.webUrl.adm.login);
  };

  // ====================================================================================
  // ========== RENDER SECTIONS =========================================================
  // ====================================================================================
  render() {
    const { isAuthLoading, user } = this.props;
    return (
      <>
        <div className={styles.topbar}>
          <div className={styles.left}>
            <img alt={Constants.appName} src="/assets/images/logo-small.png" />
            <div className={styles.app}>
              {isAuthLoading && (
                <>
                  <Skeleton
                    variant="text"
                    width={100}
                    height={24}
                    sx={{ mt: 1 }}
                  />
                  <Skeleton
                    variant="text"
                    width={100}
                    height={12}
                    sx={{ mt: 1 }}
                  />
                </>
              )}
              {!isAuthLoading && (
                <>
                  <h3>
                    {!this.props.title
                      ? "Point Of Sales System"
                      : this.props.title}
                  </h3>
                  <p>{!this.props.subtitle ? "" : this.props.subtitle}</p>
                </>
              )}
            </div>
          </div>
          <div className={styles.right}>
            <div className={styles.account}>
              {isAuthLoading && (
                <Skeleton
                  variant="text"
                  width={100}
                  height={24}
                  sx={{ mt: 1 }}
                />
              )}
              {!isAuthLoading && (
                <>
                  <h3>{this.props?.user?.name ?? ""}</h3>
                </>
              )}
              <TimePicker addClass={styles.timepicker} />
            </div>
            {!this.props.hideButton && (
              <>
                <button
                  className={styles.profile}
                  onClick={this.onAccountListeners}
                >
                  <i className="ph ph-bold ph-user"></i>
                </button>
                <button onClick={this.onLogoutListeners}>
                  <i className="ph ph-bold ph-power"></i>
                </button>
              </>
            )}
          </div>
        </div>

        <Loading ref={(value) => (this.ref_Loading = value)} />
      </>
    );
  }
}
