/*
Created by esoda
Created on Nov, 2024
Contact esoda.id
*/

import React from "react";
import {
  Modal,
  Box,
  TextField,
  InputAdornment,
  MenuItem,
  IconButton,
  Chip,
} from "@mui/material";
import Loading from "@/components/modal/Loading";
import Constants from "@/utils/Constants";
import ImageUploadSingle from "@/components/libs/ImageUploadSingle";
import AdminModalListBranch from "@/components/pages/adm/modal/AdminModalListBranch";
import AdminModalListEmployee from "@/components/pages/adm/modal/AdminModalListEmployee";
import ApiHelper from "@/utils/ApiHelper";
import MySnackbar from "@/components/MySnackbar";
import Input from "@/components/libs/Input";

const DEFAULT_INPUTS = {
  id: "",
  name: "",
  username: "",
  email: "",
  password: "",
  password_input_type: "password",
  confirm_password: "",
  confirm_password_input_type: "password",
  image_url: "",
  mobilephone: "",
  role_id: "placeholder",
  role_name: "",
  active_bool: true,
  all_branch_bool: true,
  branch_array: [],
  type: "admin",
  employee_object: null,
};

export default class UsersModalAddUpdate extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showDialog: false,
      formType: "add",
      formData: null,
      formIndex: -1,

      inputs: structuredClone(DEFAULT_INPUTS),
      errors: {},

      getRoleOptions: false,
      roleOptions: [],
    };
  }

  // ====================================================================================
  // ========== INITIALIZE, GET DATA ====================================================
  // ====================================================================================
  onShowDialog = (
    formType,
    formData = null,
    formIndex = -1,
    formInputs = null
  ) => {
    let inputs = structuredClone(DEFAULT_INPUTS);
    if (formInputs) {
      inputs = formInputs;
    }
    this.setState(
      {
        formType,
        formData,
        formIndex,
        showDialog: true,

        inputs,
      },
      () => {
        this.onFetchRole();
        if (formType === "edit") {
          this.onFetchDetail();
        }
        setTimeout(() => {
          var inputNameID = document.getElementById("input-name");
          if (inputNameID) {
            inputNameID.focus();
          }
        }, 250);
      }
    );
  };
  onCloseDialog = () => {
    this.setState({
      showDialog: false,
      formType: "add",
      formData: null,
      formIndex: -1,

      inputs: structuredClone(DEFAULT_INPUTS),
      errors: {},
    });
  };

  onFetchRole = async () => {
    this.setState({ getRoleOptions: true });
    let response = await ApiHelper.get("kiosk/admin/user/role/options", {
      sort: "name",
    });

    let data = [];
    if (response.status === 200) {
      data = response.results.data;
    } else {
      this.onNotify(response.message, "error");
      data = [];
    }
    this.setState({ getRoleOptions: false, roleOptions: data });
  };

  onFetchDetail = async () => {
    this.ref_Loading.onShowDialog();
    let response = await ApiHelper.get("kiosk/admin/user/detail", {
      id: this.state.formData.id,
    });

    let inputs = structuredClone(this.state.inputs);
    if (response.status === 200) {
      inputs = { ...structuredClone(inputs), ...response.results.data };
    } else {
      this.onNotify(response.message, "error");
      this.onCloseDialog();
    }
    this.ref_Loading.onCloseDialog();
    this.setState({ inputs });
  };

  // ====================================================================================
  // ========== ACTION LISTENERS ========================================================
  // ====================================================================================
  onNotify = (message, severity) => {
    if (this.ref_MySnackbar) {
      this.ref_MySnackbar.onNotify(message, severity);
    }
  };
  onTextInputListeners = (text, input) => {
    let inputs = this.state.inputs;
    inputs[input] = text;
    this.setState((prevState) => ({ ...prevState, inputs }));
  };
  onTextErrorListeners = (error, input) => {
    let errors = this.state.errors;
    errors[input] = error;
    this.setState((prevState) => ({ ...prevState, errors }));
  };
  onValidateListeners = () => {
    if (this.state.formType === "add" || this.state.formType === "edit") {
      let inputs = this.state.inputs;
      let isValid = true;

      let errorArr = [];
      if (!inputs.name) {
        this.onTextErrorListeners("Harus diisi", "name");
        errorArr.push("Nama Pengguna harus diisi.");
      }
      if (!inputs.username) {
        this.onTextErrorListeners("Harus diisi", "username");
        errorArr.push("Username harus diisi.");
      }
      if (this.state.formType === "add") {
        if (!inputs.password) {
          this.onTextErrorListeners("Harus diisi", "password");
          errorArr.push("Password harus diisi.");
        }
        if (!inputs.confirm_password) {
          this.onTextErrorListeners("Harus diisi", "confirm_password");
          errorArr.push("Konfirmasi Password harus diisi.");
        }
      }
      if (inputs.password !== inputs.confirm_password) {
        this.onTextErrorListeners(
          "Tidak cocok dengan password",
          "confirm_password"
        );
        errorArr.push("Konfirmasi Password tidak cocok dengan password.");
      }
      if (inputs.role_id === "placeholder") {
        this.onTextErrorListeners("Harus dipilih", "role_id");
        errorArr.push("Hak Akses Pengguna harus dipilih.");
      }
      if (inputs.active_bool === null) {
        this.onTextErrorListeners("Harus dipilih", "active_bool");
        errorArr.push("Status Aktif harus dipilih.");
      }
      if (inputs.all_branch_bool === null) {
        this.onTextErrorListeners("Harus dipilih", "all_branch_bool");
        errorArr.push("Akses Semua Cabang harus dipilih.");
      }
      if (!inputs.all_branch_bool && inputs.branch_array.length === 0) {
        this.onTextErrorListeners("Harus dipilih", "branch_array");
        errorArr.push("Cabang harus dipilih.");
      }
      if (inputs.type === null) {
        this.onTextErrorListeners("Harus dipilih", "type");
        errorArr.push("Tipe Pengguna harus dipilih.");
      }

      if (errorArr.length > 0) {
        this.onNotify(errorArr.join("\n"), "error");
        return;
      }
      this.actOnSaveListeners();
    }
  };
  actOnSaveListeners = async () => {
    this.ref_Loading.onShowDialog();

    let params = structuredClone(this.state.inputs);
    params.employee_id = params.employee_object?.id || "0";

    const response = await ApiHelper.post("kiosk/admin/user/save", params);

    if (response.status === 200) {
      const { onResults } = this.props;
      if (onResults) {
        onResults(
          this.state.formType,
          this.state.formData,
          this.state.formIndex
        );
      }
      this.onCloseDialog();

      this.onNotify(response?.message || "Berhasil Simpan Data", "success");
    } else {
      this.onNotify(response?.message || "Terjadi Kesalahan", "error");
    }

    this.ref_Loading.onCloseDialog();
  };

  onDeleteBranch = (index) => {
    let inputs = this.state.inputs;
    inputs.branch_array.splice(index, 1);
    this.setState((prevState) => ({ ...prevState, inputs }));
  };

  onSelectBranch = () => {
    this.ref_AdminModalListBranch.onShowDialog("list", {
      apiEndpoint: "kiosk/admin/user/branch/options",
      apiParams: {
        sort: "aol_session_name,name",
      },
      selected: this.state.inputs.branch_array,
    });
  };

  onSelectBranchListeners = (selectedBranches) => {
    let inputs = this.state.inputs;
    inputs.branch_array = selectedBranches;
    this.setState((prevState) => ({ ...prevState, inputs }));
  };

  onSelectEmployee = () => {
    let selected = [];
    if (this.state.inputs.employee_object !== null) {
      selected.push(this.state.inputs.employee_object);
    }
    this.ref_AdminModalListEmployee.onShowDialog("list", {
      // apiEndpoint: "kiosk/admin/user/employee/options",
      apiParams: {
        sort: "aol_session_name,name",
      },
      selected,
    });
  };

  onRemoveEmployee = () => {
    let inputs = this.state.inputs;
    inputs.employee_object = null;
    this.setState((prevState) => ({ ...prevState, inputs }));
  };

  onSelectEmployeeListeners = (selectedEmployee) => {
    let inputs = this.state.inputs;
    inputs.employee_object = selectedEmployee[0];
    this.setState((prevState) => ({ ...prevState, inputs }));
  };

  // ====================================================================================
  // ========== RENDER SECTIONS =========================================================
  // ====================================================================================
  render() {
    let addModalClass = "small";
    return (
      <>
        <Modal open={this.state.showDialog} onClose={this.onCloseDialog}>
          <Box
            sx={{
              flex: 1,
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              display: "flex",
              width: "100wh",
              height: "100vh",
            }}
          >
            <div className={`modal-content ${addModalClass}`}>
              <div className="modal-header">{this.renderHeader()}</div>
              <div className="modal-body">{this.renderBody()}</div>
              <div className="modal-footer">
                {this.renderFooter()}
                <button
                  className="button cancel"
                  onClick={() => this.onCloseDialog()}
                >
                  <i className="ph ph-bold ph-x-circle"></i>
                  <span>
                    {this.state.formType === "info" ? "Tutup" : "Batal"}
                  </span>
                </button>
              </div>
            </div>
          </Box>
        </Modal>

        {/* Loading dialog */}
        <Loading ref={(value) => (this.ref_Loading = value)} />
        <MySnackbar ref={(value) => (this.ref_MySnackbar = value)} />

        <AdminModalListBranch
          ref={(value) => (this.ref_AdminModalListBranch = value)}
          onSelect={(selectedBranches) =>
            this.onSelectBranchListeners(selectedBranches)
          }
        />

        <AdminModalListEmployee
          ref={(value) => (this.ref_AdminModalListEmployee = value)}
          onSelect={(selectedEmployee) =>
            this.onSelectEmployeeListeners(selectedEmployee)
          }
        />
      </>
    );
  }

  renderHeader() {
    return (
      <>
        <div className="title">
          {this.state.formType === "add" && `Tambah Data Pengguna`}
          {this.state.formType === "edit" && `Ubah Data Pengguna`}
        </div>
        <span className="close" onClick={() => this.onCloseDialog()}>
          &times;
        </span>
      </>
    );
  }

  renderBody() {
    return (
      <>
        {(this.state.formType === "add" || this.state.formType === "edit") &&
          this.renderForm()}
      </>
    );
  }

  renderForm() {
    return (
      <>
        <div className="flex-rows no-right">
          <div className="row no-border">
            {/*=== INPUT TYPE */}
            {/* select, kiosk, admin */}
            <div className="input-form">
              <Input
                label="Tipe Pengguna"
                inputType="select"
                value={this.state.inputs.type}
                onChange={(event) => {
                  if (
                    event.target !== undefined &&
                    event.target.value !== undefined
                  ) {
                    let inputs = structuredClone(this.state.inputs);
                    inputs.type = event.target.value;
                    inputs.role_id = "placeholder";
                    inputs.role_name = "";
                    this.setState({ inputs });
                  }
                }}
                error={
                  this.state.errors.type !== undefined &&
                  this.state.errors.type !== null
                    ? true
                    : false
                }
                onFocus={() => this.onTextErrorListeners(null, "type")}
                options={[
                  { value: "kiosk", label: "Kiosk" },
                  { value: "admin", label: "Admin" },
                ]}
                required
              />
            </div>
            {/* button pilih karyawan, display karyawan if employee_object not null  */}
            <div className="input-form">
              {this.state.inputs.employee_object !== null && (
                <>
                  <div className="label">Karyawan Dipengguna Ini</div>
                  <div className="mt-1 border border-dashed rounded p-4 flex flex-col gap-4 py-4">
                    <div className="flex flex-col gap-4 py-4">
                      <div className="detail_container">
                        <em>Nama Karyawan</em>
                        <div className="content text">
                          {this.state.inputs.employee_object.name}
                        </div>
                      </div>
                      <div className="detail_container">
                        <em>Kode Karyawan</em>
                        <div className="content text">
                          {this.state.inputs.employee_object.code}
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              )}
              {/* null, show text pilih karyawan */}
              {this.state.inputs.employee_object === null && (
                <>
                  <div className="input-form border border-dashed rounded p-4 flex flex-col gap-4 py-4 justify-center items-center">
                    <p>-- Silahkan Pilih Karyawan --</p>
                  </div>
                </>
              )}

              <div className="input-form flex justify-end">
                <button
                  className="button"
                  onClick={() => {
                    this.onSelectEmployee();
                  }}
                >
                  {/* company branch */}
                  <i className="ph ph-bold ph-users"></i>
                  <span>
                    {this.state.inputs.employee_object === null
                      ? "Pilih"
                      : "Ubah"}{" "}
                    Karyawan
                  </span>
                </button>
                {this.state.inputs.employee_object !== null && (
                  <button
                    className="button cancel"
                    onClick={() => {
                      this.onRemoveEmployee();
                    }}
                  >
                    <i className="ph ph-bold ph-x-circle"></i>
                    <span>Tanpa Karyawan</span>
                  </button>
                )}
              </div>

              <div className="placeholder">
                <div className="optional">Opsional</div>
                <div className="note"></div>
              </div>
            </div>

            {/*=== INPUT IMAGE */}
            <div className="input-form">
              <ImageUploadSingle
                id={"image_url"}
                title={"Foto Pengguna"}
                placeholder={"Foto Pengguna..."}
                image_url={this.state.inputs.image_url}
                error={this.state.errors.image_url}
                onCompleted={(image_url) => {
                  this.onTextInputListeners(image_url, "image_url");
                  this.onTextErrorListeners(null, "image_url");
                }}
                style={{ height: "200px" }}
                optional
              />
            </div>

            {/*=== INPUT NAME */}
            <div className="input-form">
              <Input
                id="input-name"
                label="Nama Pengguna"
                placeholder="Tuliskan disini..."
                value={this.state.inputs.name}
                onChange={(event) => {
                  if (
                    event.target !== undefined &&
                    event.target.value !== undefined
                  ) {
                    if (event.target.value.length <= 50) {
                      this.onTextInputListeners(event.target.value, "name");
                    }
                  }
                }}
                error={
                  this.state.errors.name !== undefined &&
                  this.state.errors.name !== null
                    ? this.state.errors.name
                    : null
                }
                onFocus={() => this.onTextErrorListeners(null, "name")}
                maxLength={50}
                required
              />
            </div>
            {/*=== INPUT USERNAME */}
            <div className="input-form">
              <Input
                label="Username"
                placeholder="Tuliskan disini..."
                value={this.state.inputs.username}
                onChange={(event) => {
                  if (
                    event.target !== undefined &&
                    event.target.value !== undefined
                  ) {
                    if (event.target.value.length <= 50) {
                      this.onTextInputListeners(event.target.value, "username");
                    }
                  }
                }}
                error={
                  this.state.errors.username !== undefined &&
                  this.state.errors.username !== null
                    ? this.state.errors.username
                    : null
                }
                onFocus={() => this.onTextErrorListeners(null, "username")}
                maxLength={50}
                required
              />
            </div>
            {this.state.formType === "add" && (
              <>
                {/*=== INPUT PASSWORD */}
                <div className="input-form">
                  <Input
                    label="Password"
                    type={this.state.inputs.password_input_type}
                    endAdornment={
                      <IconButton
                        onClick={() => {
                          this.onTextInputListeners(
                            this.state.inputs.password_input_type === "text"
                              ? "password"
                              : "text",
                            "password_input_type"
                          );
                        }}
                      >
                        <i
                          className={`ph ph-bold ${
                            this.state.inputs.password_input_type === "text"
                              ? "ph-eye"
                              : "ph-eye-slash"
                          }`}
                          style={{ marginRight: "0px" }}
                        ></i>
                      </IconButton>
                    }
                    placeholder="Tuliskan disini..."
                    value={this.state.inputs.password}
                    onChange={(event) => {
                      if (
                        event.target !== undefined &&
                        event.target.value !== undefined
                      ) {
                        if (event.target.value.length <= 50) {
                          this.onTextInputListeners(
                            event.target.value,
                            "password"
                          );
                        }
                      }
                    }}
                    error={
                      this.state.errors.password !== undefined &&
                      this.state.errors.password !== null
                        ? this.state.errors.password
                        : null
                    }
                    onFocus={() => this.onTextErrorListeners(null, "password")}
                    maxLength={50}
                    required
                  />
                </div>
                {/* === INPUT CONFIRM PASSWORD */}
                <div className="input-form">
                  <Input
                    label="Konfirmasi Password"
                    type={this.state.inputs.confirm_password_input_type}
                    endAdornment={
                      <IconButton
                        onClick={() => {
                          this.onTextInputListeners(
                            this.state.inputs.confirm_password_input_type ===
                              "text"
                              ? "password"
                              : "text",
                            "confirm_password_input_type"
                          );
                        }}
                      >
                        <i
                          className={`ph ph-bold ${
                            this.state.inputs.confirm_password_input_type ===
                            "text"
                              ? "ph-eye"
                              : "ph-eye-slash"
                          }`}
                          style={{ marginRight: "0px" }}
                        ></i>
                      </IconButton>
                    }
                    placeholder="Tuliskan disini..."
                    value={this.state.inputs.confirm_password}
                    onChange={(event) => {
                      if (
                        event.target !== undefined &&
                        event.target.value !== undefined
                      ) {
                        if (event.target.value.length <= 50) {
                          this.onTextInputListeners(
                            event.target.value,
                            "confirm_password"
                          );
                        }
                      }
                    }}
                    error={
                      this.state.errors.confirm_password !== undefined &&
                      this.state.errors.confirm_password !== null
                        ? this.state.errors.confirm_password
                        : null
                    }
                    onFocus={() =>
                      this.onTextErrorListeners(null, "confirm_password")
                    }
                    maxLength={50}
                    required
                  />
                </div>
              </>
            )}

            {/*=== INPUT ROLE */}
            {this.state.inputs.type === "admin" && (
              <>
                <div className="input-form">
                  <Input
                    label="Hak Akses Pengguna"
                    inputType="select"
                    value={this.state.inputs.role_id}
                    onChange={(event) => {
                      if (
                        event.target !== undefined &&
                        event.target.value !== undefined
                      ) {
                        const find = this.state.roleOptions.find(
                          (item) => item.id === event.target.value
                        );
                        if (find) {
                          this.onTextInputListeners(find.name, "role_name");
                          this.onTextInputListeners(
                            event.target.value,
                            "role_id"
                          );
                        }
                      }
                    }}
                    error={
                      this.state.errors.role_id !== undefined &&
                      this.state.errors.role_id !== null
                        ? this.state.errors.role_id
                        : null
                    }
                    onFocus={() => {
                      this.onTextErrorListeners(null, "role_id");
                      this.onTextErrorListeners(null, "role_name");
                    }}
                    options={[
                      {
                        value: "placeholder",
                        label: "Pilih Hak Akses",
                        disabled: true,
                      },
                      ...(this.state.roleOptions.length > 0
                        ? this.state.roleOptions.map((item) => ({
                            value: item.id,
                            label: item.name,
                          }))
                        : []),
                    ]}
                    required
                  />
                </div>
              </>
            )}
            {/*=== INPUT STATUS */}
            <div className="input-form">
              <Input
                label="Status Aktif"
                inputType="select"
                startAdornment={
                  <InputAdornment position="start">
                    <i
                      className={`ml-3 ph ph-bold ph-toggle-left ${
                        this.state.inputs.active_bool
                          ? "text-green"
                          : "text-red"
                      }`}
                    ></i>
                  </InputAdornment>
                }
                value={this.state.inputs.active_bool}
                onChange={(event) => {
                  if (
                    event.target !== undefined &&
                    event.target.value !== undefined
                  ) {
                    this.onTextInputListeners(
                      event.target.value === "true" ? true : false,
                      "active_bool"
                    );
                  }
                }}
                error={
                  this.state.errors.active_bool !== undefined &&
                  this.state.errors.active_bool !== null
                    ? this.state.errors.active_bool
                    : null
                }
                onFocus={() => this.onTextErrorListeners(null, "active_bool")}
                options={[
                  { value: true, label: "Aktif" },
                  { value: false, label: "Tidak Aktif" },
                ]}
                required
              />
            </div>

            {/* === INPUT ALL BRANCH */}
            <div className="input-form">
              <div className="flex flex-row gap-4">
                <div className="flex-1">
                  <Input
                    label="Akses Semua Cabang"
                    inputType="select"
                    startAdornment={
                      <InputAdornment position="start">
                        <i
                          className={`ml-3 ph ph-bold ph-toggle-left ${
                            this.state.inputs.all_branch_bool
                              ? "text-green"
                              : "text-red"
                          }`}
                        ></i>
                      </InputAdornment>
                    }
                    value={this.state.inputs.all_branch_bool}
                    defaultValue={this.state.inputs.all_branch_bool}
                    onChange={(event) => {
                      if (
                        event.target !== undefined &&
                        event.target.value !== undefined
                      ) {
                        this.onTextInputListeners(
                          event.target.value === "true" ? true : false,
                          "all_branch_bool"
                        );
                      }
                    }}
                    error={
                      this.state.errors.all_branch_bool !== undefined &&
                      this.state.errors.all_branch_bool !== null
                        ? true
                        : false
                    }
                    onFocus={() =>
                      this.onTextErrorListeners(null, "all_branch_bool")
                    }
                    options={[
                      { value: true, label: "Ya" },
                      { value: false, label: "Tidak" },
                    ]}
                    required
                  />
                </div>
              </div>
            </div>

            {!this.state.inputs.all_branch_bool && (
              <>
                {this.state.inputs.branch_array.length > 0 && (
                  <div className="input-form">
                    <div className="label">Cabang Dipilih</div>
                    <div className="mt-1 border border-dashed rounded flex flex-col gap-4 p-4">
                      <div className="flex flex-row flex-wrap gap-4">
                        {this.state.inputs.branch_array.map((item, index) => {
                          return (
                            <>
                              {/* mui chip with delete */}
                              <Chip
                                key={index}
                                label={item.name}
                                onDelete={() => {
                                  this.onDeleteBranch(index);
                                }}
                              />
                            </>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                )}
                {this.state.inputs.branch_array.length === 0 && (
                  <>
                    <div className="input-form border border-dashed rounded flex flex-col gap-4 py-4 justify-center items-center">
                      <p>-- Silahkan Pilih Cabang --</p>
                    </div>
                  </>
                )}
              </>
            )}
            {!this.state.inputs.all_branch_bool && (
              <div className="input-form">
                <div className="flex flex-row justify-end">
                  <button
                    className="button"
                    onClick={() => {
                      this.onSelectBranch();
                    }}
                  >
                    {/* company branch */}
                    <i className="ph ph-bold ph-building"></i>
                    <span>Pilih Cabang</span>
                  </button>
                </div>
                <div className="placeholder">
                  <div className="required">
                    {this.state.errors.branch_array !== undefined &&
                    this.state.errors.branch_array !== null
                      ? this.state.errors.branch_array
                      : "Harus diisi"}
                  </div>
                  <div className="note"></div>
                </div>
              </div>
            )}
          </div>
        </div>
      </>
    );
  }

  renderFooter() {
    return (
      <>
        <button
          className={`button ${this.state.formType === "edit" && "warning"} ${
            this.state.formType === "delete" && "danger"
          }`}
          onClick={() => {
            this.onValidateListeners();
          }}
        >
          {this.state.formType === "add" && (
            <i className="ph ph-bold ph-check-circle"></i>
          )}
          {this.state.formType === "edit" && (
            <i className="ph ph-bold ph-pencil"></i>
          )}
          <span>
            {this.state.formType === "add" && "Tambah Data"}
            {this.state.formType === "edit" && "Ubah Data"}
          </span>
        </button>
      </>
    );
  }
}
