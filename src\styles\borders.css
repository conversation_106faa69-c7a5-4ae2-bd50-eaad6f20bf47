/* --- Tailwind-like Border CSS --- */

/* 0. CSS Custom Properties (Variables) */
:root {
  --border-default-color: var(--border-color); /* Example: Gray 300 */

  /* Colors (add as many as you need) */
  --border-color-transparent: transparent;

  /* Border Radii */
  --rounded-none: 0px;
  --rounded-sm: 0.125rem; /* 2px */
  --rounded: 0.25rem; /* 4px */
  --rounded-md: 0.375rem; /* 6px */
  --rounded-lg: 0.5rem; /* 8px */
  --rounded-xl: 0.75rem; /* 12px */
  --rounded-2xl: 1rem; /* 16px */
  --rounded-3xl: 1.5rem; /* 24px */
  --rounded-full: 9999px;
}

/* 1. Base Border Styles (Preflight-like reset for borders) */
*,
::before,
::after {
  box-sizing: border-box; /* Recommended for all elements */
  border-width: 0; /* Reset border width */
  border-style: solid; /* Default border style */
  border-color: var(--border-default-color); /* Default border color */
}

/* 2. Border Width Utilities */
.border-0 {
  border-width: 0px !important;
}
.border {
  border-width: 1px !important;
}
.border-2 {
  border-width: 2px !important;
}
.border-4 {
  border-width: 4px !important;
}
.border-8 {
  border-width: 8px !important;
}

/* Individual Side Border Widths */
.border-t-0 {
  border-top-width: 0px !important;
}
.border-t {
  border-top-width: 1px !important;
}
.border-t-2 {
  border-top-width: 2px !important;
}
.border-t-4 {
  border-top-width: 4px !important;
}
.border-t-8 {
  border-top-width: 8px !important;
}

.border-r-0 {
  border-right-width: 0px !important;
}
.border-r {
  border-right-width: 1px !important;
}
.border-r-2 {
  border-right-width: 2px !important;
}
.border-r-4 {
  border-right-width: 4px !important;
}
.border-r-8 {
  border-right-width: 8px !important;
}

.border-b-0 {
  border-bottom-width: 0px !important;
}
.border-b {
  border-bottom-width: 1px !important;
}
.border-b-2 {
  border-bottom-width: 2px !important;
}
.border-b-4 {
  border-bottom-width: 4px !important;
}
.border-b-8 {
  border-bottom-width: 8px !important;
}

.border-l-0 {
  border-left-width: 0px !important;
}
.border-l {
  border-left-width: 1px !important;
}
.border-l-2 {
  border-left-width: 2px !important;
}
.border-l-4 {
  border-left-width: 4px !important;
}
.border-l-8 {
  border-left-width: 8px !important;
}

/* X and Y axis borders (Tailwind specific) */
.border-x-0 {
  border-left-width: 0px !important;
  border-right-width: 0px !important;
}
.border-x {
  border-left-width: 1px !important;
  border-right-width: 1px !important;
}
.border-x-2 {
  border-left-width: 2px !important;
  border-right-width: 2px !important;
}
/* ...etc for x-4, x-8 */

.border-y-0 {
  border-top-width: 0px !important;
  border-bottom-width: 0px !important;
}
.border-y {
  border-top-width: 1px !important;
  border-bottom-width: 1px !important;
}
.border-y-2 {
  border-top-width: 2px !important;
  border-bottom-width: 2px !important;
}
/* ...etc for y-4, y-8 */

/* 3. Border Color Utilities */
.border-transparent {
  border-color: var(--border-color-transparent) !important;
}

/* Individual Side Border Colors (Example: Tailwind combines these, e.g. border-t-red-500) */
/* For simplicity here, we'll rely on specificity or global color. */
/* To have border-t-red-500, you'd need: */
/* .border-t-red-500 { border-top-color: var(--color-red-500) !important; } */
/* This can get very verbose. Tailwind generates these. */
/* For this manual version, you'd typically use: class="border-t-2 border-red-500" */
/* If you need ONLY the top border to be red, you'd need a more specific class or custom CSS. */

/* 4. Border Style Utilities */
.border-solid {
  border-style: solid !important;
}
.border-dashed {
  border-style: dashed !important;
}
.border-dotted {
  border-style: dotted !important;
}
.border-double {
  border-style: double !important;
}
.border-none {
  border-style: none !important;
} /* Note: border-0 is for width */

/* 5. Border Radius Utilities */
.rounded-none {
  border-radius: var(--rounded-none) !important;
}
.rounded-sm {
  border-radius: var(--rounded-sm) !important;
}
.rounded {
  border-radius: var(--rounded) !important;
}
.rounded-md {
  border-radius: var(--rounded-md) !important;
}
.rounded-lg {
  border-radius: var(--rounded-lg) !important;
}
.rounded-xl {
  border-radius: var(--rounded-xl) !important;
}
.rounded-2xl {
  border-radius: var(--rounded-2xl) !important;
}
.rounded-3xl {
  border-radius: var(--rounded-3xl) !important;
}
.rounded-full {
  border-radius: var(--rounded-full) !important;
}

/* Per-side rounding */
.rounded-t-none {
  border-top-left-radius: var(--rounded-none) !important;
  border-top-right-radius: var(--rounded-none) !important;
}
.rounded-t-sm {
  border-top-left-radius: var(--rounded-sm) !important;
  border-top-right-radius: var(--rounded-sm) !important;
}
.rounded-t {
  border-top-left-radius: var(--rounded) !important;
  border-top-right-radius: var(--rounded) !important;
}
.rounded-t-md {
  border-top-left-radius: var(--rounded-md) !important;
  border-top-right-radius: var(--rounded-md) !important;
}
.rounded-t-lg {
  border-top-left-radius: var(--rounded-lg) !important;
  border-top-right-radius: var(--rounded-lg) !important;
}
.rounded-t-xl {
  border-top-left-radius: var(--rounded-xl) !important;
  border-top-right-radius: var(--rounded-xl) !important;
}
.rounded-t-2xl {
  border-top-left-radius: var(--rounded-2xl) !important;
  border-top-right-radius: var(--rounded-2xl) !important;
}
.rounded-t-3xl {
  border-top-left-radius: var(--rounded-3xl) !important;
  border-top-right-radius: var(--rounded-3xl) !important;
}
.rounded-t-full {
  border-top-left-radius: var(--rounded-full) !important;
  border-top-right-radius: var(--rounded-full) !important;
}

/* Repeat for .rounded-r-*, .rounded-b-*, .rounded-l-* */
.rounded-r-md {
  border-top-right-radius: var(--rounded-md) !important;
  border-bottom-right-radius: var(--rounded-md) !important;
}
.rounded-b-md {
  border-bottom-left-radius: var(--rounded-md) !important;
  border-bottom-right-radius: var(--rounded-md) !important;
}
.rounded-l-md {
  border-top-left-radius: var(--rounded-md) !important;
  border-bottom-left-radius: var(--rounded-md) !important;
}
/* ... and other sizes for r, b, l */

/* Per-corner rounding */
.rounded-tl-none {
  border-top-left-radius: var(--rounded-none) !important;
}
.rounded-tl-sm {
  border-top-left-radius: var(--rounded-sm) !important;
}
.rounded-tl {
  border-top-left-radius: var(--rounded) !important;
}
.rounded-tl-md {
  border-top-left-radius: var(--rounded-md) !important;
}
.rounded-tl-lg {
  border-top-left-radius: var(--rounded-lg) !important;
}
.rounded-tl-xl {
  border-top-left-radius: var(--rounded-xl) !important;
}
.rounded-tl-2xl {
  border-top-left-radius: var(--rounded-2xl) !important;
}
.rounded-tl-3xl {
  border-top-left-radius: var(--rounded-3xl) !important;
}
.rounded-tl-full {
  border-top-left-radius: var(--rounded-full) !important;
}

/* Repeat for .rounded-tr-*, .rounded-br-*, .rounded-bl-* */
.rounded-tr-md {
  border-top-right-radius: var(--rounded-md) !important;
}
.rounded-br-md {
  border-bottom-right-radius: var(--rounded-md) !important;
}
.rounded-bl-md {
  border-bottom-left-radius: var(--rounded-md) !important;
}
/* ... and other sizes for tr, br, bl */

/* --- End of Tailwind-like Border CSS --- */
