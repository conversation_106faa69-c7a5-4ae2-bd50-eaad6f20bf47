/*
Created by esoda
Created on Nov, 2024
Contact esoda.id
*/

import React from "react";
import HtmlHead from "./HtmlHead";
import Constants from "@/utils/Constants";
import Router from "next/router";

const ARR_MENU = [
    {
        "title": "Beranda",
        "icon": "ph-house",
        "link": "/admin/dashboard",
        "active": false,
        "menu": []
    },
    {
        "title": "Data Master",
        "icon": "ph-database",
        "link": "#",
        "active": false,
        "menu": [
            {
                "title": "Barang",
                "link": "#",
                "active": false,
                "menu": [
                    {
                        "title": "Satuan Unit",
                        "link": "/admin/master/product/units",
                        "active": false,
                    },
                    {
                        "title": "Kategori",
                        "link": "/admin/master/product/categories",
                        "active": false,
                    },
                    {
                        "title": "Bahan Baku",
                        "link": "/admin/master/product/materials",
                        "active": false,
                    },
                    {
                        "title": "Barang Jadi",
                        "link": "/admin/master/product/goods",
                        "active": false,
                    },
                    {
                        "title": "Resep / Komposisi",
                        "link": "/admin/master/product/recipes",
                        "active": false,
                    }
                ]
            },
            {
                "title": "Gudang",
                "link": "/admin/master/warehouse",
                "active": false,
                "menu": []
            },
            {
                "title": "Suplier",
                "link": "/admin/master/supplier",
                "active": false,
                "menu": []
            },
            {
                "title": "Vendor (Konsinyasi)",
                "link": "/admin/master/vendor",
                "active": false,
                "menu": []
            },
            {
                "title": "Toko / Outlet / Hub",
                "link": "/admin/master/outlet",
                "active": false,
                "menu": []
            },
            {
                "title": "Pelanggan",
                "link": "/admin/master/customer",
                "active": false,
                "menu": []
            },
            // {
            //     "title": "Armada (Fleet)",
            //     "link": "/admin/master/fleet",
            //     "active": false,
            //     "menu": []
            // },
        ]
    },
    {
        "title": "Stok & Persediaan",
        "icon": "ph-stack",
        "link": "#",
        "active": false,
        "menu": [
            {
                "title": "Data Stok",
                "link": "#",
                "active": false,
                "menu": [
                    {
                        "title": "Ringkasan Stok",
                        "link": "/admin/inventory/summary",
                        "active": false,
                    },
                    {
                        "title": "Kartu Stok",
                        "link": "/admin/inventory/stockcard",
                        "active": false,
                    },
                    {
                        "title": "Mutasi Stok",
                        "link": "/admin/inventory/mutation",
                        "active": false,
                    },
                    {
                        "title": "Penyesuaian Stok",
                        "link": "/admin/inventory/adjustment",
                        "active": false,
                    },
                    {
                        "title": "Opnam Stok",
                        "link": "/admin/inventory/opname",
                        "active": false,
                    },
                    {
                        "title": "Tambah Stok",
                        "link": "/admin/inventory/add",
                        "active": false,
                    },
                    {
                        "title": "Reset Stok",
                        "link": "/admin/inventory/reset",
                        "active": false,
                    }
                ]
            },
            {
                "title": "Pemindahan Barang",
                "link": "/admin/inventory/moving",
                "active": false,
                "menu": []
            },
            {
                "title": "Penerimaan Barang",
                "link": "/admin/inventory/receipt",
                "active": false,
                "menu": []
            },
            {
                "title": "Penggunaan Barang",
                "link": "/admin/inventory/issue",
                "active": false,
                "menu": []
            },
        ]
    },
    {
        "title": "Transaksi",
        "icon": "ph-cash-register",
        "link": "#",
        "active": false,
        "menu": [
            {
                "title": "Purchase Order",
                "link": "#",
                "active": false,
                "menu": [
                    {
                        "title": "Pembelian Barang",
                        "link": "/admin/transaction/purchaseorder",
                        "active": false,
                    },
                    {
                        "title": "Pengembalian Barang",
                        "link": "/admin/transaction/purchaseorder/retur",
                        "active": false,
                    }
                ]
            },
            {
                "title": "Sales Order",
                "link": "#",
                "active": false,
                "menu": [
                    {
                        "title": "Penjualan Barang",
                        "link": "/admin/transaction/salesorder",
                        "active": false,
                    },
                    // {
                    //     "title": "Pengembalian Barang",
                    //     "link": "/admin/transaction/salesorder/retur",
                    //     "active": false,
                    // },
                    // {
                    //     "title": "Kunjungan Sales",
                    //     "link": "/admin/transaction/salesorder/visit",
                    //     "active": false,
                    // }
                ]
            },
            {
                "title": "Job Order / Job Costing",
                "link": "/admin/transaction/joborder",
                "active": false,
                "menu": []
            },
            {
                "title": "Packing Order",
                "link": "/admin/transaction/packingorder",
                "active": false,
                "menu": []
            },
            {
                "title": "Delivery Order",
                "link": "#",
                "active": false,
                "menu": [
                    {
                        "title": "Data Pengiriman",
                        "link": "/admin/transaction/deliveryorder",
                        "active": false,
                    },
                    {
                        "title": "Jadwal Pengiriman",
                        "link": "/admin/transaction/deliveryorder/schedule",
                        "active": false,
                    },
                    {
                        "title": "Surat Jalan (POD)",
                        "link": "/admin/transaction/deliveryorder/pod",
                        "active": false,
                    },
                    {
                        "title": "Monitoring Pengiriman",
                        "link": "/admin/transaction/deliveryorder/monitoring",
                        "active": false,
                    },
                    {
                        "title": "Data Pengembalian",
                        "link": "/admin/transaction/deliveryorder/retur",
                        "active": false,
                    }
                ]
            },
        ]
    },
    {
        "title": "Akunting",
        "icon": "ph-bank",
        "link": "#",
        "active": false,
        "menu": [
            {
                "title": "Chart of Account (CoA)",
                "link": "/admin/accounting/coa",
                "active": false,
                "menu": []
            },
            {
                "title": "Atur Saldo Awal",
                "link": "/admin/accounting/balance-initial",
                "active": false,
                "menu": []
            },
            {
                "title": "Balance Sheet (Neraca)",
                "link": "/admin/accounting/balance-sheet",
                "active": false,
                "menu": []
            },
            {
                "title": "Profit & Loss (Laba Rugi)",
                "link": "/admin/accounting/profit-loss",
                "active": false,
                "menu": []
            },
            {
                "title": "General Ledger (Buku Besar)",
                "link": "/admin/accounting/general-ledger",
                "active": false,
                "menu": []
            },
            {
                "title": "Journal",
                "link": "/admin/accounting/journal",
                "active": false,
                "menu": []
            }
        ]
    },
    // {
    //     "title": "Laporan",
    //     "icon": "ph-file-text",
    //     "link": "#",
    //     "active": false,
    //     "menu": [
    //         {
    //             "title": "Purchase Order",
    //             "link": "#",
    //             "active": false,
    //             "menu": []
    //         },
    //         {
    //             "title": "Goods Receipt",
    //             "link": "#",
    //             "active": false,
    //             "menu": []
    //         },
    //         {
    //             "title": "Sales Order",
    //             "link": "#",
    //             "active": false,
    //             "menu": []
    //         },
    //         {
    //             "title": "Issue to Production",
    //             "link": "#",
    //             "active": false,
    //             "menu": []
    //         },
    //         {
    //             "title": "Receipt from Production",
    //             "link": "#",
    //             "active": false,
    //             "menu": []
    //         },
    //         {
    //             "title": "Delivery Order",
    //             "link": "#",
    //             "active": false,
    //             "menu": []
    //         }
    //     ]
    // },
    {
        "title": "Pengaturan",
        "icon": "ph-gear",
        "link": "#",
        "active": false,
        "menu": [
            {
                "title": "Pengguna & Akses Sistem",
                "link": "/admin/setting/user",
                "active": false,
                "menu": []
            },
            // {
            //     "title": "Stok & Persediaan",
            //     "link": "/admin/setting/stock",
            //     "active": false,
            //     "menu": []
            // },
            // {
            //     "title": "Purchase Order",
            //     "link": "#",
            //     "active": false,
            //     "menu": []
            // },
            // {
            //     "title": "Sales Order",
            //     "link": "#",
            //     "active": false,
            //     "menu": []
            // },
            // {
            //     "title": "Packing Order",
            //     "link": "#",
            //     "active": false,
            //     "menu": []
            // },
            // {
            //     "title": "Delivery Order",
            //     "link": "#",
            //     "active": false,
            //     "menu": []
            // }
        ]
    }
]

export default class PageLayout extends React.Component {
    constructor(props) {
        super(props)
        this.state = {
            arrMenu: ARR_MENU,
            pathname: '',
        }

        this._FirstMount = true
    }
    componentDidMount() {
        if (Router !== undefined && Router.pathname !== undefined){
            if(this._FirstMount){
                this._FirstMount = false
                this.setActiveMenuListener()
            }
        }
    }

    // ====================================================================================
    // ========== INITIALIZE, GET DATA ====================================================
    // ====================================================================================
    setActiveMenuListener = () => {
        let arrMenu = this.state.arrMenu
        arrMenu.map(item => {
            item.active = false
            if(item.menu.length <= 0) {
                if(item.link === Router.pathname){
                    item.active = true
                }
            } else {
                item.menu.map(submenu => {
                    submenu.active = false
                    if(submenu.menu.length <= 0) {
                        if(submenu.link === Router.pathname) {
                            submenu.active = true
                            item.active = true
                        }
                    } else {
                        submenu.menu.map(moremenu => {
                            moremenu.active = false
                            if(moremenu.link === Router.pathname) {
                                moremenu.active = true
                                submenu.active = true
                                item.active = true
                            }
                        })
                    }
                })
            }
        })
        this.setState({ arrMenu, pathname: Router.pathname })
    }

    // ====================================================================================
    // ========== ACTION LISTENERS ========================================================
    // ====================================================================================

    // ====================================================================================
    // ========== RENDER SECTIONS =========================================================
    // ====================================================================================
    render() {
        return(
            <>
            <HtmlHead title={this.props.pageName}/>
            <nav>
                <div className="topbar">
                    <div className="contact"><EMAIL> &nbsp;&nbsp;|&nbsp;&nbsp; <a target="_blank" href="https://wa.me/628112643036">+62 811 2643 036</a></div>
                    <div className="contact">
                        <a target="_blank" href="https://www.sodapos.com/">&#9400; sodapos.com</a><a target="_blank" href="https://esoda.id/">by esoda.id</a>
                    </div>
                </div>
                <div className="navbar">
                    <div className="btn-menu">
                        <i className="ph ph-bold ph-list"></i>
                    </div>
                    <div className="title">{this.props.pageName}</div>
                    <div className="logo">
                        <img alt="" src="/assets/images/img_logo_soda.png"/>
                        <span>Hubs</span>
                    </div>
                    <div className="menu">
                        <div className="app-name">
                            <div>{Constants.appName}</div>
                            <div className="btn-menu-close">
                                <i className="ph ph-bold ph-x"></i>
                            </div>
                        </div>
                        {this.renderMenu()}
                    </div>
                </div>
            </nav>
            {this.props.children}
            </>
        )
    }
    renderMenu() {
        let arrData = this.state.arrMenu
        if(arrData.length > 0) {
            return(
                <ul>
                    {arrData.map((item, index) => {
                        return(
                            <li key={index}
                                className={item.active && `active`}
                                >
                                <a href={item.link}>
                                    <i className={`ph ${item.icon} icon-menu`}></i>
                                    <span>{item.title}</span>
                                    {item.menu.length > 0 && 
                                        <i className="ph ph-bold ph-caret-down arrow"></i>
                                    }
                                </a>
                                {item.menu.length > 0 && this.renderMenuItem(item.menu)}
                            </li>
                        )
                    })}
                    <li className="profile">
                        <a href="#">
                            <img alt="John Doe" src="https://lenox-pasifik.co.id/wp-content/uploads/2016/06/team-1.jpg"/>
                            <div className="account">
                                <div>John Doe</div>
                                <div><EMAIL></div>
                            </div>
                        </a>
                    </li>
                </ul>
            )
        }
    }
    renderMenuItem(arrData) {
        return(
            <ul className="sub-menu">
                {arrData.map((item, index) => {
                    return(
                        <li key={index}
                            className={item.active && `active`}
                            >
                            <a href={item.link}>
                                <span>{item.title}</span>
                                {item.menu.length > 0 && 
                                    <i className="ph ph-bold ph-caret-right arrow"></i>
                                }
                            </a>
                            {item.menu.length > 0 && this.renderMenuItemSub(item.menu)}
                        </li>
                    )
                })}
            </ul>
        )
    }
    renderMenuItemSub(arrData) {
        return(
            <ul className="more-menu">
                {arrData.map((item, index) => {
                    return(
                        <li key={index}
                            className={item.active && `active`}
                            >
                            <a href={item.link}>{item.title}</a>
                        </li>
                    )
                })}
            </ul>
        )
    }
}