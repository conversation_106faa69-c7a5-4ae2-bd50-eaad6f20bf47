import React from "react";
import { Modal, Box, Alert } from "@mui/material";
import Loading from "@/components/modal/Loading";
import ApiHelper from "@/utils/ApiHelper";
import MySnackbar from "@/components/MySnackbar";
import Image from "next/image";
import InputAutoComplete2 from "@/components/libs/InputAutoComplete2";
import Constants from "@/utils/Constants";
import CommonHelper from "@/utils/CommonHelper";
import Table from "@/components/libs/Table";
import MomentHelper from "@/utils/MomentHelper";

export default class TransactionModalVerify extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showDialog: false,
      formType: "verify",
      formData: null,
      formIndex: -1,

      detail: null,
    };
  }

  onFetchDetail = async () => {
    this.ref_Loading.onShowDialog();
    let response = await <PERSON>piHelper.get("kiosk/admin/transaction/detail", {
      id: this.state.formData.id,
    });

    let detail = null;
    if (response.status === 200) {
      detail = response.results.data;
    } else {
      this.onNotify(response.message, "error");
      this.onCloseDialog();
    }
    this.ref_Loading.onCloseDialog();
    this.setState({ detail });
  };

  onNotify = (message, severity = "info") => {
    this.ref_MySnackbar.onNotify(message, severity);
  };

  onLoading = (show, timout = 300) => {
    if (show) {
      this.ref_Loading.onShowDialog();
      return;
    } else {
      setTimeout(() => {
        this.ref_Loading.onCloseDialog();
      }, timout);
    }
  };

  onShowDialog = (formType, formData = null, formIndex = -1) => {
    this.setState(
      {
        formType,
        formData,
        formIndex,
        showDialog: true,
      },
      () => {
        this.onFetchDetail();
      }
    );
  };

  onCloseDialog = () => {
    this.setState({
      showDialog: false,
      formType: "verify",
      formData: null,
      formIndex: -1,

      detail: null,
    });
  };

  onValidateListeners = () => {
    let isError = false;
    this.state.detail.product_array.forEach((product) => {
      if (
        product.serial_number_type === "UNIQUE" &&
        product.serial_number === ""
      ) {
        isError = true;
      }
    });
    if (isError) {
      this.onNotify("Silahkan isi nomor serial produk", "warning");
      return;
    }

    this.actOnProcessListeners();
  };

  getProductTableHeadings = () => {
    return [
      {
        key: "name",
        label: "Nama Produk",
        className: "",
        sortable: false,
        sort: "",
      },
      {
        key: "serial_number",
        label: "Nomor Serial",
        className: "wd80",
        sortable: false,
        sort: "",
      },
      {
        key: "code",
        label: "Kode",
        className: "wd120",
        sortable: false,
        sort: "",
      },
      {
        key: "category",
        label: "Kategori",
        className: "wd120",
        sortable: false,
        sort: "",
      },
      {
        key: "price",
        label: "Harga",
        className: "text-right wd120",
        sortable: false,
        sort: "",
      },
      {
        key: "quantity",
        label: "Jumlah",
        className: "text-center wd100",
        sortable: false,
        sort: "",
      },
      {
        key: "total",
        label: "Total",
        className: "text-right wd140",
        sortable: false,
        sort: "",
      },
    ];
  };

  renderProductTableItems = (product, index) => {
    const objData = this.state.detail;

    return (
      <tr key={index}>
        {/* Product Name with Serial Number Input */}
        <td style={{ position: "relative" }}>
          <div>
            <div
              style={{
                fontSize: "1rem",
                fontWeight: "600",
                color: "#2d3436",
                marginBottom: "0.25rem",
              }}
              className="flex flex-row gap-4 items-center"
            >
              <Image
                src={product.image_url || Constants.image_default.empty}
                alt={product.name}
                width={60}
                height={60}
                style={{ objectFit: "contain" }}
              />

              {product.name}
            </div>
          </div>
        </td>
        <td className="text-left">
          {/* Serial Number Input */}
          {product.type === "Persediaan" &&
          product.serial_number_type === "UNIQUE" ? (
            <div>
              <InputAutoComplete2
                label=""
                inputName={`serial_number_${product.id}`}
                formClass="full-width"
                dataUrl="kiosk/admin/product/accurate/serial/options"
                dataParams={{
                  aol_id: product.aol_id,
                  aol_session_database: product.aol_session_database,
                  product_id: product.id,
                  warehouse_accurate_id: product.warehouse_accurate_id,
                }}
                displayTitle="number"
                displayRenderCustom={(item, index) => {
                  return (
                    <>
                      <div className="title" style={{ marginBottom: "0px" }}>
                        {item.number}
                      </div>
                    </>
                  );
                }}
                required={true}
                onChange={(item) => {
                  if (item) {
                    const updatedProducts = [...objData.product_array];
                    updatedProducts[index] = {
                      ...product,
                      serial_number: item.number,
                    };
                    this.setState({
                      detail: {
                        ...objData,
                        product_array: updatedProducts,
                      },
                    });
                  }
                }}
                disablePagination
              />
            </div>
          ) : (
            <div>-</div>
          )}
        </td>

        {/* Product Code */}
        <td>{product.code}</td>

        {/* Category */}
        <td>{product.category_name}</td>

        {/* Price */}
        <td className="text-right">
          {Number(product.selling_price) < 0 ? "-" : ""}
          Rp{CommonHelper.formatNumberDecimal(Math.abs(product.selling_price))}
        </td>

        {/* Quantity */}
        <td className="text-center">
          {parseFloat(product.quantity)} {product.unit_name}
        </td>

        {/* Total */}
        <td className="text-right">
          <span
            style={{
              color: "var(--base-color)",
              fontWeight: "600",
            }}
          >
            {Number(product.total_selling_price) < 0 ? "-" : ""}
            Rp
            {CommonHelper.formatNumberDecimal(
              Math.abs(product.total_selling_price)
            )}
          </span>
        </td>
      </tr>
    );
  };

  actOnProcessListeners = async () => {
    this.ref_Loading.onShowDialog();

    let params = {
      id: this.state.detail.id,
      product_array: this.state.detail.product_array,
    };

    const response = await ApiHelper.post(
      "kiosk/admin/transaction/process/waiting",
      params
    );

    if (response.status === 200) {
      const { onResults } = this.props;
      if (onResults) {
        onResults(
          this.state.formType,
          this.state.formData,
          this.state.formIndex
        );
      }
      this.onCloseDialog();

      this.onNotify(response?.message || "Berhasil Verifikasi", "success");
    } else {
      this.onNotify(response?.message || "Terjadi Kesalahan", "error");
    }

    this.ref_Loading.onCloseDialog();
  };

  render() {
    return (
      <>
        <Modal open={this.state.showDialog} onClose={this.onCloseDialog}>
          <Box
            sx={{
              flex: 1,
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              display: "flex",
              width: "100wh",
              height: "100vh",
            }}
          >
            <div className="modal-content">
              <div className="modal-header">{this.renderHeader()}</div>
              <div className="modal-body" style={{ height: "fit-content" }}>
                {this.renderBody()}
              </div>
              <div className="modal-footer">{this.renderFooter()}</div>
            </div>
          </Box>
        </Modal>

        {/* Loading dialog */}
        <Loading ref={(value) => (this.ref_Loading = value)} />
        <MySnackbar ref={(value) => (this.ref_MySnackbar = value)} />
      </>
    );
  }

  renderHeader() {
    return (
      <>
        <div className="title">Proses Transaksi</div>
        <span className="close" onClick={() => this.onCloseDialog()}>
          &times;
        </span>
      </>
    );
  }

  renderBody() {
    return <>{this.renderTransactionDetail()}</>;
  }

  renderTransactionDetail() {
    let objData = this.state.detail;

    if (!objData) {
      return (
        <div className="flex-rows no-right">
          <div className="row no-border">
            <div className="input-form">
              <Alert severity="info">Memuat detail transaksi...</Alert>
            </div>
          </div>
        </div>
      );
    }

    return (
      <>
        <div className="flex-rows no-right p-4">
          {/* Transaction Information */}
          <div className="row wd100 no-border">
            <div className="box">
              <div className="title flex flex-row gap-4 flex-wrap">
                <div>Informasi Transaksi</div>
                <div className="content">
                  <span
                    className={`status ${
                      objData.status === "Menunggu Diproses"
                        ? "unavailable"
                        : ""
                    }`}
                  >
                    {objData.status}
                  </span>
                </div>
              </div>
              <div className="detail_wrapper_grid">
                <div className="detail_container_grid">
                  <em>Nomor Transaksi</em>
                  <div className="content text">{objData.number || "-"}</div>
                </div>
                <div className="detail_container_grid">
                  <em>Tanggal Input</em>
                  <div className="content text">
                    {MomentHelper.format(
                      objData.input_datetime,
                      MomentHelper.dateFormatReadable2
                    )}
                  </div>
                </div>
                <div className="detail_container_grid">
                  <em>Nama Pelanggan</em>
                  <div className="content text">
                    {objData.customer_name || "-"}
                  </div>
                </div>
                <div className="detail_container_grid">
                  <em>WhatsApp</em>
                  <div className="content text">
                    {objData.customer_whatsapp || "-"}
                  </div>
                </div>
                <div className="detail_container_grid">
                  <em>Affiliate</em>
                  <div className="content text">
                    {objData.affiliate_name || "-"}
                  </div>
                </div>
                <div className="detail_container_grid">
                  <em>Kode Affiliate</em>
                  <div className="content text">
                    {objData.affiliate_code || "-"}
                  </div>
                </div>
                <div className="detail_container_grid">
                  <em>Total Item</em>
                  <div className="content text">
                    {objData.total_product_item || "0"}
                  </div>
                </div>
                <div className="detail_container_grid">
                  <em>Grand Total</em>
                  <div className="content text">
                    {new Intl.NumberFormat("id-ID", {
                      style: "currency",
                      currency: "IDR",
                    }).format(objData.grand_total || 0)}
                  </div>
                </div>
              </div>
            </div>

            {/* Product Details */}
            {objData.product_array && objData.product_array.length > 0 && (
              <div className="flex-rows no-right mt-5">
                <div className="row no-border">
                  <div className="box">
                    <div className="title">Detail Produk</div>
                    <div className="input-form">
                      <Table
                        title=""
                        disabledHeader={true}
                        disabledPage={true}
                        dataHeadings={this.getProductTableHeadings()}
                        dataTables={objData.product_array}
                        renderItems={(product, index) =>
                          this.renderProductTableItems(product, index)
                        }
                        isFetched={false}
                        customTableContainers="product-details-table"
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </>
    );
  }
  renderFooter() {
    const { detail } = this.state;

    return (
      <div className="flex flex-row gap-4">
        {detail?.status === "Menunggu Diproses" && (
          <button className="button" onClick={() => this.onValidateListeners()}>
            <i className="ph ph-bold ph-check-circle"></i>
            <span>Proses</span>
          </button>
        )}
        <button
          className="button cancel ml-0"
          onClick={() => this.onCloseDialog()}
        >
          <i className="ph ph-bold ph-x-circle"></i>
          <span>Tutup</span>
        </button>
      </div>
    );
  }
}
