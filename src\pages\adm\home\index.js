/*
Created by esoda
Created on Apr, 2025
Contact esoda.id
*/

import React from "react";
import styles from "@/styles/Pos.module.css";
import Router from "next/router";
import PosComponentTopbar from "@/components/pages/adm/_topbar";
import Constants from "@/utils/Constants";
import AuthWrapperAdmin from "@/components/wrapper/AuthWrapperAdmin";
import { Skeleton } from "@mui/material";

class AdminMenu extends React.Component {
  // ====================================================================================
  // ========== INITIALIZE, GET DATA ====================================================
  // ====================================================================================

  // ====================================================================================
  // ========== ACTION LISTENERS ========================================================
  // ====================================================================================
  onGoToPageListeners = (urlname = "") => {        
    if (urlname) {
      Router.push({ pathname: `${urlname}` });
    }
  };

  // ====================================================================================
  // ========== RENDER SECTIONS =========================================================
  // ====================================================================================
  render() {
    return (
      <div
        className={`${styles.ct_containers}`}
        style={{ alignContent: "baseline" }}
      >
        <PosComponentTopbar
          title="Kiosk Admin"
          subtitle=""
          user={this.props.user}
          isAuthLoading={this.props.isAuthLoading}
        />
        <div className={styles.contents}>
          {this.renderMenuLoading()}
          {this.renderMenu()}

          {/* <div
            style={{
              textAlign: "center",
              marginTop: "2rem",
              color: "#2f3640",
              fontSize: "12px",
            }}
          >
            Version 1.0.2025
          </div>
          <div
            style={{
              textAlign: "center",
              color: "#2f3640",
              fontSize: "12px",
            }}
          >
            2025 © ELS
          </div> */}
        </div>
      </div>
    );
  }

  renderMenuLoading() {
    if (!this.props.isAuthLoading) {
      return null;
    }

    return (
      <div className={styles.menus}>
        {[...Array(8)].map((_, index) => (
          <div key={`skeleton-1-${index}`} className={styles.items}>
            <Skeleton variant="circular" width={50} height={50} />
            <Skeleton variant="text" width={100} height={24} sx={{ mt: 1 }} />
          </div>
        ))}
      </div>
    );
  }

  renderMenu() {
    if (this.props.isAuthLoading) {
      return null;
    }

    return (
      <>
        <div className={styles.menus}>
          {this.props.menu?.map((item, index) => (
            <div
              key={`menu-${index}`}
              style={{ ...item.jsx_style_object }}
              className={styles.items}
              onClick={() => this.onGoToPageListeners(item.link)}
            >
              {/* add next image with loading */}
              <img src={item.icon_url} />
              <span>{item.name}</span>
            </div>
          ))}
        </div>
      </>
    );
  }
}

export default AuthWrapperAdmin(AdminMenu, {
  redirectTo: Constants.webUrl.adm.login,
});
