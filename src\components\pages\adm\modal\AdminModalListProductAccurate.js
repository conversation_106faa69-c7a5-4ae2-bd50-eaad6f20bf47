/*
Created by esoda
Created on Mei, 2024
Contact esoda.id
*/

import React from "react";
import { Modal, Box, Checkbox, Chip, Radio } from "@mui/material";
import Loading from "@/components/modal/Loading";
import Table from "@/components/libs/Table";
import ApiHelper from "@/utils/ApiHelper";
import CommonHelper from "@/utils/CommonHelper";
import MySnackbar from "@/components/MySnackbar";
import InputAutoComplete from "@/components/libs/InputAutoComplete2";
import Input from "@/components/libs/Input";
import FilterHelper, {
  FILTER_TYPE_STRING,
  FILTER_COMP_EQUAL,
} from "@/utils/FilterHelper";

// formData = {
//   selected: [],
//   apiEndpoint: "",
//   apiParams: {
//   },
//   showDatabase: true,
//   propsInputs: {
//     category: {
//       apiParams: {
//       },
//     },
//     brand: {
//       apiParams: {
//       },
//     },
//   },
// };

export default class AdminModalListProductAccurate extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showDialog: false,
      formType: "add",
      formData: null,
      formIndex: -1,

      isMultiSelect: false,

      // Table data and selection state
      headers: [],
      dataTables: [],
      selectedItems: {},
      selectAll: false,
      isFetched: false,
      pageCount: 1,
      pageSelected: 1,
      pageRow: 10,
      inputSearch: "",
      inputSort: "",
      inputFilter: [],
      inputFilterObj: {},
      filterInput: {},
    };
  }

  // ====================================================================================
  // ========== INITIALIZE, GET DATA ====================================================
  // ====================================================================================
  onShowDialog = (formType, formData = null, formIndex = -1) => {
    let selectedItems = {};

    if (formData?.selected) {
      formData.selected.map((item) => {
        selectedItems[item.id] = item;
      });
    }

    if (formData?.showDatabase === undefined) {
      formData.showDatabase = true;
    }
    let isMultiSelect = false;
    if (formData?.isMultiSelect !== undefined) {
      isMultiSelect = formData.isMultiSelect;
    }

    this.setState(
      {
        formType,
        formData,
        formIndex,
        showDialog: true,
        selectedItems,
        selectAll: false,

        isMultiSelect,
      },
      () => {
        if (formType === "list") {
          this.getTableHeadings();
          this.onRefresh();
        }
      }
    );
  };

  onCloseDialog = () => {
    this.setState({
      showDialog: false,
      formType: "add",
      formData: null,
      formIndex: -1,
      selectedItems: {},
      selectAll: false,

      isMultiSelect: false,

      headers: [],
      dataTables: [],
      selectedItems: {},
      selectAll: false,
      isFetched: false,
      pageCount: 1,
      pageSelected: 1,
      pageRow: 10,
      inputSearch: "",
      inputSort: "",
      inputFilter: [],
      inputFilterObj: {},
      filterInput: {},
    });
  };

  getTableHeadings = () => {
    let headers = [];
    if (this.state.isMultiSelect) {
      headers.push({
        key: "checkbox",
        label: "",
        className: "text-center wd40 sticky_thead",
        sortable: false,
        sort: "",
      });
    } else {
      headers.push({
        key: "radio",
        label: "",
        className: "text-center wd40 sticky_thead",
        sortable: false,
        sort: "",
      });
    }

    headers = [
      ...headers,
      {
        key: "name",
        label: "Nama Produk",
        className: "",
        sortable: true,
        sort: "",
      },
      {
        key: "code",
        label: "Kode Produk",
        className: "",
        sortable: true,
        sort: "",
      },
      {
        key: "category_name",
        label: "Kode Kategori",
        className: "",
        sortable: true,
        sort: "",
      },
      {
        key: "category_title",
        label: "Nama Kategori",
        className: "",
        sortable: true,
        sort: "",
      },
      {
        key: "brand_name",
        label: "Brand",
        className: "",
        sortable: true,
        sort: "",
      },
      {
        key: "unit_name",
        label: "Satuan",
        className: "",
        sortable: true,
        sort: "",
      },
      {
        key: "type",
        label: "Tipe",
        className: "",
        sortable: true,
        sort: "",
      },
      {
        key: "unit_selling_price",
        label: "Harga Jual",
        className: "",
        sortable: true,
        sort: "",
      },
    ];

    if (this.state.formData?.showDatabase) {
      headers.push({
        key: "aol_session_name",
        label: "Db Accurate",
        className: "",
        sortable: true,
        sort: "",
      });
    }

    this.setState({ headers });
  };

  // ====================================================================================
  // ========== ACTION LISTENERS ========================================================
  // ====================================================================================
  // Table and selection handlers
  onSelectItem = (item) => {
    let selectedItems = structuredClone(this.state.selectedItems);
    let selectAll = false;
    if (this.state.isMultiSelect) {
      if (selectedItems[item.id]) {
        delete selectedItems[item.id];
      } else {
        selectedItems[item.id] = item;
      }

      // Check if all items on the current page are selected
      selectAll = this.state.dataTables.every((item) => selectedItems[item.id]);
    } else {
      selectedItems = {};
      selectedItems[item.id] = item;
    }

    this.setState({ selectedItems, selectAll }, () => {
      if (!this.state.isMultiSelect) {
        this.onSelectListeners();
      }
    });
  };

  onSelectAll = () => {
    if (this.state.isFetched) {
      return;
    }

    let selectedItems = structuredClone(this.state.selectedItems);
    let dataTables = structuredClone(this.state.dataTables);

    let selectAll = !this.state.selectAll;
    // map datatable
    dataTables.map((item) => {
      if (selectAll) {
        if (selectedItems[item.id] === undefined) {
          selectedItems[item.id] = item;
        }
      } else {
        if (selectedItems[item.id] !== undefined) {
          delete selectedItems[item.id];
        }
      }
    });

    this.setState({ selectedItems, selectAll });
  };

  // Table pagination handlers
  onPageChangeListeners = (page) => {
    this.setState({ pageSelected: page }, () => this.onFetchData(true));
  };

  onPageRowChangeListeners = (pageRow) => {
    this.setState({ pageRow, pageSelected: 1 }, () => this.onFetchData(true));
  };

  onSearchListeners = (inputSearch) => {
    this.ref_Table.setSearchValue(inputSearch);
    this.setState({ inputSearch }, this.onRefresh);
  };

  onSortListeners = (inputSort) => {
    this.setState({ inputSort }, () => this.onFetchData(true));
  };

  onFilterListeners = () => {
    this.ref_ModalFilter.onShowDialog(
      "filter",
      {
        inputs: this.state.filterInput,
        propsInputs: this.state.formData?.propsInputs ?? {},
      },
      -1
    );
  };

  onFilterChangeListeners = (filterInput) => {
    const refFilter = {
      category: {
        field: (filter, key) => {
          return "category_id";
        },
        fieldObj: (filter, key) => {
          if (!filter?.id) {
            return {};
          }
          return {
            [`category_id`]: filter.id,
          };
        },
        removeFieldObj: [`category`, `category_id`],
        value: (filter, key) => {
          if (!filter?.id) {
            return "";
          }
          return `${filter.id}`;
        },
        title: (filter, key) => {
          if (!filter?.title) {
            return ``;
          }
          return `Kategori: ${filter.title}`;
        },
        type: FILTER_TYPE_STRING,
        comparison: FILTER_COMP_EQUAL,
      },
      brand: {
        field: (filter, key) => {
          return "brand_id";
        },
        fieldObj: (filter, key) => {
          if (!filter?.id) {
            return {};
          }
          return {
            [`brand_id`]: filter.id,
          };
        },
        removeFieldObj: [`brand`, `brand_id`],
        value: (filter, key) => {
          if (!filter?.id) {
            return "";
          }
          return `${filter.id}`;
        },
        title: (filter, key) => {
          if (!filter?.name) {
            return ``;
          }
          return `Brand: ${filter.name}`;
        },
        type: FILTER_TYPE_STRING,
        comparison: FILTER_COMP_EQUAL,
      },
      tipe: {
        field: (filter, key) => {
          return "type";
        },
        fieldObj: (filter, key) => {
          if (!filter) {
            return {};
          }
          return {
            [`type`]: filter,
          };
        },
        removeFieldObj: [`tipe`, `type`],
        value: (filter, key) => {
          if (!filter) {
            return "";
          }
          return `${filter}`;
        },
        title: (filter, key) => {
          return `Tipe: ${filter}`;
        },
        type: FILTER_TYPE_STRING,
        comparison: FILTER_COMP_EQUAL,
      },
    };
    const filterHelper = new FilterHelper();
    const { inputFilter, inputFilterObj } = filterHelper.generateFilter(
      filterInput,
      refFilter
    );

    this.setState({ inputFilter, inputFilterObj, filterInput }, this.onRefresh);
  };

  onRemoveFilterListeners = (index) => {
    let filterInput = structuredClone(this.state.filterInput);
    let inputFilterObj = structuredClone(this.state.inputFilterObj);
    let inputFilter = structuredClone(this.state.inputFilter);

    inputFilter.forEach((filter, filterIdx) => {
      if (filterIdx === index) {
        for (const key in filter.removeFieldObj) {
          const element = filter.removeFieldObj[key];
          delete inputFilterObj[element];
          delete filterInput[element];
        }
        inputFilter.splice(filterIdx, 1);
      }
    });

    this.setState({ inputFilter, inputFilterObj, filterInput }, this.onRefresh);
  };

  onRefresh = () => {
    this.setState(
      {
        isFetched: true,
        dataTables: [],
        pageCount: 1,
        pageSelected: 1,
        pageRow: 10,
      },
      this.onFetchData
    );
  };

  onFetchData = async (isPageClicked = false) => {
    if (isPageClicked) {
      this.setState({ isFetched: true });
    }

    // api
    let apiEndpoint = "kiosk/admin/product/accurate/data";
    let params = {
      limit: this.state.pageRow,
      page: this.state.pageSelected,
      search: this.state.inputSearch,
      sort: this.state.inputSort,
      pagination_bool: true,
    };

    if (this.state.formData?.apiEndpoint) {
      apiEndpoint = this.state.formData.apiEndpoint;
    }
    if (this.state.formData?.apiParams) {
      params = { ...params, ...this.state.formData.apiParams };
    }

    if (this.state.inputFilterObj) {
      params = { ...params, ...this.state.inputFilterObj };
    }

    let response = await ApiHelper.get(apiEndpoint, params);
    if (response.status === 200) {
      let selectAll = true;
      let dataTables = response.results.data;
      for (let i = 0; i < dataTables.length; i++) {
        const element = dataTables[i];
        if (!this.state.selectedItems[element.id]) {
          selectAll = false;
          break;
        }
      }
      this.setState({
        isFetched: false,
        dataTables: response.results.data,
        pageCount: response.results.pagination.total_page,
        selectAll,
      });
    } else {
      this.setState({
        isFetched: false,
        dataTables: [],
        pageCount: 1,
        selectAll: false,
      });
    }
  };

  onSelectListeners = () => {
    // Get selected branches
    const selected = [];
    for (const key in this.state.selectedItems) {
      if (Object.hasOwnProperty.call(this.state.selectedItems, key)) {
        const element = this.state.selectedItems[key];
        selected.push(element);
      }
    }

    const { onSelect } = this.props;
    if (onSelect) {
      onSelect(selected, this.state.formData);
    }
    this.onCloseDialog();
  };

  // ====================================================================================
  // ========== RENDER SECTIONS =========================================================
  // ====================================================================================
  render() {
    let addModalClass = "medium";
    if (
      this.state.formType === "delete" ||
      this.state.formType === "activated"
    ) {
      addModalClass = "dialog";
    }
    return (
      <>
        <Modal open={this.state.showDialog} onClose={this.onCloseDialog}>
          <Box
            sx={{
              flex: 1,
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              display: "flex",
              width: "100wh",
              height: "100vh",
            }}
          >
            <div className={`modal-content ${addModalClass}`}>
              <div className="modal-header">{this.renderHeader()}</div>
              <div className="modal-body">{this.renderBody()}</div>
              <div className="modal-footer flex-wrap">
                {this.renderFooter()}
                <button
                  className="button cancel"
                  onClick={() => this.onCloseDialog()}
                >
                  <i className="ph ph-bold ph-x-circle"></i>
                  <span>
                    {this.state.formType === "info" ? "Tutup" : "Batal"}
                  </span>
                </button>
              </div>
            </div>
          </Box>
        </Modal>

        {/* Loading dialog */}
        <Loading ref={(value) => (this.ref_Loading = value)} />

        <ModalFilter
          ref={(value) => (this.ref_ModalFilter = value)}
          onFilter={(inputs) => this.onFilterChangeListeners(inputs)}
        />
      </>
    );
  }

  renderHeader() {
    return (
      <>
        <div className="title">
          {this.state.formType === "add" && `Tambah Data REPLACE_HERE`}
          {this.state.formType === "info" && `Detail Data REPLACE_HERE`}
          {this.state.formType === "edit" && `Ubah Data REPLACE_HERE`}
          {this.state.formType === "delete" && `Hapus Data REPLACE_HERE`}
          {this.state.formType === "activated" &&
            `Aktif/Non-Aktif Data REPLACE_HERE`}
          {this.state.formType === "list" && `Pilih Produk`}
        </div>
        <span className="close" onClick={() => this.onCloseDialog()}>
          &times;
        </span>
      </>
    );
  }

  renderBody() {
    return <>{this.state.formType === "list" && this.renderTableList()}</>;
  }

  renderTableList() {
    return (
      <div style={{ height: "100%" }}>
        <Table
          ref={(value) => (this.ref_Table = value)}
          title="Daftar Produk"
          subtitle="Pilih Produk yang akan digunakan"
          disabledAddBtn
          disabledBtnExport
          dataHeadings={this.state.headers}
          dataTables={this.state.dataTables}
          renderItems={(item, index) => this.renderTableItems(item, index)}
          pageCount={this.state.pageCount}
          pageSelected={this.state.pageSelected}
          pageListeners={this.onPageChangeListeners}
          pageRowListeners={this.onPageRowChangeListeners}
          onSearch={this.onSearchListeners}
          inputSearch={this.state.inputSearch}
          searchListeners={this.onSearchListeners}
          sortListeners={this.onSortListeners}
          onReload={() => this.onFetchData(true)}
          filterListeners={() => {
            this.onFilterListeners();
          }}
          inputFilter={this.state.inputFilter}
          onRemoveFilterItem={this.onRemoveFilterListeners}
          isFetched={this.state.isFetched}
        />
      </div>
    );
  }

  renderTableItems(item, index) {
    const isSelected = !!this.state.selectedItems[item.id];

    let sellingPriceMark = "";
    if (Number(item.unit_selling_price) < 0) {
      sellingPriceMark = "-";
    }

    return (
      <tr
        key={index}
        style={{ opacity: !item.suspended_bool ? 1 : 0.4, cursor: "pointer" }}
        onClick={() => this.onSelectItem(item)}
      >
        {this.state.isMultiSelect && (
          <td className="text-center">
            <Checkbox checked={isSelected} color="primary" size="small" />
          </td>
        )}
        {!this.state.isMultiSelect && (
          <td className="text-center">
            <Radio
              checked={isSelected}
              color="primary"
              size="small"
              onChange={() => this.onSelectItem(item)}
            />
          </td>
        )}
        <td>{item.name || "-"}</td>
        <td>{item.code || "-"}</td>
        <td>{item.category_name || "-"}</td>
        <td>{item.category_title || "-"}</td>
        <td>{item.brand_name || "-"}</td>
        <td>{item.unit_name || "-"}</td>
        <td>{item.type || "-"}</td>
        <td>{`${sellingPriceMark}Rp${CommonHelper.formatNumberDecimal(
          Math.abs(item.unit_selling_price)
        )}`}</td>
        {this.state.formData?.showDatabase && (
          <td>{item.aol_session_name || "-"}</td>
        )}
      </tr>
    );
  }

  renderForm() {
    // This is a placeholder for the form implementation
    // The actual form implementation would go here when needed
    return null;
  }

  renderFooter() {
    const selectedCount = Object.keys(this.state.selectedItems).length;

    return (
      <>
        {this.state.formType !== "list" && (
          <button
            className={`button ${this.state.formType === "edit" && "warning"} ${
              this.state.formType === "delete" && "danger"
            }`}
            onClick={() => {
              this.onSubmitFilter();
            }}
          >
            {this.state.formType === "add" && (
              <i className="ph ph-bold ph-check-circle"></i>
            )}
            {this.state.formType === "edit" && (
              <i className="ph ph-bold ph-pencil"></i>
            )}
            {this.state.formType === "delete" && (
              <i className="ph ph-bold ph-trash-simple"></i>
            )}
            {this.state.formType === "activated" && (
              <i className="ph ph-bold ph-check-circle"></i>
            )}
            <span>
              {this.state.formType === "add" && "Tambah Data"}
              {this.state.formType === "edit" && "Ubah Data"}
              {this.state.formType === "delete" && "Hapus Data"}
              {this.state.formType === "activated" && "Aktif/Non-aktif"}
            </span>
          </button>
        )}

        {this.state.formType === "list" && (
          <>
            {this.state.isMultiSelect && (
              <>
                <div
                  style={{
                    marginRight: "auto",
                    fontSize: "0.9rem",
                    color: "#666",
                  }}
                  className="hidden sm:hidden md:flex"
                >
                  {selectedCount > 0
                    ? `${selectedCount} data terpilih`
                    : "Belum data yang dipilih"}
                </div>
                <div
                  style={{
                    marginRight: "auto",
                    fontSize: "0.9rem",
                    color: "#666",
                  }}
                  className="flex sm:flex md:hidden gap-2 justify-center items-center"
                >
                  <i className="ph ph-bold ph-check-square"></i>
                  <span>{selectedCount}</span>
                </div>
                <button
                  className={`button responsive ${
                    this.state.isFetched ? "disabled_opacity" : ""
                  }`}
                  style={{ marginLeft: "0.5rem" }}
                  onClick={this.onSelectAll}
                >
                  <i
                    className={`ph ph-bold ${
                      this.state.selectAll ? "ph-checks" : "ph-check-square"
                    }`}
                  ></i>
                  <span>
                    {this.state.selectAll ? "Batalkan Semua" : "Pilih Semua"}
                  </span>
                </button>
                <button
                  className="button responsive"
                  disabled={selectedCount === 0}
                  onClick={() => {
                    this.onSelectListeners();
                  }}
                >
                  <i className="ph ph-bold ph-check-circle"></i>
                  <span>Pilih</span>
                </button>
              </>
            )}
          </>
        )}
      </>
    );
  }
}

const DEFAULT_INPUTS_FILTER = {
  category_name: "",
  category: null,
  brand: null,
  tipe: "",
};

class ModalFilter extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showDialog: false,
      formType: "filter",
      formData: null,
      formIndex: -1,

      inputs: structuredClone(DEFAULT_INPUTS_FILTER),
      errors: {},
    };
  }

  // ====================================================================================
  // ========== INITIALIZE, GET DATA ====================================================
  // ====================================================================================
  onShowDialog = (formType, formData = null, formIndex = -1) => {
    let inputs = structuredClone(DEFAULT_INPUTS_FILTER);
    if (formData?.inputs) {
      inputs = { ...structuredClone(inputs), ...formData.inputs };
    }

    this.setState({
      showDialog: true,
      formType,
      formData,
      formIndex,
      inputs,
    });
  };
  onCloseDialog = () => {
    this.setState({
      showDialog: false,
      formType: "filter",
      formData: null,
      formIndex: -1,

      inputs: structuredClone(DEFAULT_INPUTS_FILTER),
      errors: {},
    });
  };

  // ====================================================================================
  // ========== ACTION LISTENERS ========================================================
  // ====================================================================================
  onNotify = (message, severity) => {
    if (this.ref_MySnackbar) {
      this.ref_MySnackbar.onNotify(message, severity);
    }
  };
  onTextInputListeners = (text, input) => {
    let inputs = this.state.inputs;
    inputs[input] = text;
    this.setState((prevState) => ({ ...prevState, inputs }));
  };
  onTextErrorListeners = (error, input) => {
    let errors = this.state.errors;
    errors[input] = error;
    this.setState((prevState) => ({ ...prevState, errors }));
  };
  onSubmitFilter = () => {
    const { onFilter } = this.props;
    if (onFilter) {
      onFilter(this.state.inputs);
    }
    this.onCloseDialog();
  };
  actOnSaveListeners = () => {
    const { onResults } = this.props;
    if (onResults) {
      onResults(this.state.inputs);
    }
    this.onCloseDialog();
  };

  // ====================================================================================
  // ========== RENDER ==================================================================
  // ====================================================================================
  render() {
    let addModalClass = "dialog";

    return (
      <>
        <Modal open={this.state.showDialog} onClose={this.onCloseDialog}>
          <Box
            sx={{
              flex: 1,
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              display: "flex",
              width: "100wh",
              height: "100vh",
            }}
          >
            <div className={`modal-content ${addModalClass}`}>
              <div className="modal-header">{this.renderHeader()}</div>
              <div className="modal-body">{this.renderBody()}</div>
              <div className="modal-footer flex-wrap">
                {this.renderFooter()}
                <button
                  className="button cancel"
                  onClick={() => this.onCloseDialog()}
                >
                  <i className="ph ph-bold ph-x-circle"></i>
                  <span>
                    {this.state.formType === "info" ? "Tutup" : "Batal"}
                  </span>
                </button>
              </div>
            </div>
          </Box>
        </Modal>

        {/* Loading dialog */}
        <Loading ref={(value) => (this.ref_Loading = value)} />
        <MySnackbar ref={(value) => (this.ref_MySnackbar = value)} />
      </>
    );
  }
  renderHeader() {
    return (
      <>
        <div className="title">Filter Data</div>
        <span className="close" onClick={() => this.onCloseDialog()}>
          &times;
        </span>
      </>
    );
  }

  renderBody() {
    return <>{this.renderForm()}</>;
  }

  renderForm() {
    return (
      <>
        <div className="flex-rows no-right">
          <div className="row no-border">
            <div className="input-form">
              <InputAutoComplete
                ref={(value) => (this.ref_InputCategory = value)}
                label="Kategori"
                placeholder="Tuliskan kode atau nama kategori"
                inputName="modal-list-product-accurate-input-category"
                dataUrl="kiosk/admin/product/category/options"
                dataParams={
                  this.state.formData?.propsInputs?.category?.apiParams ?? {}
                }
                displayTitle="name"
                onChange={(item, type) => {
                  this.onTextInputListeners(item, "category");
                }}
                error={
                  this.state.errors.category !== undefined &&
                  this.state.errors.category !== null
                    ? true
                    : false
                }
                onFocus={() => this.onTextErrorListeners(null, "category")}
                displayRenderCustom={(item, index) => {
                  return (
                    <>
                      <div className="title">
                        {item.title && `${item.title} | `}
                        {item.name}
                      </div>
                      <div className="subtitle"></div>
                    </>
                  );
                }}
              />
            </div>
            <div className="input-form">
              <InputAutoComplete
                ref={(value) => (this.ref_InputBrand = value)}
                label="Brand"
                placeholder="Tuliskan nama brand"
                inputName="modal-list-product-accurate-input-brand"
                dataUrl="kiosk/admin/product/brand/options"
                dataParams={
                  this.state.formData?.propsInputs?.brand?.apiParams ?? {}
                }
                displayTitle="name"
                onChange={(item, type) => {
                  this.onTextInputListeners(item, "brand");
                }}
                error={
                  this.state.errors.brand !== undefined &&
                  this.state.errors.brand !== null
                    ? true
                    : false
                }
                onFocus={() => this.onTextErrorListeners(null, "brand")}
              />
            </div>
            <div className="input-form">
              <Input
                label="Tipe"
                inputType="select"
                value={this.state.inputs.tipe}
                // defaultValue={this.state.inputs.tipe}
                onChange={(event) => {
                  if (
                    event.target !== undefined &&
                    event.target.value !== undefined
                  ) {
                    this.onTextInputListeners(event.target.value, "tipe");
                  }
                }}
                error={
                  this.state.errors.tipe !== undefined &&
                  this.state.errors.tipe !== null
                    ? true
                    : false
                }
                onFocus={() => this.onTextErrorListeners(null, "tipe")}
                options={[
                  {
                    value: "",
                    label: "Pilih Tipe",
                  },
                  { value: "Persediaan", label: "Persediaan" },
                  { value: "Non Persediaan", label: "Non Persediaan" },
                  { value: "Jasa", label: "Jasa" },
                ]}
              />
            </div>
          </div>
        </div>
      </>
    );
  }

  renderFooter() {
    return (
      <>
        <button
          className="button"
          onClick={() => {
            this.onSubmitFilter();
          }}
        >
          <i className="ph ph-bold ph-check-circle"></i>
          <span>Filter</span>
        </button>
      </>
    );
  }
}
