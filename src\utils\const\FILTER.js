// "type":"string",
// "field":"is_active_bool",
// "value": "",
// "comparison":"=",
// "title": ""
export const FILTER_VIEW_DATA = {
  type: "",
  field: "",
  value: "",
  comparison: "",
  title: "",
  removeFieldObj: [],
  filterParams: null,
};
export const FILTER_DATA = {
  name: "", // Label Value
  value: "",
  label: "", // Label
  field: "", //"db_field"
  fieldType: "", // FILTER_TYPE_STRING,
  comparison: "", // FILTER_COMP_EQUAL,
  exception: false,
  exceptionValue: "",
};

export const FILTER_TYPE_STRING = "string";
export const FILTER_TYPE_DATE = "date";
export const FILTER_COMP_EQUAL = "=";
export const FILTER_COMP_BETWEEN = "bet";
