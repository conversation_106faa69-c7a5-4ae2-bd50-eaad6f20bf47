/*
Created by esoda
Created on Feb, 2025
Contact esoda.id
*/

import React from "react";
import HtmlHead from "./HtmlHead";
import Router from "next/router";
import styles from "@/styles/Public.module.css"

const ARR_MENU = [
    // {
    //     "title": "Beranda",
    //     "icon": "ph-house",
    //     "link": "/public",
    //     "active": true
    // },
    {
        "title": "Promo & Redeem",
        "icon": "ph-seal-percent",
        "link": "/public/promotions",
        "active": true
    },
    {
        "title": "Riwayat Belanja",
        "icon": "ph-shopping-bag",
        "link": "/public/history",
        "active": false
    },
    {
        "title": "Profile Saya",
        "icon": "ph-user",
        "link": "/public/profile",
        "active": false
    },
]

export default class PublicLayout extends React.Component {
    constructor(props) {
        super(props)
        this.state = {
            arrMenu: ARR_MENU,
            pathname: '',
        }

        this._FirstMount = true
    }
    componentDidMount() {
        if (Router !== undefined && Router.pathname !== undefined) {
            if (this._FirstMount) {
                this._FirstMount = false
                this.setActiveMenuListener()
            }
        }
    }

    // ====================================================================================
    // ========== INITIALIZE, GET DATA ====================================================
    // ====================================================================================
    setActiveMenuListener = () => {
        let arrMenu = this.state.arrMenu
        arrMenu.map(item => {
            item.active = false
            if (item.link === Router.pathname) {
                item.active = true
            }
        })
        this.setState({ arrMenu, pathname: Router.pathname }, () => this.onScrollTabListeners(Router.pathname))
    }

    // ====================================================================================
    // ========== ACTION LISTENERS ========================================================
    // ====================================================================================
    onScrollTabListeners = (menuRef) => {
        let x = document.getElementById(menuRef);
        x.scrollIntoView({ behavior: "smooth", inline: "center" });
    }

    // ====================================================================================
    // ========== RENDER SECTIONS =========================================================
    // ====================================================================================
    render() {
        return (
            <>
                <HtmlHead title={this.props.pageName} />
                <div className={styles.public}>
                    <div className={styles.topbar}>
                        <div className={styles.contact}><EMAIL> &nbsp;&nbsp;|&nbsp;&nbsp; <a target="_blank" href="https://wa.me/6282123283030">0821-2328-3030</a></div>
                        <div className={styles.contact}>
                            <a target="_blank" href="https://esoda.id/">&#9400; esoda.id</a>
                        </div>
                    </div>
                    <div className={styles.navbar}>
                        <div className={styles.menu}>
                            {this.renderMenu()}
                        </div>
                    </div>
                    {this.props.children}
                </div>
            </>
        )
    }
    renderMenu() {
        let arrData = this.state.arrMenu
        if (arrData.length > 0) {
            return (
                <ul>
                    {arrData.map((item, index) => {
                        let menuRef = item.link
                        this[menuRef] = React.createRef()
                        return (
                            <li id={menuRef} key={index} className={item.active && styles.active}>
                                <a href={item.link}>
                                    <i className={`ph ${item.icon}`}></i>
                                    <span>{item.title}</span>
                                </a>
                            </li>
                        )
                    })}
                </ul>
            )
        }
    }
}