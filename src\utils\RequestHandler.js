import Constants from "./Constants"

const getBaseUrlAPI = (mode) => {
    if (mode === 'dev') {
        return Constants.Api.apiUrlDev
    } else {
        return Constants.Api.apiUrlProd
    }
}

const getHeader = async () => {
    return {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: Constants.Api.basicAuth
    }
}

const getHandlerGlobal = async (url, headers) => {
    return new Promise((resolve) => {
        let uri = url;
        fetch(uri, {
            method: 'GET',
            headers: {
                ...headers,
            },
        })
            .then((response) => { return response.json() })
            .then((responseData) => {
                if (responseData.status === 404) {
                    return resolve({
                        status: 404,
                        message: 'Page Not Found.',
                    });
                } else {
                    return resolve(responseData);
                }
            })
            .catch((error) => {
                return resolve({
                    status: 500,
                    message: '<PERSON><PERSON><PERSON><PERSON> Server.',
                    error
                });
            });
    });
}

const getHandler = async (mode, session, url) => {
    let Header = await getHeader();
    return new Promise((resolve) => {
        let uri = getBaseUrlAPI(mode) + url;
        fetch(uri, {
            method: 'GET',
            headers: {
                ...Header,
                token: session == null ? '' : session.token
            },
        })
            .then( async (response) => { 
                // const response2 = response.clone();

                // console.log(url + "\n");
                // console.log(await response2.text());
                // console.log(url + "\n");
                return response.json() })
            .then((responseData) => {
                // console.log(responseData);
                if (responseData.status === 404) {
                    return resolve({
                        status: 404,
                        message: 'Page Not Found.',
                    });
                } else {
                    return resolve(responseData);
                }
            })
            .catch((error) => {
                // console.log(error);
                return resolve({
                    status: 500,
                    message: 'Terjadi Kesalahan Server.',
                    error
                });
            });
    });
}

const postHandler = async (mode, session, url, params) => {
    let Header = await getHeader();
    return new Promise((resolve) => {
        fetch(getBaseUrlAPI(mode) + url, {
            method: 'POST',
            headers: {
                ...Header,
                token: session == null ? '' : session.token
            },
            body: JSON.stringify(params),
        })
            .then( async (response) => {
                
                // const response2 = response.clone();

                // console.log({
                //     ...Header,
                //     token: session == null ? '' : session.token
                // });
                // console.log(url + "\n");
                // console.log(await response2.text());
                return response.json()
            })
            .then((responseData) => {
                if (responseData.status === 404) {
                    return resolve({
                        status: 404,
                        message: 'Page Not Found.',
                    });
                } else {
                    return resolve(responseData);
                }
            })
            .catch((error) => {
                return resolve({
                    status: 500,
                    message: 'Terjadi Kesalahan Server.',
                    error
                });
            });
    });
}

export { getHandler, postHandler, getHandlerGlobal }