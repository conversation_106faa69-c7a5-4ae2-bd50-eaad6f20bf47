/*
Created by esoda
Created on Feb, 2025
Contact esoda.id
*/

import React, { useRef, useEffect, useState } from "react";
import Tooltip from "@mui/material/Tooltip";
import IconButton from "@mui/material/IconButton";
import CommonHelper from "@/utils/CommonHelper";
import Constants from "@/utils/Constants";
import styles from "@/styles/Kiosk.module.css";
import TimePicker from "@/components/TimePicker";
import TransactionSalesorderCashierPayment from "@/components/pages/kiosk/pos/payment/cashierPayment";
import Loading from "@/components/modal/Loading";
import Router from "next/router";
import useTrackedPaymentStore, {
  usePaymentStore,
} from "@/store/kiosk/pos/payment/store";
import useTrackedPersistKioskPosStore, {
  usePersistKioskPosStore,
} from "@/store/kiosk/pos/storePersist";
import AuthWrapper from "@/components/wrapper/AuthWrapper";
import { Alert, Avatar, Chip, Skeleton } from "@mui/material";
import Input, { numberFormatIdToNumber } from "@/components/libs/Input";
import ModalVoucher from "@/components/pages/kiosk/pos/payment/ModalVoucher";
import ModalAffiliate from "@/components/pages/kiosk/pos/payment/ModalAffiliate";
import MySnackbar from "@/components/MySnackbar";
import ModalConfirmation from "@/components/modal/ModalConfirmation";
import { getUntrackedObject } from "react-tracked";

const KioskPaymentPage = ({
  isAuthLoading,
  isLoggedIn,
  user,
  selected_aol,
  selected_branch,
}) => {
  const { onSelectVoucher, onChangeAffiliate, isProductUseSerial } =
    useTrackedPaymentStore();

  // Refs
  const ref_Loading = useRef(null);
  const ref_MySnackbar = useRef(null);
  const ref_ModalConfirmation = useRef(null);
  const ref_paymentForm = useRef(null);
  const ref_ModalVoucher = useRef(null);
  const ref_ModalAffiliate = useRef(null);

  // Set refs in store when component mounts
  useEffect(() => {
    usePaymentStore.getState().setState({
      ref_Loading: ref_Loading,
      ref_MySnackbar: ref_MySnackbar,
      ref_ModalConfirmation: ref_ModalConfirmation,
      ref_paymentForm: ref_paymentForm,
      ref_ModalVoucher: ref_ModalVoucher,
      ref_ModalAffiliate: ref_ModalAffiliate,
    });
  }, []);

  useEffect(() => {
    // loading
    usePaymentStore.getState().onLoading(true);
    if (!isAuthLoading && isLoggedIn) {
      usePersistKioskPosStore.getState().setState({
        isInPayment: true,
      });
      usePaymentStore.getState().setState(
        {
          user,
          selected_aol,
          selected_branch,
        },
        () => {
          usePaymentStore.getState().onInit();
        }
      );
    }
  }, [isAuthLoading, isLoggedIn]);

  // ====================================================================================
  // ========== RENDER FUNCTIONS ========================================================
  // ====================================================================================

  return (
    <>
      <div className={styles.kiosk_payment}>
        <div className={styles.catalogs}>
          <div className={styles.topbar}>
            <div className={styles.store}>
              <a
                onClick={() => {
                  Router.replace({
                    pathname: "/pos",
                    query: { from: "payment" },
                  });
                }}
              >
                <i className="ph ph-arrow-left"></i>
              </a>
              <div className={styles.info}>
                <div className={styles.title}>PEMESANAN</div>
                <div className={styles.subtitle}>
                  Lengkapi informasi pemesanan Anda
                </div>
              </div>
              <div className={styles.date}>
                <div className={styles.merchant}>{selected_branch?.name}</div>
                <TimePicker />
              </div>
            </div>
          </div>
          <RenderCart />
          <RenderCartSummary />
        </div>
        <div className={styles.payments}>
          <RenderCustomerDetail />
          {/* <RenderPaymentMethods /> */}
          <RenderPaymentSummary />
          <RenderPaymentActions />
        </div>
      </div>

      {/* Loading dialog */}
      <Loading ref={ref_Loading} />
      <MySnackbar ref={ref_MySnackbar} />
      <ModalConfirmation ref={ref_ModalConfirmation} />

      {/* Payment dialog */}
      <TransactionSalesorderCashierPayment ref={ref_paymentForm} />
      <ModalVoucher ref={ref_ModalVoucher} onSelect={onSelectVoucher} />
      <ModalAffiliate ref={ref_ModalAffiliate} onResults={onChangeAffiliate} />
    </>
  );
};

const RenderCart = () => {
  const {
    onRemoveCartItem,
    onUpdateCartItemQty,
    onUpdateCartItemNote,
    onUpdateCartItemDiscount,
  } = useTrackedPersistKioskPosStore();
  const arrCart = usePersistKioskPosStore((state) => state.arrCart);

  return (
    <div className={styles.carts}>
      <table>
        <thead>
          <tr>
            <th>Produk ({arrCart.length} items)</th>
            <th className="text-center">Qty</th>
            <th className="text-right">Harga</th>
            <th className="text-right">Diskon</th>
            <th className="text-right">Total</th>
          </tr>
        </thead>
        {arrCart.length > 0 && (
          <tbody>
            {arrCart.map((item, index) => (
              <RenderCartItem
                key={index}
                id={item.id}
                // item={item}
                index={index}
                onRemoveCartItem={onRemoveCartItem}
                onUpdateCartItemQty={onUpdateCartItemQty}
                onUpdateCartItemNote={onUpdateCartItemNote}
                onUpdateCartItemDiscount={onUpdateCartItemDiscount}
              />
            ))}
            <RenderCartVoucher />
          </tbody>
        )}
      </table>
    </div>
  );
};

const RenderCartItem = ({
  // item,
  id,
  index,
  onRemoveCartItem,
  onUpdateCartItemQty,
  onUpdateCartItemNote,
  onUpdateCartItemDiscount,
}) => {
  const user = usePaymentStore((state) => state.user);
  const item = usePersistKioskPosStore((state) => state.arrCart[index]);

  let isStockEmpty = item.stock_balance <= 0;

  return (
    <tr key={id} style={{ opacity: isStockEmpty ? 0.5 : 1 }}>
      <td width="100%">
        <div className={styles.rows}>
          <Tooltip title="Hapus Item Ini">
            <IconButton
              onClick={() => onRemoveCartItem(index)}
              style={{ marginLeft: -10 }}
            >
              <i
                className="ph ph-bold ph-x-circle"
                style={{ color: "#e74c3c" }}
              ></i>
            </IconButton>
          </Tooltip>
          <img
            alt={item.name}
            src={item.image_url || Constants.image_default.empty}
          />
          <div>
            {item.name}
            <span>
              <i className="ph ph-pencil-simple-line"></i>
              <input
                placeholder="Tambahkan catatan disini, jika ada ........."
                value={item.notes}
                onChange={(e) => onUpdateCartItemNote(index, e.target.value)}
              />
            </span>
          </div>

          {isStockEmpty && (
            <Chip
              label="Stok Habis"
              color="warning"
              size="small"
              sx={{
                flexGrow: "unset !important",
                paddingLeft: "0px !important",
                width: "fit-content !important",
              }}
            />
          )}
        </div>
      </td>
      <td className="text-center">
        <div className={styles.qty_price}>
          <button
            className="button danger"
            onClick={() => onUpdateCartItemQty(index, item.qty - 1)}
          >
            <i className="ph ph-bold ph-minus"></i>
          </button>
          <input
            value={item.qty}
            onChange={(e) =>
              onUpdateCartItemQty(index, Number(e.target.value) || 0)
            }
          />
          <button
            className="button success"
            onClick={() => onUpdateCartItemQty(index, item.qty + 1)}
          >
            <i className="ph ph-bold ph-plus"></i>
          </button>
        </div>
      </td>
      <td className="text-right">
        {CommonHelper.formatNumber(item.selling_price, "idr")}
      </td>
      <td className="text-right">
        {Number(user?.employee_id) <= 0 && (
          <div className="text-green">Rp0</div>
        )}
        {Number(user?.employee_id) > 0 && (
          <Input
            className="text-right"
            inputType="number"
            value={item.discount_label}
            onChange={(event) => {
              if (
                event.target !== undefined &&
                event.target.value !== undefined
              ) {
                onUpdateCartItemDiscount(index, event.target.value);
              }
            }}
            startAdornment={<div className="px-2">Rp</div>}
            min={0}
            disablePlaceholder
          />
        )}
      </td>
      <td className="text-right">
        {CommonHelper.formatNumber(item.sub_total, "idr")}
      </td>
    </tr>
  );
};

const RenderCartVoucher = () => {
  const { inputs } = useTrackedPaymentStore();

  if (!inputs.selectedVoucher?.id) {
    return null;
  }

  let markSellingPrice = "";
  let sellingPrice = 0;
  if (inputs.selectedVoucher?.point_redeem_type === "Potongan Harga") {
    sellingPrice = Math.abs(inputs.selectedVoucher?.product_selling_price);
    if (Number(inputs.selectedVoucher?.product_selling_price) < 0) {
      markSellingPrice = "-";
    }
  }

  return (
    <tr key={inputs.selectedVoucher?.id} style={{}}>
      <td width="100%">
        <div className={styles.rows}>
          <div
            style={{
              width: 24,
              height: 32,
              flexGrow: "unset",
              paddingLeft: "0px",
            }}
          ></div>
          <img
            alt={inputs.selectedVoucher.product_name}
            src={
              inputs.selectedVoucher.image_url || Constants.image_default.empty
            }
          />
          <div>{inputs.selectedVoucher.product_name}</div>
        </div>
      </td>
      <td className="text-right">
        <div className={`${styles.qty_price} justify-end`}>1</div>
      </td>
      <td className="text-right">
        {markSellingPrice}
        {CommonHelper.formatNumber(sellingPrice, "idr")}
      </td>
      <td className="text-right">
        <div className="text-green">Rp0</div>
      </td>
      <td className="text-right">
        {markSellingPrice}
        {CommonHelper.formatNumber(sellingPrice, "idr")}
      </td>
    </tr>
  );
};

const RenderCartSummary = () => {
  const { arrCart } = useTrackedPersistKioskPosStore();
  const { inputs, onChangeTotalDiscount, user, onShowModalVoucher } =
    useTrackedPaymentStore();

  if (arrCart.length === 0) {
    return null;
  }

  let voucherImageUrl = Constants.image_default.empty;
  if (inputs.selectedVoucher?.image_url) {
    voucherImageUrl = inputs.selectedVoucher.image_url;
  }

  return (
    <div className={styles.summaries}>
      <div className={styles.left}>
        {/* Voucher */}
        <div className={styles.item}>
          <div>
            {inputs.selectedVoucher?.id ? (
              <div className="flex items-center">
                <Avatar
                  variant="rounded"
                  src={voucherImageUrl}
                  className="mr-2"
                />
                <span>
                  {inputs.selectedVoucher.name} ({inputs.selectedVoucher.code})
                </span>
              </div>
            ) : (
              "Tanpa Voucher"
            )}
          </div>
          <div>
            <button className="button info" onClick={onShowModalVoucher}>
              {inputs.selectedVoucher?.id ? "Ubah" : "Pilih"} Voucher
            </button>
          </div>
        </div>

        {inputs.selectedVoucher?.id && (
          <>
            <div className={styles.item}>
              <div>Kode Verifikasi Voucher</div>
              <div>
                <Input
                  className="text-right"
                  inputType="text"
                  value={inputs.voucher_verif_code}
                  onChange={(event) => {
                    if (
                      event.target !== undefined &&
                      event.target.value !== undefined
                    ) {
                      usePaymentStore
                        .getState()
                        .onTextInputListeners(
                          event.target.value,
                          "voucher_verif_code"
                        );
                    }
                  }}
                  required
                  maxLength={6}
                />
              </div>
            </div>
          </>
        )}

        <div className={styles.item}>
          <div>Subtotal</div>
          <div>{CommonHelper.formatNumber(inputs.sub_total, "idr")}</div>
        </div>

        {Number(user?.employee_id) > 0 && (
          <div className={styles.item}>
            <div className="text-green">Total Diskon</div>
            {/* <div className="text-green">(Rp0)</div> */}
            <div>
              <Input
                className="text-right"
                inputType="number"
                value={inputs.nominal_discount_label}
                onChange={(event) => {
                  if (
                    event.target !== undefined &&
                    event.target.value !== undefined
                  ) {
                    onChangeTotalDiscount(event.target.value);
                    // onUpdateCartItemDiscount(index, event.target.value);
                  }
                }}
                startAdornment={<div className="px-2">Rp</div>}
                min={0}
                disablePlaceholder
              />
            </div>
          </div>
        )}
        {/* <div className={styles.item}>
          <div className="text-orange">Pajak (11%)</div>
          <div className="text-orange">Rp2.750.000</div>
        </div> */}
        {/* <div className={styles.item}>
          <div className="">Pembulatan</div>
          <div className="">Rp0</div>
        </div> */}
      </div>
      <div className={styles.right}>
        <div style={{ fontSize: "20px", fontWeight: "600" }}>
          Total Transaksi
        </div>
        <div style={{ fontSize: "28px", fontWeight: "700", marginTop: -10 }}>
          {CommonHelper.formatNumber(inputs.nominal_transaction, "idr")}
        </div>
      </div>
    </div>
  );
};

const RenderCustomerDetail = () => {
  const { objCustomer } = useTrackedPersistKioskPosStore();

  let imageUrl = objCustomer?.image_url;

  return (
    <div className={styles.customers}>
      <div className={styles.detail}>
        {/* <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQGXSeTomhrdIan6G5XRR_rk8zJ12BjlmXAG__1AnicoI7PC5HtZe9s26QERd2FqObM6sw&usqp=CAU" /> */}
        <Avatar src={imageUrl}>{objCustomer?.name?.charAt(0)}</Avatar>
        <div className={styles.info}>
          <div className={styles.name}>{objCustomer?.name}</div>
          {objCustomer?.membership_badge && (
            <div className={styles.badge}>{objCustomer?.membership_badge}</div>
          )}
        </div>
        <div className={styles.poin}>
          <div className={styles.name}>Poin Tersedia</div>
          <div className={styles.badge}>
            {CommonHelper.formatNumber(objCustomer?.point)}
          </div>
        </div>
      </div>
    </div>
  );
};

const RenderPaymentMethods = () => {
  const {
    arrPaymentMethods,
    isGetPaymentMethods,
    inputs,
    onSelectPaymentMethod,
    isProductUseSerial,
  } = useTrackedPaymentStore();

  // if (isProductUseSerial) {
  //   return null;
  // }

  if (isGetPaymentMethods) {
    return (
      <div className={styles.methods}>
        <div className={styles.items}>
          {Array.from({ length: 10 }).map((item, index) => (
            <div
              key={index}
              className={styles.item}
              style={{ padding: "0px 0px 0px 0px" }}
            >
              <div style={{ width: "100%" }}>
                <Skeleton
                  variant="rectangular"
                  height={150}
                  width="100%"
                  sx={{ borderRadius: "2px" }}
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (arrPaymentMethods.length === 0) {
    return null;
  }

  return (
    <div className={styles.methods}>
      <div className={styles.items}>
        {arrPaymentMethods.map((item, index) => {
          return (
            <div
              key={index}
              className={`${styles.item} ${
                inputs.selectedPaymentMethod?.id === item.id && styles.active
              }`}
              onClick={() => onSelectPaymentMethod(getUntrackedObject(item))}
            >
              <img
                alt={item.label}
                src={item.image_url || Constants.image_default.empty}
              />
              <div className={styles.title}>{item.label}</div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

const RenderPaymentSummary = () => {
  const {
    inputs,
    onTextInputListeners,
    onShowModalAffiliate,
    isProductUseSerial,
  } = useTrackedPaymentStore();

  return (
    <div className={styles.summaries}>
      <>
        {/* <div className={styles.nominals}>
          <label>Total Transaksi</label>
          <input
            disabled
            value={CommonHelper.formatNumber(inputs.nominal_transaction, "idr")}
          />
        </div>
        {inputs.payment_mdr > 0 && (
          <div className={styles.nominals}>
            <label className="text-orange">MDR</label>
            <input
              className="text-orange"
              disabled
              value={CommonHelper.formatNumber(inputs.payment_mdr, "idr")}
            />
          </div>
        )} */}
        {/* <div className={styles.nominals}>
        <label className="text-green">Poin Digunakan</label>
        <input
          className="text-green"
          disabled
          value={CommonHelper.formatNumber(inputs.payment_poin_redeem, "idr")}
        />
      </div> */}
        {/* <div className={styles.nominals}>
          <label style={{ color: "var(--base-color)" }}>Total Bayar</label>
          <input
            style={{ color: "var(--base-color)" }}
            disabled
            value={CommonHelper.formatNumber(inputs.payment_total, "idr")}
          />
        </div> */}
      </>
      <div className={styles.affiliate_section}>
        <label className="text-green">Affiliate</label>
        <div
          className={`flex justify-end items-center gap-4`}
          style={{ width: "100%" }}
        >
          {inputs.affiliate_code && (
            <div>
              {inputs.affiliate_name} <b>({inputs.affiliate_code})</b>
            </div>
          )}
          <button className="button info ml-2" onClick={onShowModalAffiliate}>
            {inputs.affiliate_code ? "Ubah" : "Gunakan"} Affiliate
          </button>
        </div>
      </div>
      {/* <div className={styles.nominals}>
        <label>Est. Poin Didapat</label>
        <input disabled value={`${CommonHelper.formatNumber(27750)} Poin`} />
      </div> */}
    </div>
  );
};

const RenderPaymentActions = () => {
  const { onValidateListeners, isProductUseSerial } = useTrackedPaymentStore();

  return (
    <div className={styles.payment_action}>
      <button
        className="button"
        onClick={() => {
          onValidateListeners();
        }}
      >
        <i class="ph ph-bold ph-check-circle"></i>
        <span>Proses Pemesanan</span>
      </button>
      <button className="button" style={{ backgroundColor: "#e74c3c" }}>
        <i class="ph ph-bold ph-x-circle"></i>
        <span style={{ marginLeft: "1rem" }}>Batalkan</span>
      </button>
    </div>
  );
};

export default AuthWrapper(KioskPaymentPage, {
  redirectTo: Constants.webUrl.login,
});
