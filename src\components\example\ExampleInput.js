/*
Component to showcase Input and InputAutoComplete2 components
*/

import React, { useState, useRef } from 'react';
import Input from '@/components/libs/Input';
import InputAutoComplete2 from '@/components/libs/InputAutoComplete2';

const ExampleInput = () => {
  const [textInputValue, setTextInputValue] = useState('Initial Text');
  const [numberInputValue, setNumberInputValue] = useState('1.234.567');
  const [textareaValue, setTextareaValue] = useState('Initial textarea content.');
  const [selectValue, setSelectValue] = useState('option2');
  const [errorInput, setErrorInput] = useState('');
  const [autoCompleteValue, setAutoCompleteValue] = useState(null);
  const [autoCompleteInputValue, setAutoCompleteInputValue] = useState('');

  const ref_autoComplete = useRef(null);

  const handleInputChange = (setter) => (event) => {
    setter(event.target.value);
    console.log(`${event.target.name || 'Input'} changed:`, event.target.value);
  };

  const handleAutoCompleteChange = (selectedItem, type) => {
    console.log('AutoComplete Changed:', { selectedItem, type });
    if (type === 'select') {
      setAutoCompleteValue(selectedItem);
      setAutoCompleteInputValue(selectedItem ? selectedItem.name : ''); // Assuming 'name' is the displayTitle
    } else if (type === 'input') {
      // The input text itself is handled by the component's internal state for search
      // We might want to clear the selectedItem if the text input changes significantly
      setAutoCompleteValue(null);
    }
  };

  const selectOptions = [
    { value: '', label: 'Pilih Opsi...', disabled: true },
    { value: 'option1', label: 'Opsi 1' },
    { value: 'option2', label: 'Opsi 2' },
    { value: 'option3', label: 'Opsi 3 (Disabled)', disabled: true },
    { value: 'option4', label: 'Opsi Empat' },
  ];

  // Dummy data for InputAutoComplete2 - in a real app, this would come from an API
  const dummyApiData = [
    { id: '1', name: 'Apple', number: 'FRT-001', category: 'Fruit' },
    { id: '2', name: 'Banana', number: 'FRT-002', category: 'Fruit' },
    { id: '3', name: 'Carrot', number: 'VEG-001', category: 'Vegetable' },
    { id: '4', name: 'Broccoli', number: 'VEG-002', category: 'Vegetable' },
    { id: '5', name: 'Orange', number: 'FRT-003', category: 'Fruit' },
    { id: '6', name: 'Strawberry', number: 'FRT-004', category: 'Fruit' },
    { id: '7', name: 'Grapes', number: 'FRT-005', category: 'Fruit' },
    { id: '8', name: 'Watermelon', number: 'FRT-006', category: 'Fruit' },
    { id: '9', name: 'Spinach', number: 'VEG-003', category: 'Vegetable' },
    { id: '10', name: 'Potato', number: 'VEG-004', category: 'Vegetable' },
    { id: '11', name: 'Tomato', number: 'FRT-007', category: 'Fruit/Vegetable' },
    { id: '12', name: 'Cucumber', number: 'VEG-005', category: 'Vegetable' },
  ];

  // Mock ApiHelpers.get for InputAutoComplete2 for this showcase
  // In a real app, InputAutoComplete2 uses the global ApiHelpers
  const mockApiGet = async (url, params) => {
    console.log('Mock API Call:', url, params);
    await new Promise(resolve => setTimeout(resolve, 500)); // Simulate network delay
    let filteredData = dummyApiData;
    if (params && params.search) {
      const searchTerm = params.search.toLowerCase();
      filteredData = dummyApiData.filter(item =>
        item.name.toLowerCase().includes(searchTerm) ||
        (item.number && item.number.toLowerCase().includes(searchTerm))
      );
    }
    const page = params.page || 1;
    const limit = params.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    const paginatedData = filteredData.slice(startIndex, endIndex);

    return {
      status: 200,
      results: {
        data: paginatedData,
        pagination: {
          current: page,
          total_page: Math.ceil(filteredData.length / limit),
          total_data: filteredData.length,
        },
      },
    };
  };
  // If InputAutoComplete2 uses a global ApiHelper, this local mock won't be used by it.
  // For a true test, you'd mock the global ApiHelper or use a library like msw.
  // For this showcase, we'll assume the component handles the API call and we're just showing props.

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h1>Input Component Showcase</h1>
      <p style={{ marginBottom: '20px' }}>
        Demonstrating various props and functionalities of the <code>Input</code> and <code>InputAutoComplete2</code> components.
      </p>

      {/* --- Input Component Examples --- */}
      <section style={{ marginBottom: '40px', padding: '20px', border: '1px solid #eee', borderRadius: '8px' }}>
        <h2><code><Input /></code> Examples</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4>Basic Text Input</h4>
            <Input
              // Required props for controlled component
              label="Nama Lengkap" // Prop: label - Text for the input label
              value={textInputValue} // Prop: value - Current value of the input
              onChange={handleInputChange(setTextInputValue)} // Prop: onChange - Function to call on value change
              // Optional props
              name="fullName" // Prop: name - HTML name attribute
              placeholder="Masukkan nama lengkap Anda" // Prop: placeholder - Placeholder text
              onFocus={() => console.log('Text input focused')} // Prop: onFocus - Function on focus
              maxLength={50} // Prop: maxLength - Maximum characters allowed
              required // Prop: required - Displays "Wajib" and can be used for validation
            />
            <p className="text-sm text-gray-500 mt-1">Value: {textInputValue}</p>
          </div>

          <div>
            <h4>Text Input with Icon & Adornments</h4>
            <Input
              label="Email Address"
              value={textInputValue}
              onChange={handleInputChange(setTextInputValue)}
              placeholder="<EMAIL>"
              icon="ph-envelope-simple" // Prop: icon - Phosphor icon class for start adornment (if startAdornment not provided)
              startAdornment={<i className="ph ph-at" style={{ padding: '0 10px', color: '#888' }}></i>} // Prop: startAdornment - Custom element at the start
              endAdornment={<span style={{ padding: '0 10px', color: '#888' }}>.com</span>} // Prop: endAdornment - Custom element at the end
              type="email" // Prop: type - HTML input type
              optional // Prop: optional - Displays "Opsional"
            />
          </div>

          <div>
            <h4>Number Input (Formatted)</h4>
            <Input
              label="Jumlah (Formatted IDR)"
              value={numberInputValue} // Prop: value - Current value (string for formatted input)
              onChange={handleInputChange(setNumberInputValue)} // Prop: onChange - Handles the formatted string
              inputType="number" // Prop: inputType - Set to "number" for formatted input
              inputNumberOptions={{ // Prop: inputNumberOptions - Options for @react-input/number-format
                locales: "id-ID", // Indonesian locale for formatting
                currency: "IDR",
                currencyDisplay: "symbol", // "symbol", "code", "name"
                // maximumFractionDigits: 2, // Allow decimals
              }}
              placeholder="0"
              className="text-right" // Prop: className - Custom class for the input element itself
              required
            />
             <p className="text-sm text-gray-500 mt-1">Raw Value: {numberInputValue} (Formatted)</p>
          </div>

          <div>
            <h4>Textarea Input</h4>
            <Input
              label="Deskripsi"
              value={textareaValue}
              onChange={handleInputChange(setTextareaValue)}
              inputType="textarea" // Prop: inputType - Set to "textarea"
              placeholder="Tuliskan deskripsi singkat..."
              maxLength={200} // Prop: maxLength - For textarea
              rows={4} // Prop: rows - Standard HTML attribute for textarea
              required
            />
          </div>

          <div>
            <h4>Select Input</h4>
            <Input
              label="Pilih Kategori"
              value={selectValue} // Prop: value - The selected value
              onChange={handleInputChange(setSelectValue)} // Prop: onChange - Handles selection change
              inputType="select" // Prop: inputType - Set to "select"
              options={selectOptions} // Prop: options - Array of {value, label, disabled?} for select
              required
              icon="ph-tag" // Icon can also be used with select
            />
            <p className="text-sm text-gray-500 mt-1">Selected: {selectValue}</p>
          </div>

          <div>
            <h4>Input with Error</h4>
            <Input
              label="Kode Verifikasi"
              value={errorInput}
              onChange={handleInputChange(setErrorInput)}
              error={errorInput.length < 6 ? "Kode minimal 6 karakter" : null} // Prop: error - Error message string or null
              onFocus={() => console.log('Error input focused')}
              required
              maxLength={10}
            />
          </div>

          <div>
            <h4>Readonly Input</h4>
            <Input
              label="ID Pengguna (Readonly)"
              value="USER-12345"
              onChange={() => {}} // onChange is still good practice for consistency
              readonly // Prop: readonly - Makes the input non-editable
              readonlyMessage="ID ini dibuat otomatis oleh sistem." // Prop: readonlyMessage - Message displayed below readonly input
            />
          </div>

          <div>
            <h4>Input without Placeholder Helper Text</h4>
            <Input
              label="Catatan Tambahan"
              value=""
              onChange={() => {}}
              disablePlaceholder // Prop: disablePlaceholder - Hides the helper text below the input
            />
          </div>
        </div>
      </section>

      {/* --- InputAutoComplete2 Component Examples --- */}
      <section style={{ marginBottom: '40px', padding: '20px', border: '1px solid #eee', borderRadius: '8px' }}>
        <h2><code><InputAutoComplete2 /></code> Examples</h2>
        <p className="text-sm text-gray-500 mb-4">
          Note: The <code>dataUrl</code> for <code>InputAutoComplete2</code> is using a mock data source for this showcase.
          In a real application, it would point to an API endpoint.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4>Basic AutoComplete</h4>
            <InputAutoComplete2
              ref={ref_autoComplete} // Prop: ref - To access component methods like setDefaultInputs
              // Essential props
              label="Cari Produk" // Prop: label - Text for the input label
              inputName="productSearch" // Prop: inputName - HTML name attribute for the input
              dataUrl="/api/dummy/products" // Prop: dataUrl - API endpoint to fetch data (placeholder)
              onChange={handleAutoCompleteChange} // Prop: onChange - Function for selection and input text changes
              displayTitle="name" // Prop: displayTitle - Object key for the main display text in suggestions
              // Optional props
              placeholder="Ketik nama atau kode produk..." // Prop: placeholder - Placeholder text
              dataParams={{ type: 'electronics', inStock: true }} // Prop: dataParams - Additional static params for API
              icon="ph-magnifying-glass" // Prop: icon - Phosphor icon class
              displaySubtitle="number" // Prop: displaySubtitle - Object key for secondary display text
              pageSize={5} // Prop: pageSize - Number of items per page/API call
              required // Prop: required - Displays "Wajib"
            />
            {autoCompleteValue && (
              <p className="text-sm text-gray-500 mt-1">
                Selected: {autoCompleteValue.name} ({autoCompleteValue.number})
              </p>
            )}
            <button
              className="button mt-2"
              onClick={() => ref_autoComplete.current?.setDefaultInputs('Banana')}
            >
              Set Default "Banana"
            </button>
          </div>

          <div>
            <h4>AutoComplete with Custom Render</h4>
            <InputAutoComplete2
              label="Pilih Pengguna (Custom Render)"
              inputName="userSearchCustom"
              dataUrl="/api/dummy/users" // Placeholder
              onChange={(item, type) => console.log('Custom Render AC Change:', item, type)}
              displayTitle="name" // Still needed for default input value setting on select
              displayRenderCustom={(item, index) => ( // Prop: displayRenderCustom - Function to render custom suggestion item
                <div key={item.id || index} style={{ display: 'flex', alignItems: 'center', padding: '8px 0' }}>
                  <img
                    src={`https://i.pravatar.cc/40?u=${item.id}`}
                    alt={item.name}
                    style={{ width: '30px', height: '30px', borderRadius: '50%', marginRight: '10px' }}
                  />
                  <div>
                    <div style={{ fontWeight: 'bold' }}>{item.name}</div>
                    <div style={{ fontSize: '0.8em', color: '#555' }}>ID: {item.id} | Category: {item.category}</div>
                  </div>
                </div>
              )}
              pageSize={3}
              optional
            />
          </div>

          <div>
            <h4>Readonly AutoComplete</h4>
            <InputAutoComplete2
              label="Item Terpilih (Readonly)"
              inputName="readonlyAC"
              dataUrl="/api/dummy/items" // Placeholder
              onChange={() => {}}
              displayTitle="name"
              readonly // Prop: readonly - Makes the input non-editable
              readonlyMessage="Item ini sudah dikunci." // Prop: readonlyMessage
              // To show a default value in readonly, you'd typically set it via `setDefaultInputs`
              // or manage the input's value externally if the component supports it.
              // For this example, we'll assume it's set and just shows the placeholder.
              placeholder="Item A (Readonly)"
            />
             <p className="text-sm text-gray-500 mt-1">
              (Note: To properly show a default readonly value, you might need to call `setDefaultInputs` on mount if the component is controlled this way, or ensure the internal input value is set.)
            </p>
          </div>

           <div>
            <h4>AutoComplete with Error</h4>
            <InputAutoComplete2
              label="Pilih Kota (Wajib)"
              inputName="citySearchError"
              dataUrl="/api/dummy/cities" // Placeholder
              onChange={() => {}}
              displayTitle="name"
              error={!autoCompleteValue ? "Kota harus dipilih" : null} // Prop: error - Error message
              required
            />
          </div>

        </div>
      </section>
    </div>
  );
};

export default ExampleInput;