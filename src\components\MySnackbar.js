import { Snackbar, Alert } from "@mui/material";
import React, { Component } from "react";

export default class MySnackbar extends Component {
  constructor(props) {
    super(props);

    this.state = {
      showSnackbar: false,
      messageSnackbar: "",
      severitySnackbar: "info",
    };
  }

  onNotify = (messageSnackbar, severitySnackbar) => {
    if (this.setState !== null) {
      this.setState({
        showSnackbar: true,
        messageSnackbar,
        severitySnackbar,
      });
    }
  };

  render() {
    return (
      <Snackbar
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
        style={{ bottom: 80 }}
        open={this.state.showSnackbar}
        autoHideDuration={10000}
        onClose={() => {
          this.setState({ showSnackbar: false, messageSnackbar: "" });
        }}
      >
        <Alert
          severity={this.state.severitySnackbar}
          onClose={() => {
            this.setState({ showSnackbar: false, messageSnackbar: "" });
          }}
          variant="filled"
          sx={{
            width: {
              xs: "100%",
              sm: "100%",
              md: "100%",
            },
            whiteSpace: "pre-wrap",
          }}
        >
          {this.state.messageSnackbar}
        </Alert>
      </Snackbar>
    );
  }
}
