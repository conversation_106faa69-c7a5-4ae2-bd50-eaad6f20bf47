import { create } from "zustand";
import { createTrackedSelector } from "react-tracked";
import <PERSON>pi<PERSON>elper from "@/utils/ApiHelper";
import CommonHelper from "@/utils/CommonHelper";
import { usePersistKioskPosStore } from "../../storePersist";
import Router from "next/router";

const DEFAULT_INPUTS = {
  sub_total: 0,
  nominal_discount_label: "0",
  nominal_discount: 0,
  nominal_transaction: 0,
  selectedPaymentMethod: null,
  mdr: 0,
  service_fee: 0,
  payment_mdr: 0,
  payment_poin_redeem: 0,
  payment_total: 0, // nominal_transaction + payment_mdr
  payment_nominal_input: 0, // <PERSON><PERSON> yang dibayar
  payment_nominal_return: 0, // Kembalian dari nilai yang dibayar
};

const page = (set, get) => ({
  setState: (newValue, cb = null, keys = []) => {
    set((prevState) => {
      if (keys.length > 0) {
        return CommonHelper.updateNestedState(prevState, keys, newValue);
      }
      // Shallow update if no keys provided
      return {
        ...prevState,
        ...newValue,
      };
    });

    if (cb) {
      setTimeout(() => {
        cb();
      }, 0);
    }
  },

  paymentDetail: null,
  loading: false,
  error: null,
  ref_Loading: null,
  ref_MySnackbar: null,
  ref_ModalConfirmation: null,

  intervalRefresh: null,
  number: "",

  inputs: DEFAULT_INPUTS,

  onLoading: (show) => {
    if (get().ref_Loading.current) {
      if (show) {
        get().ref_Loading.current.onShowDialog();
      } else {
        get().ref_Loading.current.onCloseDialog();
      }
    }
  },
  onNotify: (message, severity) => {
    if (get().ref_MySnackbar.current) {
      get().ref_MySnackbar.current.onNotify(message, severity);
    }
  },

  onInit: async () => {
    const params = CommonHelper.getAllURLParams();
    if (params?.code) {
      set({ number: params.code });
      get().onLoading(true);
      await get().fetchPaymentDetail();
      get().onLoading(false);

      if (get().intervalRefresh) {
        clearInterval(get().intervalRefresh);
      }

      const intervalRefresh = setInterval(() => {
        get().fetchPaymentDetail();
      }, 10000);

      set({ intervalRefresh });
    }
  },

  fetchPaymentDetail: async () => {
    try {
      const response = await ApiHelper.get("kiosk/kiosk/pos/payment/detail", {
        number: get().number,
        customer_id: usePersistKioskPosStore.getState().objCustomer.id,
      });
      if (response.status === 200) {
        const paymentDetail = response.results.data;
        
        if (paymentDetail.status === "Dihapus") {
          Router.push({ pathname: "/pos" });
          return;
        }
        set({ paymentDetail, loading: false });
        return response.results.data;
      } else {
        get().onNotify(response.message, "error");
        set({ error: response.message, loading: false });
        return null;
      }
    } catch (error) {
      get().onNotify(error.message, "error");
      set({ error: error.message, loading: false });
      return null;
    }
  },

  onSelectPaymentMethod: (paymentMethod) => {
    set({ inputs: { ...get().inputs, selectedPaymentMethod: paymentMethod } });
    get().onCalculatePayment();
  },

  onCalculatePayment: () => {
    const inputs = get().inputs;

    const totalProductSellingPrice =
      get().paymentDetail.total_product_selling_price;

    inputs.sub_total = totalProductSellingPrice;

    // discount voucher
    if (inputs.selectedVoucher?.id) {
      inputs.sub_total -= Math.abs(
        inputs.selectedVoucher?.product_selling_price
      );
    }

    inputs.nominal_transaction = inputs.sub_total - inputs.nominal_discount;

    inputs.mdr = 0;
    inputs.service_fee = 0;
    if (inputs.selectedPaymentMethod?.id) {
      if (inputs.selectedPaymentMethod?.id === 1) {
        inputs.payment_mdr = 0;
      } else {
        // calculate mdr
        inputs.mdr = Number(inputs.selectedPaymentMethod?.mdr);
        inputs.service_fee = Number(inputs.selectedPaymentMethod?.service_fee);
        if (inputs.selectedPaymentMethod?.mdr_type === "percent") {
          inputs.mdr = (inputs.nominal_transaction * inputs.mdr) / 100;
        }
        if (inputs.selectedPaymentMethod?.admin_fee_type === "percent") {
          inputs.service_fee =
            (inputs.nominal_transaction * inputs.service_fee) / 100;
        }

        inputs.payment_mdr = inputs.mdr + inputs.service_fee;
      }
    }

    inputs.payment_total =
      Number(inputs.nominal_transaction) + Number(inputs.payment_mdr);

    set({ inputs });
  },

  onValidateListeners: () => {
    let isValid = false;
    let inputs = get().inputs;
    if (!inputs.selectedPaymentMethod) {
      get().onNotify("Silahkan pilih cara bayar", "warning");
      return;
    }

    get().ref_ModalConfirmation.current.onShowDialog("custom", {
      text: {
        title: "Konfirmasi Pembayaran",
        action: "Ya",
        info: <>Apakah Anda yakin akan melakukan pembayaran?</>,
      },
      render: (formData) => {
        return <div>Apakah Anda yakin akan melakukan pembayaran?</div>;
      },
      icon: (
        <>
          <i className="ph ph-bold ph-check-circle"></i>
        </>
      ),
      classBody: "flex flex-col gap-4 items-center justify-center",
      onConfirmed: (formType, formData, formIndex) => {
        get().ref_ModalConfirmation.current.onCloseDialog();
        get().onSubmitPayment();
      },
    });
  },

  onSubmitPayment: async (retry = false) => {
    get().onLoading(true);
    let inputs = get().inputs;
    const paymentDetail = get().paymentDetail;
    if (retry) {
      const paymentMethod = paymentDetail.payment_method_array.find(
        (item) => item.id == paymentDetail.kiosk_payment_method_ref_id
      );
      if (paymentMethod) {
        inputs.selectedPaymentMethod = paymentMethod;
        get().onCalculatePayment();
      }

      inputs = get().inputs;
    }

    let params = {
      number: get().paymentDetail.number,
      inputsPayment: inputs,
      objCustomer: usePersistKioskPosStore.getState().objCustomer,
      retry_bool: retry,
    };

    let response = await ApiHelper.post(
      "kiosk/kiosk/pos/payment/process",
      params
    );

    if (response.status === 200) {
      get().onLoading(false, 0);
      get().onInit();
      get().onNotify("Berhasil Proses Pembayaran", "success");
    } else {
      get().onLoading(false);
      get().onNotify(response?.message || "Terjadi Kesalahan", "error");
    }
  },
});

export const usePaymentDetailStore = create((...a) => ({
  ...page(...a),
}));

const useTrackedPaymentDetailStore = createTrackedSelector(
  usePaymentDetailStore
);

export default useTrackedPaymentDetailStore;
