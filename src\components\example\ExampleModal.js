/*
Created by esoda
Created on Nov, 2024
Contact esoda.id
*/

import React from "react";
import { Modal, Box, TextField, InputAdornment, MenuItem } from "@mui/material";
import Loading from "@/components/modal/Loading";
import Constants from "@/utils/Constants";

const DEFAULT_INPUTS = {
  id: "",
  name: "",
  username: "",
  email: "",
  password: "",
  image_url: "",
  mobilephone: "",
  role_id: "",
  role_name: "",
  active_bool: "",
  all_branch_bool: "",
  branch_array: "",
  type: "kiosk",
};

export default class ExampleModal extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showDialog: false,
      formType: "add",
      formData: null,
      formIndex: -1,

      inputs: structuredClone(DEFAULT_INPUTS),
      errors: {},
    };
  }

  // ====================================================================================
  // ========== INITIALIZE, GET DATA ====================================================
  // ====================================================================================
  onShowDialog = (formType, formData = null, formIndex = -1) => {
    this.setState(
      {
        formType,
        formData,
        formIndex,
        showDialog: true,
      },
      this.getCategory
    );
    setTimeout(() => {
      var inputNameID = document.getElementById("input-name");
      if (inputNameID) {
        inputNameID.focus();
      }
    }, 250);
  };
  onCloseDialog = () => {
    this.setState({
      showDialog: false,
      formType: "add",
      formData: null,
      formIndex: -1,

      inputs: structuredClone(DEFAULT_INPUTS),
      errors: {},
    });
  };

  // ====================================================================================
  // ========== ACTION LISTENERS ========================================================
  // ====================================================================================
  onTextInputListeners = (text, input) => {
    let inputs = this.state.inputs;
    inputs[input] = text;
    this.setState((prevState) => ({ ...prevState, inputs }));
  };
  onTextErrorListeners = (error, input) => {
    let errors = this.state.errors;
    errors[input] = error;
    this.setState((prevState) => ({ ...prevState, errors }));
  };
  onValidateListeners = () => {
    if (this.state.formType === "activated") {
      this.actOnActivateListeners();
    }

    if (this.state.formType === "delete") {
      this.actOnDeleteListeners();
    }

    if (this.state.formType === "add" || this.state.formType === "edit") {
      let inputs = this.state.inputs;
      let isValid = true;

      if (!inputs.name) {
        this.onTextErrorListeners("Harus diisi", "name");
        isValid = false;
      } else if (inputs.name.length < 3) {
        this.onTextErrorListeners("Minimal 3 karakter", "name");
        isValid = false;
      }

      if (!inputs.category_id || inputs.category_id === "0") {
        this.onTextErrorListeners("Harus dipilih", "category_id");
        isValid = false;
      }

      if (isValid) {
        if (this.state.formType === "add") {
          this.actOnAddListeners();
        } else {
          this.actOnEditListeners();
        }
      }
    }
  };
  actOnActivateListeners = () => {
    this.ref_Loading.onShowDialog();
    setTimeout(() => {
      this.ref_Loading.onCloseDialog();
    }, 300);
    setTimeout(() => {
      const { onResults } = this.props;
      if (onResults) {
        onResults(
          this.state.formType,
          this.state.formData,
          this.state.formIndex
        );
      }
      this.onCloseDialog();
    }, 400);
  };
  actOnDeleteListeners = () => {
    this.ref_Loading.onShowDialog();
    setTimeout(() => {
      this.ref_Loading.onCloseDialog();
    }, 300);
    setTimeout(() => {
      const { onResults } = this.props;
      if (onResults) {
        onResults(
          this.state.formType,
          this.state.formData,
          this.state.formIndex
        );
      }
      this.onCloseDialog();
    }, 400);
  };
  actOnAddListeners = () => {
    this.ref_Loading.onShowDialog();
    setTimeout(() => {
      this.ref_Loading.onCloseDialog();
    }, 300);
    setTimeout(() => {
      const { onResults } = this.props;
      if (onResults) {
        onResults(
          this.state.formType,
          this.state.formData,
          this.state.formIndex
        );
      }
      this.onCloseDialog();
    }, 400);
  };
  actOnEditListeners = () => {
    this.ref_Loading.onShowDialog();
    setTimeout(() => {
      this.ref_Loading.onCloseDialog();
    }, 300);
    setTimeout(() => {
      const { onResults } = this.props;
      if (onResults) {
        onResults(
          this.state.formType,
          this.state.formData,
          this.state.formIndex
        );
      }
      this.onCloseDialog();
    }, 400);
  };

  // ====================================================================================
  // ========== RENDER SECTIONS =========================================================
  // ====================================================================================
  render() {
    let addModalClass = "medium";
    if (
      this.state.formType === "delete" ||
      this.state.formType === "activated"
    ) {
      addModalClass = "dialog";
    }
    return (
      <>
        <Modal open={this.state.showDialog} onClose={this.onCloseDialog}>
          <Box
            sx={{
              flex: 1,
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              display: "flex",
              width: "100wh",
              height: "100vh",
            }}
          >
            <div className={`modal-content ${addModalClass}`}>
              <div className="modal-header">{this.renderHeader()}</div>
              <div className="modal-body">{this.renderBody()}</div>
              <div className="modal-footer">
                {this.renderFooter()}
                <button
                  className="button cancel"
                  onClick={() => this.onCloseDialog()}
                >
                  <i className="ph ph-bold ph-x-circle"></i>
                  <span>
                    {this.state.formType === "info" ? "Tutup" : "Batal"}
                  </span>
                </button>
              </div>
            </div>
          </Box>
        </Modal>

        {/* Loading dialog */}
        <Loading ref={(value) => (this.ref_Loading = value)} />
      </>
    );
  }

  renderHeader() {
    return (
      <>
        <div className="title">
          {this.state.formType === "add" && `Tambah Data REPLACE_HERE`}
          {this.state.formType === "info" && `Detail Data REPLACE_HERE`}
          {this.state.formType === "edit" && `Ubah Data REPLACE_HERE`}
          {this.state.formType === "delete" && `Hapus Data REPLACE_HERE`}
          {this.state.formType === "activated" &&
            `Aktif/Non-Aktif Data REPLACE_HERE`}
        </div>
        <span className="close" onClick={() => this.onCloseDialog()}>
          &times;
        </span>
      </>
    );
  }

  renderBody() {
    return (
      <>
        {(this.state.formType === "add" || this.state.formType === "edit") &&
          this.renderForm()}
        {this.state.formType === "info" && (
          <div style={{ textAlign: "center" }}>DETAIL DATA REPLACE_HERE</div>
        )}
        {this.state.formType === "delete" && (
          <div style={{ textAlign: "center" }}>
            Apakah Anda yakin akan hapus data
            <br />
            <b>REPLACE_HERE</b>?
          </div>
        )}
        {this.state.formType === "activated" && (
          <div style={{ textAlign: "center" }}>
            Apakah Anda yakin akan aktif/non-aktif
            <br />
            <b>REPLACE_HERE</b>?
          </div>
        )}
      </>
    );
  }

  renderForm() {
    return null;
    return (
      <>
        <div className="flex-rows no-right">
          <div className="row no-border">
            <div className="box">
              <div className="title" style={{ color: "#E50040" }}>
                Informasi Pelanggan
              </div>
              {/* === INPUT IMAGE */}
              <div className="input-image">
                <img src={this.state.inputs.image_url} />
                <div>
                  <small style={{ fontSize: ".8rem", color: "#27ae60" }}>
                    Opsional
                  </small>
                  <p className="note">Maksimal ukuran file 1MB</p>
                  <button className="button cancel">
                    <i className="ph ph-bold ph-camera"></i>
                    Gambar
                  </button>
                </div>
              </div>
              {/*=== INPUT NAME & CODE*/}
              <div className="flex-container">
                {/*=== INPUT NAME */}
                <div className="input-form">
                  <div className="label">Nama Pelanggan</div>
                  <TextField
                    id="input-name"
                    fullWidth
                    placeholder="Tuliskan disini..."
                    value={this.state.inputs.name}
                    onChange={(event) => {
                      if (
                        event.target !== undefined &&
                        event.target.value !== undefined
                      ) {
                        if (event.target.value.length <= 50) {
                          this.onTextInputListeners(event.target.value, "name");
                        }
                      }
                    }}
                    error={
                      this.state.errors.name !== undefined &&
                      this.state.errors.name !== null
                        ? true
                        : false
                    }
                    onFocus={() => this.onTextErrorListeners(null, "name")}
                  />
                  <div className="placeholder">
                    <div className="required">
                      {this.state.errors.name !== undefined &&
                      this.state.errors.name !== null
                        ? this.state.errors.name
                        : "Harus diisi"}
                    </div>
                    <div className="note">
                      <b>{this.state.inputs.name.length}</b> dari 50 karakter
                    </div>
                  </div>
                </div>
                {/*=== INPUT CATEGORY */}
                <div className="input-form" style={{ marginTop: 0 }}>
                  <div className="label">Kategori Pelanggan</div>
                  <TextField
                    fullWidth
                    select
                    value={this.state.inputs.category_id}
                    onChange={(event) => {
                      if (
                        event.target !== undefined &&
                        event.target.value !== undefined
                      ) {
                        this.onTextInputListeners(
                          event.target.value,
                          "category_id"
                        );

                        // let arrCategory = this.state.arrCategory
                        // let idx = arrCategory.findIndex((item) => item.id === event.target.value)
                        // if(idx >= 0) {
                        //     this.onTextInputListeners(arrCategory[idx].name, 'category_name')
                        // }
                      }
                    }}
                    error={
                      this.state.errors.category_id !== undefined &&
                      this.state.errors.category_id !== null
                        ? true
                        : false
                    }
                    onFocus={() =>
                      this.onTextErrorListeners(null, "category_id")
                    }
                  >
                    <MenuItem value={"0"}>
                      <em>Pilih Kategori Pelanggan</em>
                    </MenuItem>
                    {this.state.arrCategory.length > 0 &&
                      this.state.arrCategory.map((cat, idxs) => {
                        return (
                          <MenuItem key={idxs} value={cat.id}>
                            {cat.name}
                          </MenuItem>
                        );
                      })}
                  </TextField>
                  <div className="placeholder">
                    <div className="required">
                      {this.state.errors.category_id !== undefined &&
                      this.state.errors.category_id !== null
                        ? this.state.errors.category_id
                        : "Harus dipilih"}
                    </div>
                    <div className="note">Pilih dari pilihan</div>
                  </div>
                </div>
              </div>
              {/*=== INPUT PROVINCE & CITY*/}
              <div className="flex-container">
                {/*=== INPUT PROVINCE */}
                <div className="input-form">
                  <div className="label">Provinsi</div>
                  <TextField
                    fullWidth
                    select
                    value={this.state.inputs.province_id}
                    onChange={(event) => {
                      if (
                        event.target !== undefined &&
                        event.target.value !== undefined
                      ) {
                        this.onTextInputListeners(
                          event.target.value,
                          "province_id"
                        );
                        if (event.target.value === "0") {
                          this.onTextInputListeners("0", "city_id");
                        }
                      }
                    }}
                    error={
                      this.state.errors.province_id !== undefined &&
                      this.state.errors.province_id !== null
                        ? true
                        : false
                    }
                    onFocus={() =>
                      this.onTextErrorListeners(null, "province_id")
                    }
                  >
                    <MenuItem value={"0"}>
                      <em>Pilih Provinsi</em>
                    </MenuItem>
                    {this.state.arrProvince.length > 0 &&
                      this.state.arrProvince.map((cat, idxs) => {
                        return (
                          <MenuItem key={idxs} value={cat.id}>
                            {cat.name}
                          </MenuItem>
                        );
                      })}
                  </TextField>
                  <div className="placeholder">
                    <div className="optional">
                      {this.state.errors.province_id !== undefined &&
                      this.state.errors.province_id !== null
                        ? this.state.errors.category_id
                        : "Opsional"}
                    </div>
                    <div className="note">Pilih dari pilihan</div>
                  </div>
                </div>
                {/*=== INPUT CITY */}
                <div className="input-form" style={{ marginTop: 0 }}>
                  <div className="label">Kota Kabupaten</div>
                  <TextField
                    fullWidth
                    select
                    value={this.state.inputs.city_id}
                    onChange={(event) => {
                      if (
                        event.target !== undefined &&
                        event.target.value !== undefined
                      ) {
                        this.onTextInputListeners(
                          event.target.value,
                          "city_id"
                        );
                      }
                    }}
                    error={
                      this.state.errors.city_id !== undefined &&
                      this.state.errors.city_id !== null
                        ? true
                        : false
                    }
                    onFocus={() => this.onTextErrorListeners(null, "city_id")}
                    disabled={
                      this.state.inputs.province_id === "0" ? true : false
                    }
                  >
                    <MenuItem value={"0"}>
                      <em>Pilih Kota Kabupaten</em>
                    </MenuItem>
                    {this.state.arrCity.length > 0 &&
                      this.state.arrCity.map((cat, idxs) => {
                        return (
                          <MenuItem key={idxs} value={cat.id}>
                            {cat.name}
                          </MenuItem>
                        );
                      })}
                  </TextField>
                  <div className="placeholder">
                    <div className="optional">
                      {this.state.errors.city_id !== undefined &&
                      this.state.errors.city_id !== null
                        ? this.state.errors.category_id
                        : "Opsional"}
                    </div>
                    <div className="note">Pilih dari pilihan</div>
                  </div>
                </div>
              </div>
              {/*=== INPUT ADDRESS */}
              <div className="input-form">
                <div className="label">Alamat</div>
                <TextField
                  fullWidth
                  placeholder="Tuliskan disini..."
                  value={this.state.inputs.address}
                  onChange={(event) => {
                    if (
                      event.target !== undefined &&
                      event.target.value !== undefined
                    ) {
                      if (event.target.value.length <= 250) {
                        this.onTextInputListeners(
                          event.target.value,
                          "address"
                        );
                      }
                    }
                  }}
                  error={
                    this.state.errors.address !== undefined &&
                    this.state.errors.address !== null
                      ? true
                      : false
                  }
                  onFocus={() => this.onTextErrorListeners(null, "address")}
                  multiline
                  rows={2.35}
                />
                <div className="placeholder">
                  <div className="optional">
                    {this.state.errors.address !== undefined &&
                    this.state.errors.address !== null
                      ? this.state.errors.address
                      : "Opsional"}
                  </div>
                  <div className="note">
                    <b>{this.state.inputs.address.length}</b> dari 250 karakter
                  </div>
                </div>
              </div>
              {/*=== INPUT STATUS */}
              <div className="input-form">
                <div className="label">Status Aktif</div>
                <TextField
                  slotProps={{
                    input: {
                      startAdornment: (
                        <InputAdornment position="start">
                          <i
                            className={`ph ph-bold ph-toggle-left ${
                              this.state.inputs.active_bool && "text-green"
                            }`}
                          ></i>
                        </InputAdornment>
                      ),
                    },
                  }}
                  select
                  value={this.state.inputs.active_bool}
                  onChange={(event) => {
                    if (
                      event.target !== undefined &&
                      event.target.value !== undefined
                    ) {
                      this.onTextInputListeners(
                        event.target.value,
                        "active_bool"
                      );
                    }
                  }}
                >
                  <MenuItem value={true}>Aktif</MenuItem>
                  <MenuItem value={false}>Tidak Aktif</MenuItem>
                </TextField>
              </div>
            </div>
          </div>
          <div className="row wd60">
            <div className="box">
              <div className="title" style={{ color: "#16a085" }}>
                GEO-LOCATION
              </div>
              <div className="flex-rows" style={{ marginTop: "1rem" }}>
                {/*=== INPUT LATITUDE */}
                <div className="input-form row no-border">
                  <div className="label">Latitude</div>
                  <TextField
                    fullWidth
                    placeholder="Tuliskan disini..."
                    value={this.state.inputs.latitude}
                    onChange={(event) => {
                      if (
                        event.target !== undefined &&
                        event.target.value !== undefined
                      ) {
                        this.onTextInputListeners(
                          event.target.value,
                          "latitude"
                        );
                      }
                    }}
                    error={
                      this.state.errors.latitude !== undefined &&
                      this.state.errors.latitude !== null
                        ? true
                        : false
                    }
                    onFocus={() => this.onTextErrorListeners(null, "latitude")}
                  />
                  <div className="placeholder">
                    <div className="optional">
                      {this.state.errors.latitude !== undefined &&
                      this.state.errors.latitude !== null
                        ? this.state.errors.latitude
                        : "Opsional"}
                    </div>
                  </div>
                </div>
                {/*=== INPUT LONGITUDE */}
                <div className="input-form row no-border" style={{ marginTop: 0 }}>
                  <div className="label">Longitude</div>
                  <TextField
                    fullWidth
                    placeholder="Tuliskan disini..."
                    value={this.state.inputs.longitude}
                    onChange={(event) => {
                      if (
                        event.target !== undefined &&
                        event.target.value !== undefined
                      ) {
                        this.onTextInputListeners(
                          event.target.value,
                          "longitude"
                        );
                      }
                    }}
                    error={
                      this.state.errors.longitude !== undefined &&
                      this.state.errors.longitude !== null
                        ? true
                        : false
                    }
                    onFocus={() => this.onTextErrorListeners(null, "longitude")}
                  />
                  <div className="placeholder">
                    <div className="optional">
                      {this.state.errors.longitude !== undefined &&
                      this.state.errors.longitude !== null
                        ? this.state.errors.longitude
                        : "Opsional"}
                    </div>
                  </div>
                </div>
              </div>
              <button
                className="button cancel"
                style={{ marginLeft: "0", marginTop: "1rem" }}
                onClick={() => {}}
              >
                <i className="ph ph-bold ph-map-pin-simple"></i>
                <span>Pilih Dari Peta</span>
              </button>
            </div>

            <div className="box" style={{ marginTop: "1.2rem" }}>
              <div
                className="title"
                style={{ color: "#e67e22", marginBottom: "0.5rem" }}
              >
                INFORMASI KONTAK
              </div>
              {/*=== INPUT CONTACT NUMBER */}
              <div className="input-form">
                <div className="label">Nomor Kontak</div>
                <TextField
                  fullWidth
                  placeholder="Tuliskan disini..."
                  value={this.state.inputs.contact_number}
                  onChange={(event) => {
                    if (
                      event.target !== undefined &&
                      event.target.value !== undefined
                    ) {
                      if (event.target.value.length <= 20) {
                        this.onTextInputListeners(
                          event.target.value,
                          "contact_number"
                        );
                      }
                    }
                  }}
                  error={
                    this.state.errors.contact_number !== undefined &&
                    this.state.errors.contact_number !== null
                      ? true
                      : false
                  }
                  onFocus={() =>
                    this.onTextErrorListeners(null, "contact_number")
                  }
                />
                <div className="placeholder">
                  <div className="optional">
                    {this.state.errors.contact_number !== undefined &&
                    this.state.errors.contact_number !== null
                      ? this.state.errors.contact_number
                      : "Opsional"}
                  </div>
                  <div className="note">
                    <b>{this.state.inputs.contact_number.length}</b> dari 20
                    karakter
                  </div>
                </div>
              </div>
              {/*=== INPUT CONTACT EMAIL */}
              <div className="input-form">
                <div className="label">Alamat Email</div>
                <TextField
                  fullWidth
                  placeholder="Tuliskan disini..."
                  value={this.state.inputs.contact_email}
                  onChange={(event) => {
                    if (
                      event.target !== undefined &&
                      event.target.value !== undefined
                    ) {
                      if (event.target.value.length <= 50) {
                        this.onTextInputListeners(
                          event.target.value,
                          "contact_email"
                        );
                      }
                    }
                  }}
                  error={
                    this.state.errors.contact_email !== undefined &&
                    this.state.errors.contact_email !== null
                      ? true
                      : false
                  }
                  onFocus={() =>
                    this.onTextErrorListeners(null, "contact_email")
                  }
                />
                <div className="placeholder">
                  <div className="optional">
                    {this.state.errors.contact_email !== undefined &&
                    this.state.errors.contact_email !== null
                      ? this.state.errors.contact_email
                      : "Opsional"}
                  </div>
                  <div className="note">
                    <b>{this.state.inputs.contact_email.length}</b> dari 50
                    karakter
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }

  renderFooter() {
    return (
      <>
        <button
          className={`button ${this.state.formType === "edit" && "warning"} ${
            this.state.formType === "delete" && "danger"
          }`}
          onClick={() => {
            this.onValidateListeners();
          }}
        >
          {this.state.formType === "add" && (
            <i className="ph ph-bold ph-check-circle"></i>
          )}
          {this.state.formType === "edit" && (
            <i className="ph ph-bold ph-pencil"></i>
          )}
          {this.state.formType === "delete" && (
            <i className="ph ph-bold ph-trash-simple"></i>
          )}
          {this.state.formType === "activated" && (
            <i className="ph ph-bold ph-check-circle"></i>
          )}
          <span>
            {this.state.formType === "add" && "Tambah Data"}
            {this.state.formType === "edit" && "Ubah Data"}
            {this.state.formType === "delete" && "Hapus Data"}
            {this.state.formType === "activated" && "Aktif/Non-aktif"}
          </span>
        </button>
      </>
    );
  }
}
