/*
Created by esoda
Created on Mar, 2025
Contact esoda.id
*/

import React, { useRef } from 'react';
import Loading from "../modal/Loading";
import Constants from "@/utils/Constants";
import Tooltip from '@mui/material/Tooltip';
import Snackbar from '@mui/material/Snackbar';
import Alert from '@mui/material/Alert';
import ApiHelpers from '@/utils/ApiHelper';

const ImageUploadSingle = ({ id, title, placeholder, alt, image_url, error, onCompleted = () => { }, optional, ...props }) => {
    const ref_loading = useRef(null)
    const [openSnackbar, setOpenSnackbar] = React.useState(false)
    const [severitySnackbar, setSeveritySnackbar] = React.useState("")
    const [messageSnackbar, setMessageSnackbar] = React.useState("")


    // ====================================================================================
    // ========== ACTION LISTENERS ========================================================
    // ====================================================================================
    const onUploadImageListeners = async (e) => {
        if (e.target.files[0].size > 1000000) {
            setOpenSnackbar(true)
            setMessageSnackbar("Ukuran gambar terlalu besar.")
            setSeveritySnackbar("error")
        } else {
            ref_loading.current.onShowDialog()
            
            let formData = new FormData();
            formData.append("image", e.target.files[0]);

            let response = await ApiHelpers.uploadImage(formData)
            if (response.status === 200) {
                ref_loading.current.onCloseDialog()
                onCompleted(response.data.fileuri)
            } else {
                ref_loading.current.onCloseDialog()
                setOpenSnackbar(true)
                setMessageSnackbar(response.message)
                setSeveritySnackbar("error")
            }
        }
    }
    const onCopyImageUrlListeners = () => {
        navigator.clipboard.writeText(image_url);
        setOpenSnackbar(true)
        setMessageSnackbar("URL gambar berhasil disalin.")
        setSeveritySnackbar("success")
    }
    const onCloseSnackbarListeners = (event, reason) => {
        if (reason === 'clickaway') { return; }
        setOpenSnackbar(false);
        setMessageSnackbar("")
        setSeveritySnackbar("")
    }

    // ====================================================================================
    // ========== RENDER SECTIONS =========================================================
    // ====================================================================================
    return(
        <>
            <div className="form-image">
                {title &&
                    <div className="title">{title}</div>
                }
                <div className="image" {...props}>
                    <img alt={alt}
                        src={image_url || Constants.image_default.empty}
                    />
                </div>
                <div className={`inputs ${error && "error"}`}>
                    <div className={"icon"}><i className="ph ph-link-simple"></i></div>
                    <input
                        placeholder={placeholder || "Pilih gambar..."}
                        value={image_url || ""}
                        disabled
                    />
                    <input
                        id={id}
                        type="file"
                        accept="image/jpeg, image/png, image/jpg"
                        onChange={(e) => {onUploadImageListeners(e)}}
                        style={{ display: "none" }}
                    />

                    {image_url && <Tooltip title="Salin URL Gambar">
                        <button className="copy" onClick={onCopyImageUrlListeners}>
                            <i className="ph ph-copy"></i>
                        </button>
                    </Tooltip>}
                    <label for={id}>
                        <i className="ph ph-magnifying-glass"></i><span>Pilih Gambar</span>
                    </label>
                </div>
                <div className="placeholder">
                    {optional && <span className="optional">Opsional</span>}
                    {!optional && <span className="required">Wajib</span>}
                    <span className="note">
                        {!error ?
                            <>Ukuran gambar: <b>1MB</b> Maksimal</>
                            : error
                        }
                    </span>
                </div>
            </div>
            <Loading ref={ref_loading}/>
            <Snackbar
                open={openSnackbar}
                autoHideDuration={3600}
                onClose={onCloseSnackbarListeners}
                sx={{ '&.MuiSnackbar-root': { top: '20%' } }}
                anchorOrigin={{ vertical: "top", horizontal: "center" }}
            >
                <Alert
                    severity={severitySnackbar}
                    onClose={() => {
                        onCloseSnackbarListeners
                    }}
                    variant="filled"
                    sx={{ width: "100%" }}>
                    {messageSnackbar}
                </Alert>
            </Snackbar>
        </>
    )
}

export default ImageUploadSingle