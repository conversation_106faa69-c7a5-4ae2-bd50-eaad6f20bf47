// const ROLES = {
//   Superuser: {
//     setting_access: {
//       view: true,
//       view_params: (data, returnData) => {
//         returnData.allowed = true;
//         returnData.data = {};
//         return returnData;
//       },
//       create: true,
//       update: true,
//       delete: true,
//     },
//   },
//   _other: {
//     setting_access: {
//       view: () => {},
//       // superuser tidak ditampilkan
//       view_params: (data, returnData) => {
//         returnData.allowed = true;
//         returnData.data = {
//           [`id[neq]`]: 1,
//         };
//         return returnData;
//       },
//       create: true,
//       update: true,
//       delete: true,
//     },
//   },
// };

const ROLES_ID = {
  1: {
    setting_access: {
      view: true,
      view_params: (data, returnData) => {
        returnData.allowed = true;
        returnData.data = {};
        return returnData;
      },
      create: true,
      update: true,
      delete: (data, returnData) => {
        if (data.id != 1) {
          returnData.allowed = true;
        }
        return returnData;
      },
    },
    setting_user: {
      view: true,
      // superuser tidak ditampilkan
      view_params: (data, returnData) => {
        returnData.allowed = true;
        return returnData;
      },
      create: true,
      update: true,
      delete: true,
    },
  },
  _other: {
    setting_access: {
      view: true,
      // superuser tidak ditampilkan
      view_params: (data, returnData) => {
        returnData.allowed = true;
        returnData.data = {
          [`id[neq]`]: 1,
        };
        return returnData;
      },
      create: true,
      update: true,
      delete: (data, returnData) => {
        if (data.id != 1) {
          returnData.allowed = true;
        }
        return returnData;
      },
    },
    setting_user: {
      view: true,
      // superuser tidak ditampilkan
      view_params: (data, returnData) => {
        returnData.allowed = true;
        returnData.data = {
          [`id[neq]`]: 1,
        };
        return returnData;
      },
      create: true,
      update: true,
      delete: true,
    },
  },
};

// export const hasPermission = (user, resource, action, data = {}) => {};
export const hasPermission = (role_id, resource, action, data = {}) => {
  let returnData = {
    allowed: false,
    data: null,
  };

  if (
    ROLES_ID[role_id] !== undefined &&
    ROLES_ID[role_id][resource] !== undefined &&
    ROLES_ID[role_id][resource][action] !== undefined
  ) {
    if (typeof ROLES_ID[role_id][resource][action] === "function") {
      returnData = ROLES_ID[role_id][resource][action](data, returnData);
    } else {
      returnData.allowed = ROLES_ID[role_id][resource][action];
    }
  }

  return returnData;
};
