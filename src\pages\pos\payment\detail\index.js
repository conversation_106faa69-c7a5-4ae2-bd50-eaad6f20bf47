import { useEffect, useRef } from "react";
import { useRouter } from "next/router";
import useTrackedPaymentDetailStore, {
  usePaymentDetailStore,
} from "@/store/kiosk/pos/payment/detail/store";
import usePersistKioskPosStore from "@/store/kiosk/pos/storePersist";
import styles from "@/styles/Kiosk.module.css";
import AuthWrapper from "@/components/wrapper/AuthWrapper";
import Constants from "@/utils/Constants";
import Loading from "@/components/modal/Loading";
import MySnackbar from "@/components/MySnackbar";
import ModalConfirmation from "@/components/modal/ModalConfirmation";
import CommonHelper from "@/utils/CommonHelper";
import MomentHelper from "@/utils/MomentHelper";
import Router from "next/router";
import { getUntrackedObject } from "react-tracked";
import { Alert, AlertTitle } from "@mui/material";
import Image from "next/image";
import PrintTransaction from "@/components/pages/PrintTransaction";

const KioskPaymentDetailPage = ({
  isAuthLoading,
  isLoggedIn,
  user,
  selected_aol,
  selected_branch,
}) => {
  const router = useRouter();
  const { objCustomer } = usePersistKioskPosStore();

  const ref_Loading = useRef(null);
  const ref_MySnackbar = useRef(null);
  const ref_ModalConfirmation = useRef(null);

  useEffect(() => {
    usePaymentDetailStore.getState().setState({
      ref_Loading: ref_Loading,
      ref_MySnackbar: ref_MySnackbar,
      ref_ModalConfirmation: ref_ModalConfirmation,
    });

    // clean up
    return () => {
      if (usePaymentDetailStore.getState().intervalRefresh) {
        clearInterval(usePaymentDetailStore.getState().intervalRefresh);
      }
    };
  }, []);

  useEffect(() => {
    if (ref_Loading.current) {
      ref_Loading.current.onShowDialog();
    }
  }, [ref_Loading.current]);

  // Redirect if no customer selected
  useEffect(() => {
    if (!isAuthLoading && isLoggedIn) {
      if (!objCustomer?.id) {
        router.push("/pos");
      }

      usePaymentDetailStore.getState().onInit();
    }
  }, [isAuthLoading, isLoggedIn, objCustomer]);

  if (!objCustomer) return null; // Already redirecting

  return (
    <>
      <div className={styles.ct_containers}>
        <div
          className={styles.contents}
          style={{ height: "100dvh", overflowY: "auto" }}
        >
          <RenderPaymentDetail />
        </div>
      </div>

      {/* Loading dialog */}
      <Loading ref={ref_Loading} />
      <MySnackbar ref={ref_MySnackbar} />
      <ModalConfirmation ref={ref_ModalConfirmation} />
    </>
  );
};

const RenderPaymentDetail = () => {
  const paymentDetail = usePaymentDetailStore((state) => state.paymentDetail);

  if (!paymentDetail) return null;

  const getStatusClass = (status) => {
    switch (status) {
      case "Menunggu Diproses":
        return styles.Menunggu_Diproses;
      case "Terbayar":
        return styles.Terbayar;
      case "Dibatalkan":
        return styles.Dibatalkan;
      default:
        return styles.default;
    }
  };

  return (
    <div className={styles.payment_detail}>
      {paymentDetail.status === "Menunggu Diproses" && (
        <Alert severity="info" className="mb-4">
          Kami sedang menyelesaikan pesanan Anda. Cara pembayaran akan segera
          muncul di halaman ini. <b>Mohon jangan tutup halaman ini.</b>
        </Alert>
      )}

      {/* Header Section */}
      <div className={styles.section}>
        <div className={styles.header}>
          <div className={styles.left}>
            {/* back button */}
            <a
              className={styles.back_button}
              onClick={() => {
                Router.replace({
                  pathname: "/pos",
                  query: { from: "payment_detail" },
                });
              }}
            >
              <i className="ph ph-arrow-left"></i>
            </a>
            <div className={styles.title}>
              <h2>
                Detail{" "}
                {paymentDetail.status === "Menunggu Diproses"
                  ? "Pesanan"
                  : "Pembayaran"}
              </h2>
              <p>Informasi transaksi</p>
            </div>
          </div>
          <div
            className={`${styles.status} ${getStatusClass(
              paymentDetail.status
            )}`}
            style={{ textTransform: "capitalize" }}
          >
            {paymentDetail.status}
          </div>
        </div>

        <div className={styles.info_grid}>
          <div className={styles.info_item}>
            <label>Nomor Transaksi</label>
            <div className={styles.value}>{paymentDetail.number}</div>
          </div>
          <div className={styles.info_item}>
            <label>Tanggal Transaksi</label>
            <div className={styles.value}>
              {MomentHelper.format(
                paymentDetail.input_datetime,
                MomentHelper.datetimeFormatReadable2
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Customer Information */}
      <div className={styles.section}>
        <div className={styles.customer_info}>
          <img
            src={
              paymentDetail.customer_image_url || Constants.image_default.empty
            }
            alt={paymentDetail.customer_name}
            className={styles.avatar}
          />
          <div className={styles.details}>
            <div className={styles.name}>{paymentDetail.customer_name}</div>
            <div className={styles.code}>{paymentDetail.customer_code}</div>
          </div>
        </div>
      </div>

      {/* Affiliate Information (if exists) */}
      {paymentDetail.affiliate_name && (
        <div
          style={{
            backgroundColor: "#fff",
            border: "1px solid #ececec",
            borderRadius: "8px",
            padding: "1.5rem",
            marginBottom: "1rem",
            boxShadow:
              "-8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(222 222 222 / 25%)",
          }}
        >
          <h3
            style={{
              margin: "0 0 1rem 0",
              fontSize: "1.2rem",
              fontWeight: "600",
              color: "#2f3640",
            }}
          >
            Informasi Affiliate
          </h3>
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "1fr 1fr",
              gap: "1rem",
            }}
          >
            <div>
              <label
                style={{
                  fontSize: "0.8rem",
                  color: "#808080",
                  textTransform: "uppercase",
                  fontWeight: "600",
                }}
              >
                Nama Affiliate
              </label>
              <div
                style={{
                  fontSize: "1rem",
                  fontWeight: "600",
                  color: "#2f3640",
                  marginTop: "0.25rem",
                }}
              >
                {paymentDetail.affiliate_name}
              </div>
            </div>
            <div>
              <label
                style={{
                  fontSize: "0.8rem",
                  color: "#808080",
                  textTransform: "uppercase",
                  fontWeight: "600",
                }}
              >
                Kode Affiliate
              </label>
              <div
                style={{
                  fontSize: "1rem",
                  color: "#2f3640",
                  marginTop: "0.25rem",
                }}
              >
                {paymentDetail.affiliate_code || "-"}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Product Summary */}
      <RenderPaymentProduct />

      <RenderPaymentVoucher />

      {/* Financial Summary */}
      <RenderPaymentSummary />
      <RenderPaymentMethod />
      <RenderPaymentInfo />
    </div>
  );
};

const RenderPaymentProduct = () => {
  const paymentDetail = usePaymentDetailStore((state) => state.paymentDetail);

  if (!paymentDetail?.product_array?.length) return null;

  return (
    <div className={styles.product_section}>
      <h3 className={styles.title}>Produk</h3>
      <div className={styles.items}>
        {paymentDetail.product_array.map((item, index) => (
          <RenderPaymentProductItem key={index} item={item} />
        ))}
      </div>
    </div>
  );
};

const RenderPaymentProductItem = ({ item }) => {
  return (
    <div className={styles.item}>
      <div className={styles.product_image}>
        <img
          src={item.image_url || Constants.image_default.empty}
          alt={item.name}
          className={styles.image}
        />
      </div>
      <div className={styles.product_info}>
        <div className={styles.product_header}>
          <div className={styles.name}>{item.name}</div>
          <div className={styles.code}>{item.code}</div>
        </div>
        <div className={styles.product_details}>
          <div className={styles.quantity_price}>
            <div className={styles.quantity}>
              {CommonHelper.formatNumber(item.quantity)} x{" "}
              {CommonHelper.formatNumber(parseFloat(item.selling_price), "idr")}
            </div>
            {parseFloat(item.discount_nominal || 0) > 0 && (
              <div className={styles.discount}>
                <i className="ph ph-tag"></i>
                {CommonHelper.formatNumber(
                  parseFloat(item.discount_nominal),
                  "idr"
                )}
              </div>
            )}
          </div>
          <div className={styles.total_price}>
            {CommonHelper.formatNumber(
              parseFloat(item.total_selling_price),
              "idr"
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

const RenderPaymentVoucher = () => {
  const paymentDetail = usePaymentDetailStore((state) => state.paymentDetail);

  if (!paymentDetail) return null;

  if (!paymentDetail.voucher_name) return null;

  return (
    <div className={styles.payment_detail}>
      <h3 className={styles.title}>Voucher</h3>
      <div className={styles.voucher_info}>
        <div className={styles.icon}>
          <img
            alt={paymentDetail.voucher_name}
            src={
              paymentDetail.voucher_image_url || Constants.image_default.empty
            }
          />
        </div>
        <div className={styles.details}>
          <div className={styles.name}>{paymentDetail.voucher_name}</div>
          <div className={styles.code}>{paymentDetail.voucher_code}</div>
        </div>
        <div className={styles.summary}>
          <div className={styles.count}>1 Voucher</div>
          <div className={styles.value}>
            {CommonHelper.formatNumber(
              parseFloat(paymentDetail.voucher_nominal),
              "idr"
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

const RenderPaymentSummary = () => {
  const paymentDetail = usePaymentDetailStore((state) => state.paymentDetail);

  if (!paymentDetail) return null;

  return (
    <div className={styles.summary_section}>
      <h3 className={styles.title}>Ringkasan Pesanan</h3>
      <div className={styles.grid}>
        <div className={styles.item}>
          <label>Total Item</label>
          <div className={styles.value}>
            {paymentDetail.total_product_item} Item
          </div>
        </div>
        <div className={styles.item}>
          <label>Subtotal</label>
          <div className={styles.value}>
            {CommonHelper.formatNumber(
              parseFloat(paymentDetail.total_product_selling_price),
              "idr"
            )}
          </div>
        </div>
        <div className={styles.item}>
          <label>Diskon</label>
          <div className={`${styles.value} text-green`}>
            {CommonHelper.formatNumber(
              parseFloat(paymentDetail.total_discount_nominal),
              "idr"
            )}
          </div>
        </div>
        {/* <div className={styles.item}>
          <label>Pajak</label>
          <div className={`${styles.value} ${styles.tax}`}>
            {CommonHelper.formatNumber(
              parseFloat(paymentDetail.total_tax),
              "idr"
            )}
          </div>
        </div> */}
        <div className={styles.item}>
          <label>Total Transaksi</label>
          <div
            className={`${styles.value}`}
            style={{ color: "var(--base-color)" }}
          >
            {CommonHelper.formatNumber(
              parseFloat(paymentDetail.total_product_selling_price) -
                parseFloat(paymentDetail.total_discount_nominal),
              "idr"
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

const RenderPaymentInfo = () => {
  const paymentDetail = usePaymentDetailStore((state) => state.paymentDetail);
  const {} = useTrackedPaymentDetailStore();

  let allowedStatus = ["Menunggu pembayaran", "Terbayar"];
  if (!paymentDetail) return null;
  if (!allowedStatus.includes(paymentDetail.status)) {
    return null;
  }

  const isExistPaymentMethod = paymentDetail.kiosk_payment_method_id;
  if (!isExistPaymentMethod) {
    return null;
  }

  let title = "";
  let subtitle = "";
  if (
    paymentDetail.kiosk_payment_method_type === "other" &&
    paymentDetail.kiosk_payment_method_code === "cashier"
  ) {
    title = "Pembayaran di Kasir";
    if (paymentDetail.status === "Menunggu pembayaran") {
      subtitle = "Silahkan lakukan pembayaran di kasir";
    }
  } else if (paymentDetail.kiosk_payment_method_type === "midtrans") {
    let paymentCode = paymentDetail.kiosk_payment_method_code;
    let paymentLabel = "";
    if (paymentCode === "bank_transfer") {
      paymentLabel =
        "Transfer Bank " + paymentDetail.kiosk_payment_method_label;
    } else {
      paymentLabel = paymentDetail.kiosk_payment_method_label;
    }

    title = `Pembayaran dengan ${paymentLabel}`;
  }

  return (
    <div className={styles.section} style={{}}>
      <div className={"flex flex-row items-center gap-2"}>
        <Image
          src={
            paymentDetail.kiosk_payment_method_image_url ||
            Constants.image_default.empty
          }
          alt={paymentDetail.kiosk_payment_method_name}
          width={48}
          height={48}
          style={{
            objectFit: "contain",
          }}
        />
        <div>
          <h3 className={styles.title} style={{ marginBottom: 0 }}>
            {title}
          </h3>
          <p>{subtitle}</p>
        </div>
      </div>

      <RenderPaymentInfoMidtrans />

      <div className={`${styles.payments} mt-4`}>
        <div className={styles.summaries}>
          <>
            {/* <div className={styles.nominals}>
              <label>Total Transaksi</label>
              <input
                disabled
                value={CommonHelper.formatNumber(
                  inputs.nominal_transaction,
                  "idr"
                )}
              />
            </div> */}
            {Number(paymentDetail.kiosk_mdr) > 0 && (
              <div className={styles.nominals}>
                <label className="text-orange">MDR</label>
                <input
                  className="text-orange"
                  disabled
                  value={CommonHelper.formatNumber(
                    Number(paymentDetail.kiosk_mdr) +
                      Number(paymentDetail.kiosk_service_fee),
                    "idr"
                  )}
                />
              </div>
            )}
            <div className={styles.nominals}>
              <label style={{ color: "var(--base-color)" }}>Total Bayar</label>
              <input
                style={{ color: "var(--base-color)" }}
                disabled
                value={CommonHelper.formatNumber(
                  Number(paymentDetail.grand_total) +
                    Number(paymentDetail.kiosk_mdr) +
                    Number(paymentDetail.kiosk_service_fee),
                  "idr"
                )}
              />
            </div>
          </>
          {/* <div className={styles.nominals}>
        <label>Est. Poin Didapat</label>
        <input disabled value={`${CommonHelper.formatNumber(27750)} Poin`} />
      </div> */}
        </div>
        <div className={styles.payment_action}>
          <PrintTransaction
            data={paymentDetail}
            showPreview={false}
            button={
              <button className="button info">
                <i className="ph ph-bold ph-printer"></i>
                <span>Cetak Struk</span>
              </button>
            }
          />
          {paymentDetail.status === "Menunggu pembayaran" && paymentDetail.midtrans_expired_bool && (
            <button
              className="button"
              onClick={() => {
                usePaymentDetailStore.getState().onSubmitPayment(true);
              }}
            >
              <i
                className="ph ph-bold ph-arrow-counter-clockwise"
                style={{ marginRight: "0.5rem" }}
              ></i>
              <span>Permintaan Pembayaran Ulang</span>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

const RenderPaymentInfoMidtrans = () => {
  const paymentDetail = usePaymentDetailStore((state) => state.paymentDetail);

  if (!paymentDetail) return null;

  if (paymentDetail.kiosk_payment_method_type !== "midtrans") {
    return null;
  }

  if (paymentDetail.status === "Terbayar") {
    return null;
  }

  const paymentObject = paymentDetail.kiosk_payment_method_object;
  if (!paymentObject) return null;

  if (paymentDetail.midtrans_expired_bool) {
    return (
      <Alert severity="warning" className="mt-4">
        Batas waktu pembayaran telah berakhir. Silahkan minta pembayaran ulang.
      </Alert>
    );
  }

  return (
    <div className={styles.payment_info_midtrans}>
      {/* Payment Status */}
      <div className={styles.payment_status}>
        <div className={styles.status_label}>{paymentObject.status_label}</div>
        {paymentObject.time_until_expired > 0 && (
          <div className={styles.expiry_info}>
            <i className="ph ph-clock"></i>
            <span>
              Batas waktu pembayaran:{" "}
              {MomentHelper.format(
                paymentObject.expired_datetime,
                MomentHelper.datetimeFormatReadable2
              )}{" "}
              WIB
            </span>
          </div>
        )}
      </div>

      {/* VA Number for Bank Transfer */}
      {paymentObject.payment_type === "bank_transfer" &&
        paymentObject.bank_transfer && (
          <div className={styles.va_info}>
            <div className={styles.va_number}>
              <label>Nomor Virtual Account</label>
              <div className={styles.number}>
                {paymentObject.bank_transfer.va_number}
                {/* <button 
                className={styles.copy_button}
                onClick={() => {
                  navigator.clipboard.writeText(paymentObject.bank_transfer.va_number);
                }}
              >
                <i className="ph ph-copy"></i>
              </button> */}
              </div>
            </div>
          </div>
        )}

      {/* Payment Instructions */}
      {paymentObject.instruction_array &&
        paymentObject.instruction_array.length > 0 && (
          <div className={styles.instructions}>
            {paymentObject.instruction_array.map(
              (instruction, index) =>
                instruction.show && (
                  <div key={index} className={styles.instruction_group}>
                    <h4>{instruction.label}</h4>
                    <ol>
                      {instruction.value.map((step, stepIndex) => (
                        <li key={stepIndex}>{step}</li>
                      ))}
                    </ol>
                  </div>
                )
            )}
          </div>
        )}

      {/* QR */}
      {paymentObject.qr_url && (
        <div
          className={styles.qr_section}
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            gap: "1rem",
            padding: "1.5rem",
            backgroundColor: "#fff",
            borderRadius: "8px",
            boxShadow:
              "-8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(222 222 222 / 25%)",
            marginTop: "1rem",
          }}
        >
          <div
            className={styles.qr_container}
            style={{
              width: "200px",
              height: "200px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              backgroundColor: "#fff",
              padding: "1rem",
              borderRadius: "8px",
              border: "1px solid #ececec",
            }}
          >
            <Image
              src={paymentObject.qr_url}
              alt="QR Code Pembayaran"
              width={200}
              height={200}
              className={styles.qr_image}
              style={{
                objectFit: "contain",
              }}
            />
          </div>
          <p
            className={styles.qr_instruction}
            style={{
              margin: 0,
              fontSize: "0.9rem",
              color: "#666",
              textAlign: "center",
            }}
          >
            Scan QR code di atas untuk melakukan pembayaran
          </p>
        </div>
      )}
    </div>
  );
};

const RenderPaymentMethod = () => {
  const paymentDetail = usePaymentDetailStore((state) => state.paymentDetail);
  const { inputs, onSelectPaymentMethod, isChangePaymentMethod } =
    useTrackedPaymentDetailStore();

  if (!paymentDetail) return null;
  if (paymentDetail.status !== "Menunggu pembayaran") {
    return null;
  }

  if (!isChangePaymentMethod) {
    const isExistPaymentMethod = paymentDetail.kiosk_payment_method_id;
    if (isExistPaymentMethod) {
      return null;
    }
  }

  return (
    <div className={styles.section}>
      <h3 className={styles.title}>Pilih Cara Bayar</h3>
      <div className={styles.payment_methods}>
        <div className={styles.items}>
          {paymentDetail.payment_method_array.map((item, index) => {
            return (
              <div
                key={index}
                className={`${styles.item} ${
                  inputs.selectedPaymentMethod?.id === item.id && styles.active
                }`}
                onClick={() => {
                  onSelectPaymentMethod(item);
                }}
              >
                <img
                  alt={item.label}
                  src={item.image_url || Constants.image_default.empty}
                />
                <div className={styles.title}>{item.label}</div>
              </div>
            );
          })}
        </div>
      </div>
      <RenderPaymentActions />
    </div>
  );
};

const RenderPaymentActions = () => {
  const { paymentDetail, inputs, onValidateListeners } =
    useTrackedPaymentDetailStore();

  if (!paymentDetail) return null;
  const isExistPaymentMethod = paymentDetail.kiosk_payment_method_id;

  return (
    <>
      <div className={styles.payments}>
        <div className={styles.summaries}>
          <>
            {/* <div className={styles.nominals}>
              <label>Total Transaksi</label>
              <input
                disabled
                value={CommonHelper.formatNumber(
                  inputs.nominal_transaction,
                  "idr"
                )}
              />
            </div> */}
            {inputs.payment_mdr > 0 && (
              <div className={styles.nominals}>
                <label className="text-orange">MDR</label>
                <input
                  className="text-orange"
                  disabled
                  value={CommonHelper.formatNumber(inputs.payment_mdr, "idr")}
                />
              </div>
            )}
            <div className={styles.nominals}>
              <label style={{ color: "var(--base-color)" }}>Total Bayar</label>
              <input
                style={{ color: "var(--base-color)" }}
                disabled
                value={CommonHelper.formatNumber(inputs.payment_total, "idr")}
              />
            </div>
          </>
          {/* <div className={styles.nominals}>
        <label>Est. Poin Didapat</label>
        <input disabled value={`${CommonHelper.formatNumber(27750)} Poin`} />
      </div> */}
        </div>

        <div className={styles.payment_action}>
          <button
            className="button"
            onClick={() => {
              onValidateListeners();
            }}
          >
            <i className="ph ph-bold ph-check-circle"></i>
            <span>Proses Pembayaran</span>
          </button>
        </div>
      </div>
    </>
  );
};

export default AuthWrapper(KioskPaymentDetailPage, {
  redirectTo: Constants.webUrl.login,
});
