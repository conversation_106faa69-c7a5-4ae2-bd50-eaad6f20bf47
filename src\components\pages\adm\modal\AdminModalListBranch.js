/*
Created by esoda
Created on Mei, 2024
Contact esoda.id
*/

import React from "react";
import { Modal, Box, Checkbox, Chip } from "@mui/material";
import Loading from "@/components/modal/Loading";
import Table from "@/components/libs/Table";
import ApiHelper from "@/utils/ApiHelper";

export default class AdminModalListBranch extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showDialog: false,
      formType: "add",
      formData: null,
      formIndex: -1,

      // Table data and selection state
      dataTables: [],
      selectedItems: {},
      selectAll: false,
      isFetched: false,
      pageCount: 1,
      pageSelected: 1,
      pageRow: 10,
      inputSearch: "",
      inputSort: "",
    };
  }

  // ====================================================================================
  // ========== INITIALIZE, GET DATA ====================================================
  // ====================================================================================
  onShowDialog = (formType, formData = null, formIndex = -1) => {
    let selectedItems = {};
    if (formData?.selected) {
      formData.selected.map((item) => {
        selectedItems[item.id] = item;
      });
    }

    this.setState(
      {
        formType,
        formData,
        formIndex,
        showDialog: true,
        selectedItems,
        selectAll: false,
      },
      () => {
        if (formType === "list") {
          this.onRefresh();
        }
      }
    );
  };

  onCloseDialog = () => {
    this.setState({
      showDialog: false,
      formType: "add",
      formData: null,
      formIndex: -1,
      selectedItems: {},
      selectAll: false,
    });
  };

  getTableHeadings = () => {
    return [
      {
        key: "checkbox",
        label: "",
        className: "text-center wd40",
        sortable: false,
        sort: "",
      },
      {
        key: "name",
        label: "Nama Cabang",
        className: "",
        sortable: true,
        sort: "",
      },
      {
        key: "aol_session_name",
        label: "Db Accurate",
        className: "",
        sortable: true,
        sort: "",
      },
      {
        key: "status",
        label: "Status",
        className: "text-center wd100",
        sortable: true,
        sort: "",
      },
    ];
  };

  // ====================================================================================
  // ========== ACTION LISTENERS ========================================================
  // ====================================================================================
  // Table and selection handlers
  onSelectItem = (item) => {
    let selectedItems = structuredClone(this.state.selectedItems);
    if (selectedItems[item.id]) {
      delete selectedItems[item.id];
    } else {
      selectedItems[item.id] = item;
    }

    // Check if all items on the current page are selected
    const allSelected = this.state.dataTables.every(
      (item) => selectedItems[item.id]
    );

    this.setState({ selectedItems, selectAll: allSelected });
  };

  onSelectAll = () => {
    if (this.state.isFetched) {
      return;
    }

    let selectedItems = structuredClone(this.state.selectedItems);
    let dataTables = structuredClone(this.state.dataTables);

    let selectAll = !this.state.selectAll;
    // map datatable
    dataTables.map((item) => {
      if (selectAll) {
        if (selectedItems[item.id] === undefined) {
          selectedItems[item.id] = item;
        }
      } else {
        if (selectedItems[item.id] !== undefined) {
          delete selectedItems[item.id];
        }
      }
    });

    this.setState({ selectedItems, selectAll });
  };

  // Table pagination handlers
  onPageChangeListeners = (page) => {
    this.setState({ pageSelected: page }, () => this.onFetchData(true));
  };

  onPageRowChangeListeners = (pageRow) => {
    this.setState({ pageRow, pageSelected: 1 }, () => this.onFetchData(true));
  };

  onSearchListeners = (inputSearch) => {
    this.ref_Table.setSearchValue(inputSearch);
    this.setState({ inputSearch }, this.onRefresh);
  };

  onSortListeners = (inputSort) => {
    this.setState({ inputSort }, () => this.onFetchData(true));
  };

  onRefresh = () => {
    this.setState(
      {
        isFetched: true,
        dataTables: [],
        pageCount: 1,
        pageSelected: 1,
        pageRow: 10,
      },
      this.onFetchData
    );
  };

  onFetchData = async (isPageClicked = false) => {
    if (isPageClicked) {
      this.setState({ isFetched: true });
    }

    // api
    let apiEndpoint = "kiosk/admin/branch/options";
    let params = {
      limit: this.state.pageRow,
      page: this.state.pageSelected,
      search: this.state.inputSearch,
      sort: this.state.inputSort,
      pagination_bool: true,
    };

    if (this.state.formData?.apiEndpoint) {
      apiEndpoint = this.state.formData.apiEndpoint;
    }
    if (this.state.formData?.apiParams) {
      params = { ...params, ...this.state.formData.apiParams };
    }

    let response = await ApiHelper.get(apiEndpoint, params);
    if (response.status === 200) {
      let selectAll = true;
      let dataTables = response.results.data;
      for (let i = 0; i < dataTables.length; i++) {
        const element = dataTables[i];
        if (!this.state.selectedItems[element.id]) {
          selectAll = false;
          break;
        }
      }
      this.setState({
        isFetched: false,
        dataTables: response.results.data,
        pageCount: response.results.pagination.total_page,
        selectAll,
      });
    } else {
      this.setState({
        isFetched: false,
        dataTables: [],
        pageCount: 1,
        selectAll: false,
      });
    }
  };

  onSelectListeners = () => {
    // Get selected branches
    const selectedBranches = [];
    for (const key in this.state.selectedItems) {
      if (Object.hasOwnProperty.call(this.state.selectedItems, key)) {
        const element = this.state.selectedItems[key];
        selectedBranches.push(element);
      }
    }

    const { onSelect } = this.props;
    if (onSelect) {
      onSelect(selectedBranches);
    }
    this.onCloseDialog();
  };

  // ====================================================================================
  // ========== RENDER SECTIONS =========================================================
  // ====================================================================================
  render() {
    let addModalClass = "medium";
    if (
      this.state.formType === "delete" ||
      this.state.formType === "activated"
    ) {
      addModalClass = "dialog";
    }
    return (
      <>
        <Modal open={this.state.showDialog} onClose={this.onCloseDialog}>
          <Box
            sx={{
              flex: 1,
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              display: "flex",
              width: "100wh",
              height: "100vh",
            }}
          >
            <div className={`modal-content ${addModalClass}`}>
              <div class="modal-header">{this.renderHeader()}</div>
              <div class="modal-body">{this.renderBody()}</div>
              <div class="modal-footer flex-wrap">
                {this.renderFooter()}
                <button
                  className="button cancel"
                  onClick={() => this.onCloseDialog()}
                >
                  <i class="ph ph-bold ph-x-circle"></i>
                  <span>
                    {this.state.formType === "info" ? "Tutup" : "Batal"}
                  </span>
                </button>
              </div>
            </div>
          </Box>
        </Modal>

        {/* Loading dialog */}
        <Loading ref={(value) => (this.ref_Loading = value)} />
      </>
    );
  }

  renderHeader() {
    return (
      <>
        <div class="title">
          {this.state.formType === "add" && `Tambah Data REPLACE_HERE`}
          {this.state.formType === "info" && `Detail Data REPLACE_HERE`}
          {this.state.formType === "edit" && `Ubah Data REPLACE_HERE`}
          {this.state.formType === "delete" && `Hapus Data REPLACE_HERE`}
          {this.state.formType === "activated" &&
            `Aktif/Non-Aktif Data REPLACE_HERE`}
          {this.state.formType === "list" && `Pilih Cabang`}
        </div>
        <span class="close" onClick={() => this.onCloseDialog()}>
          &times;
        </span>
      </>
    );
  }

  renderBody() {
    return <>{this.state.formType === "list" && this.renderTableList()}</>;
  }

  renderTableList() {
    return (
      <div style={{ height: "100%" }}>
        <Table
          ref={(value) => (this.ref_Table = value)}
          title="Daftar Cabang"
          subtitle="Pilih cabang yang akan digunakan"
          disabledAddBtn
          disabledBtnFilter
          disabledBtnExport
          dataHeadings={this.getTableHeadings()}
          dataTables={this.state.dataTables}
          renderItems={(item, index) => this.renderTableItems(item, index)}
          pageCount={this.state.pageCount}
          pageSelected={this.state.pageSelected}
          pageListeners={this.onPageChangeListeners}
          pageRowListeners={this.onPageRowChangeListeners}
          onSearch={this.onSearchListeners}
          inputSearch={this.state.inputSearch}
          searchListeners={this.onSearchListeners}
          sortListeners={this.onSortListeners}
          onReload={() => this.onFetchData(true)}
          isFetched={this.state.isFetched}
        />
      </div>
    );
  }

  renderTableItems(item, index) {
    const isSelected = !!this.state.selectedItems[item.id];

    return (
      <tr
        key={index}
        style={{ opacity: !item.suspended_bool ? 1 : 0.4, cursor: "pointer" }}
        onClick={() => this.onSelectItem(item)}
      >
        <td className="text-center">
          <Checkbox checked={isSelected} color="primary" size="small" />
        </td>
        <td>{item.name}</td>
        <td>{item.aol_session_name}</td>
        <td className="text-center">
          <Chip
            label={item.suspended_bool ? "Tidak Aktif" : "Aktif"}
            color={item.suspended_bool ? "error" : "success"}
          />
        </td>
      </tr>
    );
  }

  renderForm() {
    // This is a placeholder for the form implementation
    // The actual form implementation would go here when needed
    return null;
  }

  renderFooter() {
    const selectedCount = Object.keys(this.state.selectedItems).length;

    return (
      <>
        {this.state.formType !== "list" && (
          <button
            className={`button ${this.state.formType === "edit" && "warning"} ${
              this.state.formType === "delete" && "danger"
            }`}
            onClick={() => {
              this.onValidateListeners();
            }}
          >
            {this.state.formType === "add" && (
              <i class="ph ph-bold ph-check-circle"></i>
            )}
            {this.state.formType === "edit" && (
              <i class="ph ph-bold ph-pencil"></i>
            )}
            {this.state.formType === "delete" && (
              <i class="ph ph-bold ph-trash-simple"></i>
            )}
            {this.state.formType === "activated" && (
              <i class="ph ph-bold ph-check-circle"></i>
            )}
            <span>
              {this.state.formType === "add" && "Tambah Data"}
              {this.state.formType === "edit" && "Ubah Data"}
              {this.state.formType === "delete" && "Hapus Data"}
              {this.state.formType === "activated" && "Aktif/Non-aktif"}
            </span>
          </button>
        )}

        {this.state.formType === "list" && (
          <>
            <div
              style={{ marginRight: "auto", fontSize: "0.9rem", color: "#666" }}
              className="hidden sm:hidden md:flex"
            >
              {selectedCount > 0
                ? `${selectedCount} cabang terpilih`
                : "Belum ada cabang yang dipilih"}
            </div>
            <div
              style={{ marginRight: "auto", fontSize: "0.9rem", color: "#666" }}
              className="flex sm:flex md:hidden gap-2 justify-center items-center"
            >
              <i class="ph ph-bold ph-check-square"></i>
              <span>{selectedCount}</span>
            </div>
            <button
              className={`button responsive ${
                this.state.isFetched ? "disabled_opacity" : ""
              }`}
              style={{ marginLeft: "0.5rem" }}
              onClick={this.onSelectAll}
            >
              <i
                class={`ph ph-bold ${
                  this.state.selectAll ? "ph-checks" : "ph-check-square"
                }`}
              ></i>
              <span>
                {this.state.selectAll ? "Batalkan Semua" : "Pilih Semua"}
              </span>
            </button>
            <button
              className="button responsive"
              disabled={selectedCount === 0}
              onClick={() => {
                this.onSelectListeners();
              }}
            >
              <i class="ph ph-bold ph-check-circle"></i>
              <span>Pilih Cabang</span>
            </button>
          </>
        )}
      </>
    );
  }
}
