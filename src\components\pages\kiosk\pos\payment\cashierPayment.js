/*
Created by esoda
Created on Nov, 2024
Contact esoda.id
*/

import React from "react";
import { Modal, Box } from "@mui/material";
import Loading from "@/components/modal/Loading";
import moment from "moment";
import CommonHelper from "@/utils/CommonHelper";
import Constants from "@/utils/Constants";

export default class TransactionSalesorderCashierPayment extends React.Component {
    constructor(props) {
        super(props)
        this.state = { 
            showDialog: false,
            formType: "payment",
            formData: null,

            arrPaymentMethods: [
                {"id": 1, "name": "Tunai", "image_url": "https://www.transparentpng.com/thumb/money/kxYuge-money-png-nowskills-apprenticeships.png"},
                {"id": 2, "name": "QRIS", "image_url": "https://developers.bri.co.id/sites/default/files/2023-02/qris-mpm-dinamis.png"},
                {"id": 3, "name": "EDC", "image_url": "https://cdn-icons-png.flaticon.com/256/2258/2258439.png"},
                {"id": 4, "name": "Cek / Giro", "image_url": "https://bri.co.id/documents/20123/57176/Giro+Valas.png/075d9b9e-4a3a-a5c9-7db1-9609a3cdfcd3?t=*************"},
                {"id": 5, "name": "Transfer Bank", "image_url": "https://img.pikbest.com/png-images/********/payment-bank-transfer-icon-illustration_10612469.png!sw800"},
                {"id": 6, "name": "Payment Link", "image_url": "https://cdn2.iconfinder.com/data/icons/untact-brepigy/128/ic_cards-512.png"},
                {"id": 7, "name": "Virtual Account", "image_url": "https://developers.bri.co.id/sites/default/files/2023-08/briva_0.png"},
                {"id": 8, "name": "Lainnya", "image_url": "https://static.vecteezy.com/system/resources/thumbnails/009/315/289/small/3d-credit-card-money-financial-security-for-online-shopping-online-payment-credit-card-with-payment-protection-concept-3d-render-for-business-finance-shopping-with-mobile-security-concept-free-png.png"},
            ],

            arrNominal: [5000, 10000, 20000, 50000, 100000],

            inputs: {
                "nominal_transaction": ********,
                "payment_type_id": 1,
                "payment_type_name": "Tunai",
                "payment_mdr": 0,
                "payment_total": ********, // nominal_transaction + payment_mdr
                "payment_nominal_input": 0, // Nilai yang dibayar
                "payment_nominal_return": 0, // Kembalian dari nilai yang dibayar
            },
            errors: {},
        }
    }
    // ====================================================================================
    // ========== INITIALIZE, GET DATA ====================================================
    // ====================================================================================
    onShowDialog = (formType, formData=null) => { 
        this.setState({ 
            formType,
            formData,
            showDialog:true,
        }, this.getPaymentMethods)
        setTimeout(() => {
            var inputNominal = document.getElementById("input-nominal")
            if(inputNominal) { inputNominal.focus() }
        }, 250)

        console.log(formType)
    }
    onCloseDialog = () => { 
        this.setState({ 
            showDialog: false,
            formType: "payment",
            formData: null,

            // arrPaymentMethods: [],

            // inputs: {
            //     "nominal_transaction": 0,
            //     "payment_type_id": 1,
            //     "payment_type_name": "Tunai",
            //     "payment_mdr": 0,
            //     "payment_total": 0, // nominal_transaction + payment_mdr
            //     "payment_nominal_input": 0, // Nilai yang dibayar
            //     "payment_nominal_return": 0, // Kembalian dari nilai yang dibayar
            // },
            // errors: {},
        }) 
    }
    getPaymentMethods = () => {
        let arrPaymentMethods = [
            {"id": 1, "name": "Tunai", "image_url": "https://www.transparentpng.com/thumb/money/kxYuge-money-png-nowskills-apprenticeships.png"},
            {"id": 2, "name": "QRIS", "image_url": "https://developers.bri.co.id/sites/default/files/2023-02/qris-mpm-dinamis.png"},
            {"id": 3, "name": "EDC", "image_url": "https://cdn-icons-png.flaticon.com/256/2258/2258439.png"},
            {"id": 4, "name": "Cek / Giro", "image_url": "https://bri.co.id/documents/20123/57176/Giro+Valas.png/075d9b9e-4a3a-a5c9-7db1-9609a3cdfcd3?t=*************"},
            {"id": 5, "name": "Transfer Bank", "image_url": "https://img.pikbest.com/png-images/********/payment-bank-transfer-icon-illustration_10612469.png!sw800"},
            {"id": 6, "name": "Payment Link", "image_url": "https://cdn2.iconfinder.com/data/icons/untact-brepigy/128/ic_cards-512.png"},
            {"id": 7, "name": "Virtual Account", "image_url": "https://developers.bri.co.id/sites/default/files/2023-08/briva_0.png"},
            {"id": 8, "name": "Lainnya", "image_url": "https://static.vecteezy.com/system/resources/thumbnails/009/315/289/small/3d-credit-card-money-financial-security-for-online-shopping-online-payment-credit-card-with-payment-protection-concept-3d-render-for-business-finance-shopping-with-mobile-security-concept-free-png.png"},
        ]
        this.setState({ arrPaymentMethods })
    }

    // ====================================================================================
    // ========== ACTION LISTENERS ========================================================
    // ====================================================================================
    onTextInputListeners = (text, input) => {
        let inputs = this.state.inputs
        inputs[input] = text
        this.setState(prevState => ({ ...prevState, inputs }))
    }
    onTextErrorListeners = (error, input) => {
        let errors = this.state.errors
        errors[input] = error
        this.setState(prevState => ({ ...prevState, errors }))
    }
    onCalculateGrandTotal = () => {
        let inputs = this.state.inputs
        inputs.payment_total = Number(inputs.nominal_transaction) + Number(inputs.payment_mdr)
        this.setState({ inputs })
    }
    onValidateListeners = () => {
        this.ref_Loading.onShowDialog()
        setTimeout(() => {
            this.ref_Loading.onCloseDialog()
            this.setState({ formType:"results" })
        }, 500);
    }

    // ====================================================================================
    // ========== RENDER SECTIONS =========================================================
    // ====================================================================================
    render() {
        let addModalClass = "medium sx"
        if(this.state.formType === "results") {
            addModalClass = "dialog sx"
        }
        if(this.state.formType === "kiosk") {
            addModalClass = "dialog sm"
        }
        return(
            <>
            <Modal open={this.state.showDialog} onClose={this.onCloseDialog}>
                <Box sx={{
                    flex: 1,
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                    display: 'flex',
                    width: '100wh',
                    height: '100vh',
                    }}>
                    <div className={`modal-content ${addModalClass}`}>
                        {this.state.formType === "payment" && <div class="modal-header">
                            <div class="title">Proses Pembayaran</div>
                            <span class="close" onClick={() => this.onCloseDialog()}>&times;</span>
                        </div>}
                        <div class="modal-body">
                            {this.state.formType === "payment" && <div className="flex-rows no-right">
                                <div class="row no-border">
                                    {this.renderPaymentMethods()}
                                </div>
                                <div class="row no-border">
                                    {this.renderPaymentSummary()}
                                </div>
                            </div>}
                            {(this.state.formType === "results" || this.state.formType === "kiosk") && this.renderPaymentResults()}
                        </div>
                        {this.state.formType === "payment" && <div class="modal-footer">
                            <button className="button" onClick={() => { this.onValidateListeners() }}>
                                <i class="ph ph-bold ph-check-circle"></i>
                                <span>Proses Pembayaran</span>
                            </button>
                            <button className="button cancel" onClick={() => this.onCloseDialog()}>
                                <i class="ph ph-bold ph-x-circle"></i>
                                <span>Batal</span>
                            </button>
                        </div>}
                    </div>
                </Box>
            </Modal>

            {/* Loading dialog */}
            <Loading ref={(value) => this.ref_Loading = value} />
            </>
        )
    }
    renderPaymentMethods() {
        let arrData = this.state.arrPaymentMethods
        if(arrData.length > 0) {
            return(
                <div className="pos-cashier-payment-methods">
                    <div className="payments">
                        {arrData.map((item, index) => {
                            return(
                                <div key={index} className={`item ${this.state.inputs.payment_type_id === item.id && "active"}`}
                                    onClick={() => {
                                        this.onTextInputListeners(item.id, "payment_type_id")
                                        this.onTextInputListeners(item.name, "payment_type_name")
                                    }}
                                    >
                                    <img alt={item.name} src={item.image_url || Constants.image_default.empty}/>
                                    <div className="title">{item.name}</div>
                                </div>
                            )
                        })}
                    </div>
                </div>
            )
        }
    }
    renderPaymentSummary() {
        return(
            <div className="pos-cashier-payment-summaries">
                <div className="nominals">
                    <label>Transaksi</label>
                    <input disabled value={CommonHelper.formatNumber(this.state.inputs.nominal_transaction, 'idr')} />
                </div>
                <div className="nominals">
                    <label>MDR</label>
                    <input placeholder="0" onChange={(e) => {
                            if (e.target !== undefined && e.target.value !== undefined) {
                                this.onTextInputListeners(Number(e.target.value), 'payment_mdr')
                                setTimeout(() => { this.onCalculateGrandTotal() }, 100);
                            }
                        }}
                        onBlur={(e) => {
                            if(e.target.value === "") {
                                this.onTextInputListeners(0, 'payment_mdr')
                                setTimeout(() => { this.onCalculateGrandTotal() }, 100);
                            }
                        }}
                    />
                </div>
                <div className="nominals">
                    <label className="text-blue">Total Bayar</label>
                    <input className="text-blue" disabled value={CommonHelper.formatNumber(this.state.inputs.payment_total, 'idr')} />
                </div>
                <div className="payment-input">
                    <input 
                        id="input-nominal"
                        placeholder="0" 
                        onChange={(e) => {
                            if (e.target !== undefined && e.target.value !== undefined) {
                                this.onTextInputListeners(Number(e.target.value), 'payment_nominal_input')
                                // setTimeout(() => { this.onCalculateGrandTotal() }, 100);
                            }
                        }}
                        onBlur={(e) => {
                            if(e.target.value === "") {
                                this.onTextInputListeners(0, 'payment_nominal_input')
                                // setTimeout(() => { this.onCalculateGrandTotal() }, 100);
                            }
                        }}
                    />
                    <button className="button" onClick={() => {
                        this.onTextInputListeners(this.state.inputs.payment_total, "payment_nominal_input")
                    }}>SAMA</button>
                </div>
                <div className="payment-nominal">
                    {this.state.arrNominal.map((item, index) => {
                        return(
                            <div key={index} className="item">
                                {CommonHelper.formatNumber(item, 'idr')}
                            </div>
                        )
                    })}
                </div>
                <div className="nominals">
                    <label className="text-green">Kembali</label>
                    <input className="text-green" disabled value={CommonHelper.formatNumber(this.state.inputs.payment_nominal_return, 'idr')} />
                </div>
            </div>
        )
    }
    renderPaymentResults() {
        return(
            <div className="pos-cashier-payment-results">
                <div className="icons">
                    <i className="ph ph-bold ph-check"></i>
                </div>
                <h3>Pembayaran Berhasil!</h3>
                <h2>{CommonHelper.formatNumber(this.state.inputs.payment_total, 'idr')}</h2>
                <div className="infos">
                    <div className="info">
                        <div>No Order</div>
                        <div>TRX20241204001</div>
                    </div>
                    <div className="info">
                        <div>Pelanggan</div>
                        <div>John Doe</div>
                    </div>
                    <div className="info">
                        <div>Metode Bayar</div>
                        <div>Tunai</div>
                    </div>
                    <div className="info">
                        <div>Tanggal Bayar</div>
                        <div>{moment().format("lll")}</div>
                    </div>
                    <div className="info">
                        <div>Kasir / Pegawai</div>
                        <div>Valerine Doe</div>
                    </div>
                </div>
                <button className="new-order" onClick={() => {
                    if(this.state.formType === "kiosk") {
                        window.history.back()
                    }
                    this.onCloseDialog()
                    }}>
                    <span>Transaksi Baru</span>
                </button>
                <button>
                    <i className="ph ph-bold ph-printer"></i>
                    <span>Cetak Struk (Nota)</span>
                </button>
                <button>
                    <i className="ph ph-bold ph-whatsapp-logo"></i>
                    <span>Kirim Struk (Nota)</span>
                </button>
            </div>
        )
    }
}