import "@/styles/colors.css";
import "@/styles/globals.css";
import "@/styles/borders.css";
import "@/styles/detail.css";
import "@/styles/grid-utilities.css";
import "@/styles/flex-utilities.css";
import "@/components/libs/styles/table.css";
import "@/components/libs/styles/inputs.css";
import { createTheme, ThemeProvider } from "@mui/material";
import "moment";
import "moment/locale/id";

const theme = createTheme({
  cssVariables: true,
  palette: {
    mode: "light",
    primary: {
      light: "var(--base-color)",
      main: "var(--base-color)",
      dark: "var(--base-color)",
      contrastText: "var(--text-contrast-color)",
    },
  },
});

export default function App({ Component, pageProps }) {
  return (
    <ThemeProvider theme={theme}>
      <Component {...pageProps} />
    </ThemeProvider>
  );
}
