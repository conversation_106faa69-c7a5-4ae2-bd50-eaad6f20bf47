/* @import url('https://fonts.googleapis.com/css2?family=Titillium+Web:ital,wght@0,200;0,300;0,400;0,600;0,700;0,900;1,200;1,300;1,400;1,600;1,700&display=swap'); */
@font-face {
  font-family: "Titillium Web";
  src: url("/fonts/TitilliumWeb-Regular.ttf") format("truetype");
  font-weight: 400; /* Regular weight */
  font-style: normal;
  font-display: swap; /* Prevents FOUT by swapping to fallback font during load */
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Titillium Web", sans-serif;
  font-size: 16px;
}
body {
  min-height: 100vh;
  background-color: var(--container-background);
  overflow: hidden;
}
h1 {
  font-size: 2rem;
  color: var(--text-primary-color);
}
h2 {
  font-size: 1.5rem;
  color: var(--text-primary-color);
}
h3 {
  font-size: 1.2rem;
  color: var(--text-primary-color);
}
p {
  font-size: 1rem;
  color: var(--text-foreign-color);
}
small {
  font-size: 0.85rem;
}
small.info {
  color: var(--text-foreign-color);
}

nav {
  width: 100%;
}
nav .topbar {
  background-color: var(--topbar-background);
  color: var(--topbar-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.2rem 2.5rem;
  font-size: 0.7rem;
}
nav .topbar .contact {
  font-size: 0.7rem;
}
nav .topbar .contact a {
  font-size: 0.7rem;
  color: var(--topbar-color);
  text-decoration: none;
}
nav .navbar {
  background-color: var(--navbar-background);
  padding: 0 2.5rem;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
nav .navbar .logo {
  display: flex;
  flex-direction: row;
  align-items: center;
}
nav .navbar .logo img {
  width: 2rem;
  height: 2rem;
}
nav .navbar .logo span {
  font-size: 1.5rem;
  margin-left: 0.8rem;
  font-weight: 600;
  color: var(--navbar-primary-color);
}
nav .navbar .title {
  display: none;
}
nav .navbar .btn-menu {
  display: none;
}

/*! NAVBAR MENU =================================================== */
nav .navbar .menu .app-name {
  display: none;
}
nav .navbar .menu {
  margin-left: 2rem;
}
nav .navbar .menu ul {
  display: flex;
  flex-direction: row;
  list-style: none;
  gap: 1rem;
}
nav .navbar .menu ul li {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0.3rem;
  position: relative;
  height: var(--navbar-height);
}
nav .navbar .menu li .arrow {
  transition: all 0.3s ease;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  margin-left: 0.3rem;
}
nav .navbar .menu li a {
  display: flex;
  flex-direction: row;
  align-items: center;
  text-decoration: none;
  color: var(--navbar-inactive-color);
  white-space: nowrap;
  font-weight: 500;
}
nav .navbar .menu li a i {
  margin-right: 0.3rem;
}
nav .navbar .menu li.active a,
nav .navbar .menu li.active a i {
  color: var(--navbar-active-color);
  font-weight: 600;
}
nav .navbar .menu li:hover a,
nav .navbar .menu li:hover i {
  color: var(--navbar-active-color);
}
nav .navbar .menu li:hover .arrow {
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
}
nav .navbar .menu li:last-child {
  padding-left: 0.5rem;
}
nav .navbar .menu li:last-child a {
  display: flex;
  flex-direction: row;
  align-items: center;
}
nav .navbar .menu li:last-child a img {
  width: 2rem;
  height: 2rem;
  border-radius: 2rem;
  -webkit-border-radius: 2rem;
  -moz-border-radius: 2rem;
  -ms-border-radius: 2rem;
  -o-border-radius: 2rem;
  object-fit: cover;
}
nav .navbar .menu li:last-child a .account {
  padding-left: 0.5rem;
}
nav .navbar .menu li:last-child a .account div:first-child {
  font-size: 0.9rem;
  font-weight: 600;
}
nav .navbar .menu li:last-child a .account div:last-child {
  font-size: 0.6rem;
  margin-top: -4px;
}

/*? NAVBAR MENU SUB-MENU ========================================== */
nav .navbar .menu .sub-menu {
  position: absolute;
  display: none;
  background-color: var(--navbar-submenu-background);
  top: calc(var(--navbar-height));
  left: 0;
  border: solid 1px var(--navbar-sub-menu-border-color);
  min-width: 100%;
  z-index: 100;
}
nav .navbar .menu .sub-menu li a {
  font-size: 1rem;
  font-weight: 500;
  flex: 1;
}
nav .navbar .menu .sub-menu li a span {
  flex-grow: 1;
}
nav .navbar .menu .sub-menu li a i {
  padding-left: 2rem;
  margin-right: -6px;
  padding-top: 1px;
  font-weight: 500;
  color: var(--navbar-inactive-color);
}
nav .navbar .menu .sub-menu li {
  padding: 0 1.2rem;
  cursor: pointer;
  height: var(--navbar-sub-menu-height);
}
nav .navbar .menu .sub-menu li:not(:last-child) {
  border-bottom: solid 1px var(--navbar-sub-menu-border-color);
}
nav .navbar .menu .sub-menu li a,
nav .navbar .menu .sub-menu li i {
  color: var(--navbar-inactive-color);
}
nav .navbar .menu .sub-menu li:hover a,
nav .navbar .menu .sub-menu li:hover i {
  color: var(--navbar-active-color);
}
nav .navbar .menu .sub-menu li.active a,
nav .navbar .menu .sub-menu li.active i {
  color: var(--navbar-active-color);
  font-weight: 600;
}
nav .navbar .menu .sub-menu li .arrow {
  transform: rotate(0deg);
  -webkit-transform: rotate(0deg);
  -moz-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  -o-transform: rotate(0deg);
}
nav .navbar .menu .sub-menu li:hover .arrow {
  transform: rotate(0deg);
  -webkit-transform: rotate(0deg);
  -moz-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  -o-transform: rotate(0deg);
}

/** NAVBAR MENU SUB-MENU MORE-MENU ================================ */
nav .navbar .menu .sub-menu .more-menu {
  position: absolute;
  display: none;
  background-color: var(--navbar-submenu-background);
  top: calc(var(--navbar-height) + 5px);
  z-index: 50;
  left: 100%;
  top: -1px;
  border: solid 1px var(--navbar-sub-menu-border-color);
}
nav .navbar .menu .sub-menu .more-menu li a,
nav .navbar .menu .sub-menu .more-menu li i {
  color: var(--navbar-inactive-color);
  font-weight: 500;
}
nav .navbar .menu .sub-menu .more-menu li:hover a,
nav .navbar .menu .sub-menu .more-menu li:hover i {
  color: var(--navbar-active-color);
}
nav .navbar .menu .sub-menu .more-menu li.active a {
  color: var(--navbar-active-color);
  font-weight: 600;
}
nav .navbar .menu li:hover .sub-menu,
nav .navbar .menu .sub-menu li:hover .more-menu {
  display: block;
}

.containers {
  width: 100%;
  height: calc(100vh - 83px);
  background-color: #f9f9f9;
  overflow-y: auto;
  padding: 0rem 2.5rem 2rem;
}
.containers::-webkit-scrollbar {
  width: 4px;
  height: 1px;
}
.containers::-webkit-scrollbar-thumb {
  border-radius: 0.2rem;
  -webkit-border-radius: 0.2rem;
  -moz-border-radius: 0.2rem;
  -ms-border-radius: 0.2rem;
  -o-border-radius: 0.2rem;
  background-color: var(--topbar-background);
  visibility: hidden;
}

/*? BREADCRUMB SECTIONS =========================================== */
.breadcrumb {
  width: 100%;
  background-color: #f9f9f9;
  padding: 1rem 0;
}
.breadcrumb ul {
  display: flex;
  flex-direction: row;
  align-items: center;
  list-style: none;
  gap: 1.3rem;
}
.breadcrumb ul li {
  font-size: 0.9rem;
  color: #7a7979;
  position: relative;
  display: flex;
  align-items: center;
}
.breadcrumb ul li:not(:first-child)::before {
  position: absolute;
  content: "\00BB";
  font-size: 0.9rem;
  padding: 0 0.5rem;
  font-style: normal;
  font-weight: 500;
  top: -1px;
  left: -22px;
}
.breadcrumb ul li:last-child {
  color: var(--base-color);
  font-weight: 600;
}
/* not last child */
.breadcrumb ul li:not(:last-child) {
  cursor: pointer;
}
.breadcrumb ul li i {
  margin-right: 0.3rem;
}
.breadcrumb ul li a {
  color: #42434b;
  font-size: 0.9rem;
}

/*? CONTENT SECTIONS ============================================== */
.contents {
  width: 100%;
}
.contents .side-bars {
  height: calc(100vh - 185px);
  overflow: hidden;
  background-color: white;
  box-shadow: 0 0.4rem 1rem #ededed55;
  border: solid 1px #eeeeee;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
}

.contents .side-bars .menu {
  width: 40%;
  height: calc(100vh - 187px);
  overflow: auto;
}
.contents .side-bars .menu .header {
  padding: 1.2rem 1.5rem 1rem;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  left: 0;
  background-color: #fff;
}
.contents .side-bars .menu .header .title {
  flex: 1;
  padding-right: 1rem;
}
.contents .side-bars .menu .header .title div {
  font-weight: 600;
}
.contents .side-bars .menu .header .title div:last-child {
  font-weight: normal;
  font-size: 0.85rem;
  margin-top: -2px;
}
.contents .side-bars .menu ul li:first-child {
  border-top: solid 1px #c0c0c0;
}
.contents .side-bars .menu ul li {
  border-bottom: solid 1px #e6e6e6;
  padding: 0rem 1.5rem;
  color: var(--navbar-inactive-color);
  cursor: pointer;
  position: relative;
}
.contents .side-bars .menu ul li:hover,
.contents .side-bars .menu ul li.active {
  color: var(--topbar-background);
  font-weight: 600;
}
.contents .side-bars .menu ul li.active::before {
  content: "";
  position: absolute;
  width: 2px;
  height: 100%;
  right: 0;
  top: 0;
  background-color: var(--topbar-background);
}
.contents .side-bars .menu ul li .item {
  display: flex;
  height: 50px;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.contents .side-bars .menu ul li .item div:first-child {
  flex-grow: 1;
  padding-right: 1rem;
}
.contents .side-bars .menu ul li .item div:last-child {
  margin-right: -11px;
}
.contents .side-bars .content {
  border-left: solid 1px #f1f1f1;
  padding: 1.5rem;
  width: 100%;
  height: calc(100vh - 187px);
  overflow: auto;
  position: relative;
}
.contents .side-bars .content:has(.actions) {
  padding-bottom: 0;
}
/* .contents .side-bars .content:has(.actions) .privilege:not(:last-child) {} */
.contents .side-bars .content .actions {
  width: 100%;
  position: -webkit-sticky;
  position: sticky;
  bottom: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 1.2rem 0rem 1.2rem;
  background-color: #fff;
}

/*? TAB BAR SECTIONS ============================================== */
.tab-bar {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.tab-bar .item {
  font-size: 1rem;
  color: #7a7979;
  text-transform: uppercase;
  cursor: pointer;
  border: solid 1px #eeeeee;
  padding: 0.7rem 1.2rem;
  border-bottom: none;
  text-wrap-mode: nowrap;
}
.tab-bar .item:not(:first-child) {
  border-left: none;
}
.tab-bar .item:hover {
  color: #2d3436;
  border-top-color: #2d3436;
}
.tab-bar .item.selected {
  background-color: #ffffff;
  border-top-color: #2d3436;
  font-weight: 600;
  color: #2d3436;
}

/*? FLEX CONTAINER SECTIONS ======================================= */
.flex-container {
  margin-top: 1.2rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.1rem;
  justify-content: center;
}
.flex-container .box {
  flex-grow: 1;
  height: 110px;
  border-radius: 0.65rem;
  -webkit-border-radius: 0.65rem;
  -moz-border-radius: 0.65rem;
  -ms-border-radius: 0.65rem;
  -o-border-radius: 0.65rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 1rem;
  line-height: 1.05rem;
  color: var(--text-primary-color);
  background: linear-gradient(-45deg, white, white);
  box-shadow: -8px -8px 12px 0 #f1f1f1, 8px 8px 12px rgb(222 222 222 / 25%);
}
.flex-container .box .top {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flex-container .box .top i {
  background-color: var(--box-color);
  color: white;
  font-size: 1.2rem;
  padding: 0.6rem;
  border-radius: 2rem;
  -webkit-border-radius: 2rem;
  -moz-border-radius: 2rem;
  -ms-border-radius: 2rem;
  -o-border-radius: 2rem;
  box-shadow: 12px 12px 16px 0 rgba(0, 0, 0, 0.25),
    -8px -8px 12px 0 rgba(255, 255, 255, 0.3);
}
.flex-container .box .top > div > div:first-child {
  font-size: 0.75rem;
  color: #a5a5a5;
  text-transform: uppercase;
}
.flex-container .box .top > div > div {
  font-size: 1rem;
  font-weight: 600;
}
.flex-container .box .bottom {
  flex-grow: 1;
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  justify-content: space-between;
}
.flex-container .box .bottom .value {
  font-size: 1.8rem;
  padding-bottom: 0.3rem;
  font-weight: 600;
  padding-right: 0.5rem;
}
.flex-container .box .bottom .value:has(span) {
  padding-bottom: 0rem;
}
.flex-container .box .bottom .value span {
  font-size: 1rem;
  color: #9f9f9f;
  font-weight: normal;
}
.flex-container .box .bottom .value span:first-child {
  padding-right: 3px;
  margin-top: -2px;
}
.flex-container .box .bottom .value span:last-child {
  padding-left: 3px;
  margin-top: -2px;
}
.flex-container .box .bottom .percentage {
  flex-grow: 1;
  font-size: 0.9rem;
  font-weight: 600;
  color: #b6b6b6;
}
.flex-container .box .bottom .percentage.up {
  color: #16a085;
}
.flex-container .box .bottom .percentage.down {
  color: #e74c3c;
}
.flex-container .box .bottom a {
  margin-bottom: -3px;
  font-size: 1.5rem;
  cursor: pointer;
  color: #59595955;
  text-decoration: none;
}
.flex-container .box .bottom a:hover {
  color: var(--box-color);
}

/** neumorphism card styles */
.flex-container .box.neumorphism {
  background: var(--box-color);
  color: #fff;
}
.flex-container .box.neumorphism .top i {
  box-shadow: 12px 12px 16px 0 rgba(0, 0, 0, 0.25),
    -8px -8px 12px 0 rgb(188 188 188 / 30%);
}
.flex-container .box.neumorphism .top > div > div:first-child {
  color: #fff;
  opacity: 0.7;
}
.flex-container .box.neumorphism .bottom .value span {
  color: #fff;
  opacity: 0.8;
}
.flex-container .box.neumorphism .bottom .percentage,
.flex-container .box.neumorphism .bottom .percentage.up,
.flex-container .box.neumorphism .bottom .percentage.down {
  color: #fff;
}
.flex-container .box.neumorphism .bottom a {
  color: #fff;
  opacity: 0.5;
}
.flex-container .box.neumorphism .bottom a:hover {
  opacity: 1;
}
.flex-container .box.neumorphism .bottom a::before {
  background-color: white;
  color: var(--box-color);
}

.auto-height {
  height: auto !important;
}
.text-left {
  text-align: left !important;
}
.text-center {
  text-align: center !important;
}
.text-right {
  text-align: right !important;
}

/** button styles */
.button {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0 0.7rem;
  height: 40px;
  background-color: var(--base-color);
  border: none;
  color: #ffffff;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  font: inherit;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 16px;
  cursor: pointer;
}
.button.cancel {
  background-color: #95a5a6;
}
.button.cancel:hover {
  background-color: #7f8c8d;
}
.button i {
  margin-right: 10px;
  font-size: 1.2rem;
}
.button:not(:first-child) {
  margin-left: 0.7rem;
}
.button:focus,
.button:hover {
  background-color: var(--base-hover-color);
}
a:not(:first-child) {
  margin-left: 1rem;
  text-decoration: none;
}
.button.cancel {
  background-color: #95a5a6;
}
.button.cancel:hover {
  background-color: #7f8c8d;
}
.button.success {
  background-color: #2ecc71;
}
.button.success:hover {
  background-color: #27ae60;
}
.button.warning {
  background-color: #e67e22;
}
.button.warning:hover {
  background-color: #d35400;
}
.button.danger {
  background-color: #e74c3c;
}
.button.danger:hover {
  background-color: #c0392b;
}
.button.info {
  background-color: #3498db;
}
.button.info:hover {
  background-color: #2980b9;
}
.button.mr1 {
  margin-right: 1rem;
}
.button.outlined {
  background-color: transparent;
  color: var(--base-color);
  border: 1px solid var(--base-color);
}
.button.outlined:hover {
  color: #ffffff;
}
.button.outlined.danger {
  background-color: transparent;
  color: #e74c3c;
  border: 1px solid #e74c3c;
}
.button.outlined.danger:hover {
  background-color: #e74c3c;
  color: #ffffff;
}

/** button dropdown styles */
.dropdown {
  position: relative;
}
.dropdown .button {
  border: 1px solid #d2d2d2;
  color: var(--text-primary-color);
  background-color: #fff;
  padding-left: 0.5rem;
  padding-right: 0rem;
}
.dropdown .button i {
  margin-left: 0.5rem;
  font-size: 0.9rem;
}

/** page not found styles */
.not-found {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 0 2rem;
}
.not-found .image {
  width: 30%;
}
.not-found .title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary-color);
  margin-top: 1.5rem;
  text-align: center;
}
.not-found .description {
  font-size: 1rem;
  color: #7a7979;
  margin-top: 0.5rem;
  text-align: center;
}

/*? modal styles */
.modal-content {
  position: relative;
  background-color: #fefefe;
  margin: auto;
  padding: 0;
  width: 80%;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  -webkit-animation-name: animatetop;
  -webkit-animation-duration: 0.4s;
  animation-name: animatetop;
  animation-duration: 0.4s;
  border-radius: 7px;
  -webkit-border-radius: 7px;
  -moz-border-radius: 7px;
  -ms-border-radius: 7px;
  -o-border-radius: 7px;
  overflow: hidden;
}
.modal-content.small {
  width: 30%;
}
.modal-content.medium {
  width: 80%;
}
.modal-content.large {
  width: 94%;
}
.modal-content.medium.sx {
  width: 50%;
}
.modal-content.dialog {
  width: 30%;
}
.modal-content.dialog.sx {
  width: 22%;
}
.modal-content.dialog.sm {
  width: 50%;
}
.modal-content .modal-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0 1rem;
  border-bottom: solid 1px rgba(0, 0, 0, 0.07);
}
.modal-content .modal-header .title {
  font-weight: 600;
  font-size: 1.1rem;
  color: #42434b;
}
.modal-content .modal-header .close {
  color: #e74c3c;
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 5px;
}
.modal-content .modal-header .close:hover,
.modal-content .modal-header .close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
}
.modal-content .modal-body {
  padding: 1.5rem 1rem;
  overflow: auto;
  min-height: 100px;
  max-height: 77vh;
}

/* Fix for data-list in modals */
.modal-content .modal-body:has(.form-input .data-list.show) {
  overflow: visible;
}
.modal-content .modal-footer {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  padding: 0.5rem 1rem;
  border-top: solid 1px rgba(0, 0, 0, 0.07);
}

.modal-content .modal-body:has(table) {
  height: 77vh;
  padding: 0rem;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.modal-content .modal-body .header {
  padding: 1rem;
}
.modal-content .modal-body .body {
  padding: 0px;
  flex-grow: 1;
  background-color: #f2f2f2;
  width: 100%;
  margin: 0 auto;
  overflow: auto;
  font-size: 1rem;
  font-weight: 600;
  color: #5a5a5a;
}
.modal-content .modal-body .footer {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 1.2rem;
  background-color: #fff;
  border-top: solid 1px #ececec;
}
.modal-content .modal-body .body table {
  width: 100%;
}
.modal-content .modal-body .body td img {
  width: 28px;
  height: 28px;
  object-fit: cover;
  margin-right: 0.5rem;
  vertical-align: middle;
}
.modal-content .modal-body .body table,
.modal-content .modal-body .body th,
.modal-content .modal-body .body td {
  color: #2d3436;
  border-collapse: collapse;
  text-align: left;
  text-wrap: nowrap;
  font-size: 0.95rem;
  font-weight: normal;
}
.modal-content .modal-body .body thead th {
  padding: 0.65rem 1.2rem;
  z-index: 1;
  position: sticky;
  top: 0;
  left: 0;
  background-color: #636e72;
  color: #ffffff;
  font-weight: 600;
  text-transform: uppercase;
}
.modal-content .modal-body .body thead th > div {
  text-transform: uppercase;
  color: white;
  font-weight: 600;
  font-size: 0.85rem;
}
.modal-content .modal-body .body thead th > .sorts {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
}
.modal-content .modal-body .body thead th > .sorts > i {
  margin-left: 1rem;
  margin-right: -5px;
}
.modal-content .modal-body .body table td {
  padding: 0.5rem 1.2rem;
}
.modal-content .modal-body .body table tr td:first-child {
  position: sticky;
  top: 0;
  left: 0;
}
.modal-content .modal-body .body table td .actions {
  display: flex;
  flex-direction: row;
  gap: 5px;
}
.modal-content .modal-body .body tbody tr:nth-child(odd),
.modal-content .modal-body .body table tr:nth-child(odd) td:first-child {
  background-color: #ffffff;
}
.modal-content .modal-body .body tbody tr:nth-child(even),
.modal-content .modal-body .body table tr:nth-child(even) td:first-child {
  background-color: #f9f9f9;
}
.modal-content .modal-body .body tbody tr:hover {
  background-color: rgb(236, 236, 236);
  cursor: pointer;
}
.modal-content .modal-body .body tbody tr:hover td:first-child {
  background-color: rgb(236, 236, 236);
  cursor: pointer;
}
.modal-content .modal-body .input-search {
  width: 40%;
  display: flex;
  margin-left: 1rem;
}
.modal-content .modal-body .input-search label {
  flex-shrink: 0;
  height: 40px;
  width: 40px;
  background-color: #636e72;
  color: rgb(255, 255, 255);
  border-radius: 5px 0 0 5px;
  -webkit-border-radius: 5px 0 0 5px;
  -moz-border-radius: 5px 0 0 5px;
  -ms-border-radius: 5px 0 0 5px;
  -o-border-radius: 5px 0 0 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.2rem;
  font-weight: 500;
}
.modal-content .modal-body .input-search input {
  box-sizing: border-box;
  flex-grow: 1;
  min-width: 0;
  height: 40px;
  padding: 1em;
  font: inherit;
  border-radius: 0 5px 5px 0;
  -webkit-border-radius: 0 5px 5px 0;
  -moz-border-radius: 0 5px 5px 0;
  -ms-border-radius: 0 5px 5px 0;
  -o-border-radius: 0 5px 5px 0;
  border-left: none;
  transition: 150ms ease;
  -webkit-transition: 150ms ease;
  -moz-transition: 150ms ease;
  -ms-transition: 150ms ease;
  -o-transition: 150ms ease;
  background-color: #fff;
  border: 1px solid #636e72;
}
.modal-content .modal-body .input-search input:hover {
  border-color: #2d3436;
}
.modal-content .modal-body .input-search input:focus {
  outline: none;
  border-color: #2d3436;
}
.modal-content .modal-body .input-search input:disabled {
  background-color: #f7f7f7;
  border-color: #f7f7f7;
}
.modal-content .modal-body .input-search:has(input:hover) > label {
  background-color: #2d3436;
}
.modal-content .modal-body .input-search:has(input:focus) > label {
  background-color: #2d3436;
}
.modal-content .modal-body .input-search input::placeholder {
  color: #d1d1d5;
}
.modal-divider {
  width: 100%;
  height: 1px;
  border-bottom: dotted 2px #d1d1d5;
  margin: 1.5rem 0;
}

/*? input-form styles */
.input-form {
  margin-top: 1.5rem;
}
.input-form:has(.label) {
  margin-top: 1rem;
}
.input-form:first-child {
  margin-top: 0px;
}
.input-form label {
  font-size: 1.1rem;
}
.input-form .label {
  color: var(--input-label-color);
  font-weight: 600;
  font-size: 0.95rem;
  text-transform: capitalize;
  margin-bottom: 0.2rem;
}
.input-form .label.disabled {
  color: #b3b3b3;
}
.input-form .placeholder {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  color: #c1c1c1;
  margin-top: 0.1rem;
}
.input-form .placeholder .required {
  color: #e74c3c;
}
.input-form .placeholder .optional {
  color: #27ae60;
}
.input-form .placeholder .read-only {
  color: #3498db;
}
.input-form .placeholder .required,
.input-form .placeholder .optional,
.input-form .placeholder .read-only {
  font-size: 0.7rem;
  flex-grow: 1;
  padding-right: 1rem;
  font-weight: 600;
  flex-grow: 1;
}
.input-form .placeholder .note,
.input-form .placeholder .note b {
  font-size: 0.8rem;
}
.input-form .placeholder .note b {
  color: #7d7d7d;
}

.input-image {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-bottom: 2rem;
}
.input-image img {
  width: 120px;
  height: 120px;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
  object-fit: cover;
  background-color: #f7f7f7;
}
.input-image > div {
  flex: 1;
  padding-left: 1.2rem;
}
.input-image .button {
  margin-left: 0 !important;
  margin-top: 0.6rem;
}
.input-image .note {
  font-size: 0.9rem;
  font-weight: normal;
}

.input-multi-image {
  display: grid;
  /* grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); */
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1rem;
  justify-content: center;
}
.input-multi-image .image {
  position: relative;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  overflow: hidden;
  min-width: 150px;
  height: 150px;
  border: dotted 2px #d7d7d7;
  cursor: pointer;
}
.input-multi-image .image.no-border {
  border: none;
}
.input-multi-image .image.no-border:hover i {
  color: #ffffff;
}
.input-multi-image .image:hover {
  border-color: var(--base-color);
}
.input-multi-image .image:hover i {
  color: var(--base-color);
}
.input-multi-image .image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
}
.input-multi-image .image .title {
  font-size: 0.8rem;
  color: var(--text-primary-color);
  text-align: center;
  position: absolute;
  top: 10px;
  left: 10px;
  background-color: var(--text-foreign-color);
  padding: 0.1rem 0.5rem;
  color: #ffffff;
}
.input-multi-image .image .title.main {
  background-color: var(--base-color);
}
.input-multi-image .image i {
  color: #d7d7d7;
  position: absolute;
  top: 50%;
  left: 50%;
  font-size: 2rem;
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}

.flex-rows {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  gap: 1.2rem;
}
.flex-rows.no-gap {
  gap: 0rem;
}
.flex-rows .row {
  width: 100%;
}
.flex-rows .row.wd10 {
  width: 10%;
}
.flex-rows .row.wd20 {
  width: 20%;
}
.flex-rows .row.wd30 {
  width: 30%;
}
.flex-rows .row.wd40 {
  width: 40%;
}
.flex-rows .row.wd50 {
  width: 50%;
}
.flex-rows .row.wd60 {
  width: 60%;
}
.flex-rows .row:not(:last-child) {
  border-right: solid 1px rgb(213, 213, 213);
  padding-right: 1rem;
}
.flex-rows.no-gap .row:not(:last-child) {
  padding-right: 0.6rem;
}
.flex-rows.no-right .row:not(:last-child) {
  padding-right: 0rem;
}
.flex-rows .row.no-border {
  border: none;
}
.flex-rows .row .box {
  padding: 1.2rem 1rem;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  background-color: #fafafa;
  border: solid 1px #e9e9e9;
}
.flex-rows .row .box .title {
  font-size: 0.95rem;
  font-weight: 600;
  text-transform: uppercase;
  margin-bottom: 1rem;
  color: #42434b;
}
.flex-rows.beetween {
  justify-content: space-between;
  gap: 0;
  color: #353c3e;
}
.flex-rows.beetween:not(:first-child) {
  margin-top: 0.4rem;
  border-top: solid 1px rgb(213, 213, 213);
  padding-top: 0.4rem;
}
.flex-rows.beetween > div:last-child {
  font-weight: 600;
  padding-left: 1rem;
}

.status {
  font-size: 0.75rem;
  text-transform: uppercase;
  padding: 0.2rem 0.8rem;
  border-radius: 1rem;
  -webkit-border-radius: 1rem;
  -moz-border-radius: 1rem;
  -ms-border-radius: 1rem;
  -o-border-radius: 1rem;
  background-color: #27ae60;
  color: #ffffff;
  font-weight: 600;
}
.status.unavailable {
  background-color: #e74c3c;
}
.status.inlimits {
  background-color: #3498db;
}
.status.lowlimits {
  background-color: #e67e22;
}
.status.maxlimits {
  background-color: #34495e;
}

.option-selector {
  display: flex;
  gap: 1rem;
  padding-bottom: 1rem;
}

.text-red {
  color: #e74c3c !important;
}
.text-orange {
  color: #e67e22 !important;
}
.text-green {
  color: #27ae60 !important;
}
.text-blue {
  color: #3498db !important;
}
.text-uppercase {
  text-transform: uppercase !important;
}

/*? PRIVILEGE PAGE styles */
.privilege {
  box-shadow: 0 0.4rem 1rem #ededed55;
  border: solid 1px #eeeeee;
  border-radius: 7px;
  -webkit-border-radius: 7px;
  -moz-border-radius: 7px;
  -ms-border-radius: 7px;
  -o-border-radius: 7px;
  overflow: hidden;
}
.privilege:not(:first-child) {
  margin-top: 1.28rem;
}
.privilege .title {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  background-color: #eeeeee;
  padding: 0.2rem 1.2rem;
  font-weight: 600;
  color: var(--navbar-inactive-color);
  text-transform: uppercase;
}
.privilege .title.active {
  color: #2d3436;
}
.privilege .title div:last-child {
  margin-right: -10px;
}
.privilege .items.more {
  padding-left: 3.3rem;
}
.privilege .items .item {
  display: flex;
  flex-direction: row;
  align-items: center;
  border-top: solid 1px #eeeeee;
  padding: 0.4rem 1.2rem;
  color: var(--navbar-inactive-color);
}
.privilege .items .item.active {
  color: #2d3436;
}
.privilege .items .item div:first-child {
  margin-left: -10px;
  margin-right: 0.5rem;
}
.not-alowed-device {
  display: none;
}

.modal-content .modal-body:has(.form-table) {
  overflow: hidden;
  height: 77vh;
  padding: 0;
}
.form-table {
  display: flex;
  flex-direction: row;
  gap: 1.2rem;
}
.form-table .title {
  font-weight: 600;
  text-transform: uppercase;
  font-size: 1rem;
  color: #2d3436;
  margin-bottom: 1.5rem;
  text-decoration: underline;
}
.form-table .form {
  width: 28%;
  height: 77vh;
  overflow: auto;
  padding: 1.2rem 0rem 1.2rem 1.2rem;
}
.form-table .form:last-child {
  padding: 1.2rem 1.2rem 1.2rem 0rem;
}
.form-table .form::-webkit-scrollbar {
  width: 4px;
  height: 1px;
}
.form-table .form::-webkit-scrollbar-thumb {
  border-radius: 0.2rem;
  -webkit-border-radius: 0.2rem;
  -moz-border-radius: 0.2rem;
  -ms-border-radius: 0.2rem;
  -o-border-radius: 0.2rem;
  background-color: var(--topbar-background);
  visibility: hidden;
}
.form-table .table {
  width: 72%;
  height: 77vh;
  display: flex;
  flex-direction: column;
}
.form-table:has(.form.right) .table {
  width: 50%;
}
.form-table:has(.form.right) .table .center {
  border-right: solid 1px #f2f2f2;
}
.form-table .table .top {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.form-table .table .center {
  background-color: #f2f2f2;
  flex-grow: 1;
  overflow: auto;
}
.form-table .table .center table {
  width: 100%;
}
.form-table .table .center td img {
  width: 28px;
  height: 28px;
  object-fit: cover;
  margin-right: 0.5rem;
  vertical-align: middle;
}
.form-table .table .center table,
.form-table .table .center th,
.form-table .table .center td {
  color: #2d3436;
  border-collapse: collapse;
  text-align: left;
  text-wrap: nowrap;
  font-size: 0.95rem;
  font-weight: normal;
}
.form-table .table .center thead th {
  padding: 0.65rem 1.2rem;
  z-index: 1;
  position: sticky;
  top: 0;
  left: 0;
  background-color: #636e72;
  color: #ffffff;
  font-weight: 600;
  text-transform: uppercase;
}
.form-table .table .center thead th > div {
  text-transform: uppercase;
  color: white;
  font-weight: 600;
  font-size: 0.85rem;
}
.form-table .table .center table td {
  padding: 0.5rem 1.2rem;
}
.form-table .table .center table tr td:first-child {
  position: sticky;
  top: 0;
  left: 0;
}
.form-table .table .center table td .actions {
  display: flex;
  flex-direction: row;
  gap: 5px;
}
.form-table .table .center tbody tr:nth-child(odd),
.form-table .table .center table tr:nth-child(odd) td:first-child {
  background-color: #ffffff;
}
.form-table .table .center tbody tr:nth-child(even),
.form-table .table .center table tr:nth-child(even) td:first-child {
  background-color: #f9f9f9;
}
.form-table .table .center tbody tr:hover {
  background-color: rgb(236, 236, 236);
  cursor: pointer;
}
.form-table .table .center tbody tr:hover td:first-child {
  background-color: rgb(236, 236, 236);
  cursor: pointer;
}
.form-table .table .bottom .flex-rows {
  display: flex;
  align-items: center;
  border-top: solid 1px #f2f2f2;
  padding: 0.5rem 0;
}
.form-table .table .bottom .flex-rows:first-child {
  border-top: none;
}
.form-table .table .bottom .flex-rows:last-child {
  font-weight: 600;
  color: var(--topbar-background);
}

/*?=============== SALES-ORDER POS PAGE styles */
.pos-cashier {
  display: flex;
}
.pos-cashier .catalogs {
  height: 100vh;
  background-color: #fcfcfc;
  width: calc(100% - 480px);
  overflow: auto;
}
.pos-cashier .catalogs::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}
.pos-cashier .catalogs::-webkit-scrollbar-thumb {
  border-radius: 0.2rem;
  -webkit-border-radius: 0.2rem;
  -moz-border-radius: 0.2rem;
  -ms-border-radius: 0.2rem;
  -o-border-radius: 0.2rem;
  background-color: var(--topbar-background);
}
.pos-cashier .catalogs .topbar {
  background-color: #fcfcfc;
  position: sticky;
  top: 0;
  padding: 1.3rem 2rem;
}
.pos-cashier .catalogs .topbar .store {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 1rem;
}
.pos-cashier .catalogs .topbar .store .info {
  flex: 1;
  padding-right: 1.5rem;
}
.pos-cashier .catalogs .topbar .store .info .name {
  font-size: 1rem;
  color: var(--text-primary-color);
  font-weight: 600;
  text-transform: capitalize;
}
.pos-cashier .catalogs .topbar .store .info .address {
  font-size: 0.8rem;
  color: var(--text-foreign-color);
  margin-top: -4px;
  text-transform: capitalize;
}
.pos-cashier .catalogs .topbar .store .date .time-picker {
  font-size: 0.85rem;
  color: var(--text-primary-color);
}
.pos-cashier .catalogs .topbar .store .date .cashier {
  color: var(--text-primary-color);
  margin-bottom: -3px;
  text-align: right;
  text-transform: capitalize;
  font-size: 0.9rem;
  font-weight: 600;
}
.pos-cashier .catalogs .topbar .categories {
  display: flex;
  align-items: center;
  overflow-y: auto;
  gap: 1.3rem;
  cursor: all-scroll;
}
.pos-cashier .catalogs .topbar .categories::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}
.pos-cashier .catalogs .topbar .categories::-webkit-scrollbar-thumb {
  border-radius: 0.2rem;
  -webkit-border-radius: 0.2rem;
  -moz-border-radius: 0.2rem;
  -ms-border-radius: 0.2rem;
  -o-border-radius: 0.2rem;
  background-color: var(--topbar-background);
  visibility: hidden;
}
.pos-cashier .catalogs .topbar .categories .item {
  min-width: 123.8px;
  text-wrap: nowrap;
  background-color: #ffffff;
  border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -ms-border-radius: 2px;
  -o-border-radius: 2px;
  box-shadow: -8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(222 222 222 / 25%);
  border: solid 1px #ececec;
  padding: 0.8rem;
  cursor: pointer;
  color: #2d3436;
}
.pos-cashier .catalogs .topbar .categories .item i {
  font-size: 20px;
}
.pos-cashier .catalogs .topbar .categories .item img {
  height: 20px;
  object-fit: contain;
}
.pos-cashier .catalogs .topbar .categories .item .title {
  font-size: 1rem;
  font-weight: 600;
  padding-top: 3px;
  text-transform: capitalize;
}
.pos-cashier .catalogs .topbar .categories .item .desc {
  font-size: 0.8rem;
  font-weight: 600;
  margin-top: -4px;
}
.pos-cashier .catalogs .topbar .categories .item .desc small {
  font-weight: normal;
  font-size: 0.7rem;
}
.pos-cashier .catalogs .topbar .categories .item:hover,
.pos-cashier .catalogs .topbar .categories .item.active {
  border-color: rgba(229, 0, 64, 0.5);
  color: rgba(229, 0, 64, 1);
}
.pos-cashier .catalogs .topbar .searchbar {
  margin-bottom: 1.3rem;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 1.3rem;
}
.pos-cashier .catalogs .topbar .searchbar a {
  height: 45px;
  width: 45px;
  border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -ms-border-radius: 2px;
  -o-border-radius: 2px;
  background-color: #2d3436;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}
.pos-cashier .catalogs .topbar .searchbar a i {
  font-size: 1.4rem;
  color: #ffffff;
}
.pos-cashier .catalogs .topbar .searchbar .input-search {
  flex-grow: 1;
  display: flex;
  align-items: center;
  border: 1px solid #ececec;
  background-color: #fff;
  box-shadow: -8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(222 222 222 / 25%);
  border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -ms-border-radius: 2px;
  -o-border-radius: 2px;
  overflow: hidden;
}
.pos-cashier .catalogs .topbar .searchbar .input-search label {
  flex-shrink: 0;
  height: 45px;
  width: 45px;
  background-color: #fff;
  color: #2d3436;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  font-size: 1.2rem;
  font-weight: 500;
  padding-right: 5px;
}
.pos-cashier .catalogs .topbar .searchbar .input-search input {
  box-sizing: border-box;
  flex-grow: 1;
  min-width: 0;
  height: 45px;
  padding: 1em;
  font: inherit;
  transition: 150ms ease;
  -webkit-transition: 150ms ease;
  -moz-transition: 150ms ease;
  -ms-transition: 150ms ease;
  -o-transition: 150ms ease;
  background-color: #fff;
  border: none;
  outline: none;
}
.pos-cashier
  .catalogs
  .topbar
  .searchbar
  .input-search:has(input:hover)
  > label,
.pos-cashier
  .catalogs
  .topbar
  .searchbar
  .input-search:has(input:focus)
  > label {
  color: var(--base-color);
}
.pos-cashier .catalogs .topbar .searchbar .input-search:has(input:hover),
.pos-cashier .catalogs .topbar .searchbar .input-search:has(input:focus) {
  border-color: rgba(229, 0, 64, 0.5);
}
.pos-cashier .catalogs .products {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1.3rem;
  justify-content: center;
  padding-left: 2rem;
  padding-right: 2rem;
  padding-bottom: 1.3rem;
  cursor: all-scroll;
}
.pos-cashier .catalogs .products .item {
  background-color: #fff;
  box-shadow: -8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(222 222 222 / 25%);
  border: solid 1px #ececec;
  border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -ms-border-radius: 2px;
  -o-border-radius: 2px;
  overflow: hidden;
  cursor: pointer;
  padding: 0.8rem;
  display: flex;
  flex-direction: column;
}
.pos-cashier .catalogs .products .item img {
  /* width: 100%;
    object-fit: contain; */
  height: 200px;
  object-fit: cover;
  border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -ms-border-radius: 2px;
  -o-border-radius: 2px;
}
.pos-cashier .catalogs .products .item .tags {
  font-size: 0.8rem;
  padding: 10px 5px 5px;
  color: #2d3436;
}
.pos-cashier .catalogs .products .item .title {
  font-weight: 600;
  font-size: 1rem;
  line-height: 21px;
  padding: 0px 5px 5px;
  flex-grow: 1;
  color: #2d3436;
  text-transform: capitalize;
}
.pos-cashier .catalogs .products .item .price {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 5px;
  color: rgba(229, 0, 64, 1);
}
.pos-cashier .catalogs .products .item .price > div {
  font-weight: 600;
  font-size: 1.1rem;
}
.pos-cashier .catalogs .products .item .price > div:last-child {
  font-size: 0.8rem;
  color: #2d3436;
}
.pos-cashier .catalogs .products .item button {
  border: none;
  background-color: rgba(229, 0, 64, 0.1);
  margin: 10px 5px 5px;
  padding: 5px 10px;
  font-weight: 600;
  color: rgba(229, 0, 64, 1);
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  cursor: pointer;
  font-size: 0.9rem;
  text-transform: uppercase;
}
.pos-cashier .catalogs .products .item:hover button,
.pos-cashier .catalogs .products .item button:hover,
.pos-cashier .catalogs .products .item.active button {
  background-color: rgba(229, 0, 64, 1);
  color: #fff;
}
.pos-cashier .catalogs .products .item:hover,
.pos-cashier .catalogs .products .item.active {
  border-color: rgba(229, 0, 64, 0.5);
}
.pos-cashier .catalogs .products .item.disabled .title,
.pos-cashier .catalogs .products .item.disabled .tags,
.pos-cashier .catalogs .products .item.disabled .price > div {
  color: #cbcbcb;
}
.pos-cashier .catalogs .products .item.disabled button {
  background-color: rgb(247 247 247);
  color: rgb(214, 214, 214);
}
.pos-cashier .carts {
  height: 100vh;
  background-color: #ffffff;
  width: 480px;
  border-left: solid 1px #ececec;
  overflow: auto;
  display: flex;
  flex-direction: column;
}
.pos-cashier .carts::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}
.pos-cashier .carts::-webkit-scrollbar-thumb {
  border-radius: 0.2rem;
  -webkit-border-radius: 0.2rem;
  -moz-border-radius: 0.2rem;
  -ms-border-radius: 0.2rem;
  -o-border-radius: 0.2rem;
  background-color: var(--topbar-background);
}
.pos-cashier .carts .customers {
  border-bottom: solid 1px #ececec;
  position: sticky;
  top: 0;
  background-color: #ffffff;
  z-index: 1;
}
.pos-cashier .carts .customers .info,
.pos-cashier .carts .customers .type {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0.8rem 1.2rem;
}
.pos-cashier .carts .customers .type {
  padding-top: 1.2rem;
  padding-bottom: 0;
  gap: 0.8rem;
}
.pos-cashier .carts .customers .type button {
  width: 100%;
  height: 35px;
  background-color: #fff;
  box-shadow: -8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(222 222 222 / 25%);
  border: solid 1px #ececec;
  border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -ms-border-radius: 2px;
  -o-border-radius: 2px;
  overflow: hidden;
  cursor: pointer;
  color: var(--text-primary-color);
  font-size: 1rem;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.pos-cashier .carts .customers .type button i {
  margin-right: 0.5rem;
}
.pos-cashier .carts .customers .type button.active,
.pos-cashier .carts .customers .type button:hover {
  border-color: rgba(229, 0, 64, 0.5);
  color: rgba(229, 0, 64, 1);
  font-weight: 600;
}
.pos-cashier .carts .customers .type button.active i,
.pos-cashier .carts .customers .type button:hover i {
  font-weight: 600;
}
.pos-cashier .carts .customers .info .name {
  padding-right: 1rem;
  flex: 1;
}
.pos-cashier .carts .customers .info .name div:first-child {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary-color);
}
.pos-cashier .carts .customers .info .name div:last-child {
  font-size: 0.8rem;
  margin-top: -4px;
  font-weight: normal;
  color: var(--text-foreign-color);
}
.pos-cashier .carts .customers .info button {
  width: 40px;
  height: 40px;
  cursor: pointer;
  border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -ms-border-radius: 2px;
  -o-border-radius: 2px;
  border: none;
  background-color: rgba(229, 0, 64, 0.1);
  color: rgba(229, 0, 64, 1);
  border: solid 1px rgba(229, 0, 64, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}
.pos-cashier .carts .customers .info button:not(:first-child) {
  margin-left: 0.8rem;
}
.pos-cashier .carts .customers .info button i {
  font-size: 1.2rem;
}
.pos-cashier .carts .customers .info button:hover {
  background-color: rgba(229, 0, 64, 1);
  color: #ffffff;
}
.pos-cashier .carts .items {
  padding: 1rem 1.2rem;
  flex-grow: 1;
}
.pos-cashier .carts .items .item {
  cursor: pointer;
  border: solid 1px #ececec;
  padding: 0.8rem;
  margin-bottom: 0.9rem;
  border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -ms-border-radius: 2px;
  -o-border-radius: 2px;
}
.pos-cashier .carts .items .item:hover {
  border-color: rgba(229, 0, 64, 0.5);
}
.pos-cashier .carts .items .item:hover .name div:first-child {
  color: rgba(229, 0, 64, 1);
}
.pos-cashier .carts .items .item:last-child {
  margin-bottom: 0;
}
.pos-cashier .carts .items .item .product {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
}
.pos-cashier .carts .items .item .product img {
  width: 60px;
  height: 60px;
  object-fit: contain;
  background-color: #fff;
  border: solid 1px #f0f0f0;
  border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -ms-border-radius: 2px;
  -o-border-radius: 2px;
  overflow: hidden;
}
.pos-cashier .carts .items .item .product .name {
  flex: 1;
  padding-right: 0.8rem;
  padding-left: 0.8rem;
  color: var(--text-primary-color);
}
.pos-cashier .carts .items .item .product .name div:first-child {
  font-weight: 600;
  font-size: 1rem;
  line-height: 21px;
}
.pos-cashier .carts .items .item .product .name div:last-child {
  font-weight: normal;
  font-size: 0.8rem;
  margin-top: 5px;
}
.pos-cashier .carts .items .item .qty-price {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 0.8rem;
}
.pos-cashier .carts .items .item .qty-price button {
  width: 28px;
  height: 28px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -ms-border-radius: 2px;
  -o-border-radius: 2px;
}
.pos-cashier .carts .items .item .qty-price button i {
  margin-right: 0;
  font-size: 1rem;
}
.pos-cashier .carts .items .item .qty-price > input {
  width: 50px;
  height: 28px;
  margin-left: 10px;
  border: solid 1px #c8c8c8;
  border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -ms-border-radius: 2px;
  -o-border-radius: 2px;
  padding: 0 0.3rem;
  outline: none;
}
.pos-cashier .carts .items .item .desc {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-top: 0.7rem;
}
.pos-cashier .carts .items .item .desc input {
  outline: none;
  border: none;
  font-size: 0.95rem;
  flex: 1;
  padding-left: 0.5rem;
}
.pos-cashier .carts .items .item .desc input::placeholder {
  color: rgb(197, 197, 197);
}
.pos-cashier .carts .items .item .desc i {
  color: rgb(127, 127, 127);
}
.pos-cashier .carts .items .item .desc:has(input:hover) i,
.pos-cashier .carts .items .item .desc:has(input:focus) i {
  color: rgba(229, 0, 64, 1);
}
.pos-cashier .carts .items .item .qty-price input:hover,
.pos-cashier .carts .items .item .qty-price input:focus {
  border-color: rgba(229, 0, 64, 0.5);
}
.pos-cashier .carts .items .item .qty-price div {
  flex: 1;
  text-align: right;
  padding-left: 1rem;
  font-size: 0.9rem;
  color: var(--text-foreign-color);
  font-weight: 600;
}
.pos-cashier .carts .items.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: var(--text-foreign-color);
  padding: 0 2.5rem;
}
.pos-cashier .carts .items.empty .title {
  font-size: 1.2rem;
  font-weight: 600;
}
.pos-cashier .carts .items.empty .subtitle {
  font-size: 0.85rem;
  margin-top: 0.5rem;
}
.pos-cashier .carts .summaries {
  position: sticky;
  bottom: 0;
  background-color: #ffffff;
  padding: 0rem 1.2rem 1rem;
}
.pos-cashier .carts .summaries .payment-info {
  background-color: #f5f5f5;
  padding: 0.8rem 1rem;
  border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -ms-border-radius: 2px;
  -o-border-radius: 2px;
  border: solid 1px #efefef;
}
.pos-cashier .carts .summaries .commision-info {
  background-color: #ebebeb;
  padding: 0.1rem 1rem 0.4rem 1rem;
  border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -ms-border-radius: 2px;
  -o-border-radius: 2px;
  border: solid 1px #efefef;
}
.pos-cashier .carts .summaries .payment-info .nominals,
.pos-cashier .carts .summaries .commision-info .nominals {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding-top: 0.3rem;
}
.pos-cashier .carts .summaries .payment-info .nominals .title,
.pos-cashier .carts .summaries .commision-info .nominals .title,
.pos-cashier .carts .summaries .payment-info .nominals .value,
.pos-cashier .carts .summaries .commision-info .nominals .value {
  font-size: 0.95rem;
  color: var(--text-foreign-color);
  font-weight: 600;
}
.pos-cashier .carts .summaries .payment-info .nominals:first-child {
  padding-top: 0;
}
.pos-cashier .carts .summaries .payment-info .nominals:first-child .title,
.pos-cashier .carts .summaries .payment-info .nominals:first-child .value {
  color: var(--text-primary-color);
}
.pos-cashier .carts .summaries .payment-info .nominals:last-child {
  border-top: dotted 1px #9f9f9f;
  margin-top: 0.5rem;
  padding-top: 0.5rem;
}
.pos-cashier .carts .summaries .payment-info .nominals:last-child .title,
.pos-cashier .carts .summaries .payment-info .nominals:last-child .value {
  font-weight: 600;
  font-size: 1.1rem;
  color: var(--base-color);
}
.pos-cashier .carts .summaries .payment-action {
  padding-top: 1rem;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.pos-cashier .carts .summaries .payment-action button {
  align-items: center;
  justify-content: center;
  border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -ms-border-radius: 2px;
  -o-border-radius: 2px;
  height: 45px;
  width: 45px;
}
.pos-cashier .carts .summaries .payment-action button:first-child {
  flex-grow: 1;
}
.pos-cashier .carts .summaries .payment-action button:not(:first-child) i {
  margin-right: 0;
}
.pos-cashier-payment-methods {
  margin-top: -1rem;
  margin-bottom: -0.5rem;
}
.pos-cashier-payment-methods .title {
  color: var(--text-primary-color);
  font-size: 1rem;
  font-weight: 600;
}
.pos-cashier-payment-methods .payments {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 0.8rem;
  justify-content: center;
  padding-top: 0.5rem;
}
.pos-cashier-payment-methods .payments .item {
  background-color: #fff;
  box-shadow: -8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(222 222 222 / 25%);
  border: solid 1px #ececec;
  border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -ms-border-radius: 2px;
  -o-border-radius: 2px;
  overflow: hidden;
  cursor: pointer;
  padding: 0.9rem 0.6rem 0.6rem 0.6rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.pos-cashier-payment-methods .payments .item img {
  width: 30px;
  height: 30px;
  object-fit: contain;
  background-color: #fcfcfc;
}
.pos-cashier-payment-methods .payments .item .title {
  font-size: 0.8rem;
  color: var(--text-primary-color);
  line-height: 20px;
  margin-top: 0.5rem;
  text-align: center;
}
.pos-cashier-payment-methods .payments .item.active,
.pos-cashier-payment-methods .payments .item:hover {
  border-color: rgba(229, 0, 64, 0.5);
  background-color: rgba(229, 0, 64, 0.1);
}
.pos-cashier-payment-methods .payments .item:hover .title,
.pos-cashier-payment-methods .payments .item.active .title {
  color: rgba(229, 0, 64, 1);
}
.pos-cashier-payment-summaries {
  margin-top: -0.3rem;
}
.pos-cashier-payment-summaries .nominals {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
  box-shadow: -8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(222 222 222 / 25%);
  border: solid 1px #ececec;
  border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -ms-border-radius: 2px;
  -o-border-radius: 2px;
}
.pos-cashier-payment-summaries .nominals:has(input:not(:disabled):hover),
.pos-cashier-payment-summaries .nominals:has(input:not(:disabled):focus) {
  border-color: rgba(229, 0, 64, 0.5);
}
.pos-cashier-payment-summaries .nominals:not(:first-child) {
  margin-top: 0.6rem;
}
.pos-cashier-payment-summaries .nominals label {
  font-size: 0.8rem;
  height: 36px;
  display: inline-flex;
  align-items: center;
  justify-content: flex-end;
  background-color: #ebebeb;
  font-weight: 600;
  padding: 0 0.7rem;
  color: var(--text-foreign-color);
  width: 150px;
  text-transform: uppercase;
}
.pos-cashier-payment-summaries .nominals input {
  width: 100%;
  text-align: right;
  font-size: 0.9rem;
  font-weight: 600;
  height: 36px;
  padding: 0 0.7rem;
  outline: none;
  border: none;
  color: var(--text-primary-color);
}
.pos-cashier-payment-summaries .payment-input {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin: 1rem 0;
}
.pos-cashier-payment-summaries .payment-input input {
  outline: none;
  border: 1px solid #ececec;
  background-color: #fff;
  box-shadow: -8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(222 222 222 / 25%);
  height: 50px;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--base-color);
  flex: 1;
  padding: 0 1rem;
  text-align: right;
  border-radius: 2px 0px 0px 2px;
  -webkit-border-radius: 2px 0px 0px 2px;
  -moz-border-radius: 2px 0px 0px 2px;
  -ms-border-radius: 2px 0px 0px 2px;
  -o-border-radius: 2px 0px 0px 2px;
}
.pos-cashier-payment-summaries .payment-input input:hover,
.pos-cashier-payment-summaries .payment-input input:focus {
  border: solid 1px var(--base-color);
}
.pos-cashier-payment-summaries .payment-input:has(button:hover) input {
  border: solid 1px var(--base-color);
}
.pos-cashier-payment-summaries .payment-input button {
  height: 50px;
  border-radius: 0px 2px 2px 0px;
  -webkit-border-radius: 0px 2px 2px 0px;
  -moz-border-radius: 0px 2px 2px 0px;
  -ms-border-radius: 0px 2px 2px 0px;
  -o-border-radius: 0px 2px 2px 0px;
  margin-left: 0;
}
.pos-cashier-payment-summaries .payment-nominal {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 0.8rem;
  justify-content: center;
  padding-bottom: 0.5rem;
}
.pos-cashier-payment-summaries .payment-nominal .item {
  background-color: #fff;
  box-shadow: -8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(222 222 222 / 25%);
  border: solid 1px #ececec;
  border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -ms-border-radius: 2px;
  -o-border-radius: 2px;
  overflow: hidden;
  cursor: pointer;
  padding: 1rem;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  color: var(--text-foreign-color);
}
.pos-cashier-payment-summaries .payment-nominal .item:hover {
  border-color: rgba(229, 0, 64, 0.5);
  color: rgba(229, 0, 64, 1);
  font-weight: 600;
}
.pos-cashier-payment-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem 0.2rem;
}
.pos-cashier-payment-results .icons {
  height: 70px;
  width: 70px;
  border-radius: 70px;
  -webkit-border-radius: 70px;
  -moz-border-radius: 70px;
  -ms-border-radius: 70px;
  -o-border-radius: 70px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(39, 174, 96, 0.2);
}
.pos-cashier-payment-results .icons i {
  height: 55px;
  width: 55px;
  font-size: 1.8rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  background-color: rgba(39, 174, 96, 1);
  border-radius: 55px;
  -webkit-border-radius: 55px;
  -moz-border-radius: 55px;
  -ms-border-radius: 55px;
  -o-border-radius: 55px;
  font-weight: 700;
}
.pos-cashier-payment-results h3 {
  color: var(--text-primary-color);
  margin-top: 1rem;
}
.pos-cashier-payment-results h2 {
  color: rgba(39, 174, 96, 1);
}
.pos-cashier-payment-results .infos {
  width: 100%;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}
.pos-cashier-payment-results .infos .info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.4rem 0;
  border-top: dotted 1px rgb(211, 211, 211);
}
.pos-cashier-payment-results .infos .info:first-child {
  border-top: none;
}
.pos-cashier-payment-results .infos .info:last-child {
  border-bottom: none;
}
.pos-cashier-payment-results .infos .info div {
  font-size: 0.8rem;
  color: var(--text-primary-color);
}
.pos-cashier-payment-results button {
  width: 100%;
  border: none;
  background-color: #fff;
  color: var(--text-foreign-color);
  cursor: pointer;
  padding: 0.3rem 0;
  margin-top: 0.8rem;
  border: solid 1px rgb(211, 211, 211);
}
.pos-cashier-payment-results button:hover {
  border-color: rgba(229, 0, 64, 0.5);
  color: rgba(229, 0, 64, 1);
}
.pos-cashier-payment-results button:has(i) span {
  margin-left: 0.5rem;
}
.pos-cashier-payment-results button.new-order {
  background-color: var(--base-color);
  border-color: var(--base-color);
  color: #ffffff;
  border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -ms-border-radius: 2px;
  -o-border-radius: 2px;
  font-weight: 600;
}

.mg-large-top {
  margin-top: 25px;
}

.div-table {
  width: 100%;
  display: table;
  margin-top: 1rem;
}
.div-table .body {
  display: table-row-group;
}
.div-table .body .row {
  display: table-row;
  color: var(--text-primary-color);
  background-color: #ffffff;
}
.div-table .body .row:first-child {
  text-transform: uppercase;
  background-color: #eaeaea;
}
.div-table .body .col {
  display: table-cell;
  border-left: 1px solid #dddddd;
  border-bottom: 1px solid #dddddd;
  padding: 10px;
  vertical-align: middle;
}
.div-table .body .col:last-child {
  border-right: 1px solid #dddddd;
}
.div-table .body .row:first-child .col {
  border-bottom: none;
}

/*?=============== Chips styles */
.chip {
  background-color: #dddddd;
  color: var(--text-primary-color);
  padding: 0.1rem 0.6rem;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
  font-size: 0.8rem;
  text-transform: capitalize;
  font-weight: 600;
}
.chip.quotation {
  background-color: rgba(99, 110, 114, 0.2);
  color: rgba(99, 110, 114, 1);
  border: solid 1px rgba(99, 110, 114, 0.1);
}
.chip.progress {
  background-color: rgba(230, 126, 34, 0.2);
  color: rgba(230, 126, 34, 1);
  border: solid 1px rgba(230, 126, 34, 0.1);
}
.chip.completed {
  background-color: rgba(39, 174, 96, 0.2);
  color: rgba(39, 174, 96, 1);
  border: solid 1px rgba(39, 174, 96, 0.1);
}
.chip.canceled {
  background-color: rgba(231, 76, 60, 0.2);
  color: rgba(231, 76, 60, 1);
  border: solid 1px rgba(231, 76, 60, 0.1);
}

/** line progress bar styles */
.line-bars {
  margin-top: 0.5rem;
  margin-bottom: 0.3rem;
  align-self: flex-start;
  width: 100%;
  flex-grow: 1;
}
.line-bars .line-bar {
  width: 100%;
}
.line-bars .line-bar .title {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--line-bar-color);
  margin: 0.7rem 0;
}
.line-bars .line-bar .value {
  height: 0.8rem;
  background-color: rgba(232, 232, 232, 1);
  border-radius: 0.5rem;
  -webkit-border-radius: 0.5rem;
  -moz-border-radius: 0.5rem;
  -ms-border-radius: 0.5rem;
  -o-border-radius: 0.5rem;
  box-shadow: -8px -8px 12px 0 #f1f1f1, 8px 8px 12px rgb(255 255 255 / 25%);
}
.line-bars .line-bar .value .percentage {
  height: 0.8rem;
  background-color: var(--line-bar-color);
  border-radius: 0.5rem;
  -webkit-border-radius: 0.5rem;
  -moz-border-radius: 0.5rem;
  -ms-border-radius: 0.5rem;
  -o-border-radius: 0.5rem;
  position: relative;
  animation: animLineBarFillIn 2.5s 1;
  -webkit-animation: animLineBarFillIn 2.5s 1;
}
.line-bars .line-bar .value .percentage::before {
  content: attr(line-bar-value);
  position: absolute;
  padding: 2px 6px;
  background-color: var(--line-bar-color);
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  top: -30px;
  right: 0;
  transform: translateX(50%);
  -webkit-transform: translateX(50%);
  -moz-transform: translateX(50%);
  -ms-transform: translateX(50%);
  -o-transform: translateX(50%);
  border-radius: 0.2rem;
  -webkit-border-radius: 0.2rem;
  -moz-border-radius: 0.2rem;
  -ms-border-radius: 0.2rem;
  -o-border-radius: 0.2rem;
}
.line-bars .line-bar .value .percentage::after {
  content: "";
  position: absolute;
  width: 9px;
  height: 9px;
  background-color: var(--line-bar-color);
  top: -14px;
  right: 0;
  transform: translateX(50%) rotate(45deg);
  -webkit-transform: translateX(50%) rotate(45deg);
  -moz-transform: translateX(50%) rotate(45deg);
  -ms-transform: translateX(50%) rotate(45deg);
  -o-transform: translateX(50%) rotate(45deg);
  border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -ms-border-radius: 2px;
  -o-border-radius: 2px;
}

/*! Animation Setup */
@keyframes animLineBarFillIn {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

@media (max-width: 1180px) {
  .modal-content.small {
    width: 38%;
  }
  .modal-content.medium {
    width: 85%;
  }
  nav .navbar .menu li:last-child a .account {
    display: none;
  }
  .not-found .image {
    width: 35%;
  }
}
@media (max-width: 1080px) {
  .modal-content .modal-body:has(table) {
    height: 65.2vh !important;
  }
  .modal-content.small {
    width: 40%;
  }
  .modal-content.medium {
    width: 100%;
    height: 100%;
    border-radius: 0;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    -ms-border-radius: 0;
    -o-border-radius: 0;
  }
  .modal-content.medium.sx {
    width: 80%;
    height: 77vh;
    border-radius: 7px;
    -webkit-border-radius: 7px;
    -moz-border-radius: 7px;
    -ms-border-radius: 7px;
    -o-border-radius: 7px;
  }
  .modal-content.medium .modal-body {
    height: 100%;
    max-height: calc(100vh - 106px);
  }
  nav .navbar .logo span {
    display: none;
  }
  .not-found .image {
    width: 40%;
  }
}
@media (max-width: 1040px) {
  .modal-content.small {
    width: 45%;
  }
  nav .topbar {
    padding: 0.2rem 1.5rem;
  }
  nav .navbar {
    padding: 0 1.5rem;
  }
  nav .navbar .menu li .icon-menu {
    visibility: hidden;
  }
  nav .navbar .menu ul {
    gap: 0.2rem;
  }
  nav .navbar .menu li:last-child {
    padding-left: 1.5rem;
  }
  .containers {
    padding: 0rem 1.5rem;
  }
}
@media (max-width: 920px) {
  .modal-content.small {
    width: 50%;
  }
  nav .navbar .menu ul {
    gap: 0.1rem;
  }
  .not-found .image {
    width: 45%;
  }
}
@media (max-width: 880px) {
  .modal-content {
    border-radius: 0;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    -ms-border-radius: 0;
    -o-border-radius: 0;
  }
  .modal-content.small {
    width: 100%;
    height: 100%;
  }
  .modal-content.medium.sx {
    width: 100%;
    height: 100%;
    border-radius: 0;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    -ms-border-radius: 0;
    -o-border-radius: 0;
  }
  .modal-content.dialog {
    width: 80%;
    height: auto;
    border-radius: 7px;
    -webkit-border-radius: 7px;
    -moz-border-radius: 7px;
    -ms-border-radius: 7px;
    -o-border-radius: 7px;
  }
  .modal-content.dialog.sx {
    width: 80%;
    height: auto;
    border-radius: 7px;
    -webkit-border-radius: 7px;
    -moz-border-radius: 7px;
    -ms-border-radius: 7px;
    -o-border-radius: 7px;
  }
  .modal-content .modal-body {
    height: 100%;
    max-height: calc(100vh - 106px);
  }
  .modal-content.dialog .modal-body {
    height: auto;
  }
  .modal-content .modal-body:has(.flex-rows .row .box) {
    background-color: #f7f6f6;
  }
  .modal-content .modal-body:has(table) {
    height: 100vh !important;
  }
  .modal-content:has(table) {
    width: 100%;
  }
  .modal-content.large {
    width: 100%;
  }
  .form-table {
    display: block;
  }
  .form-table .form {
    width: 100%;
    padding-right: 1.2rem;
    overflow: hidden;
    height: auto;
  }

  .form-table .form:last-child {
    padding-left: 1.2rem;
  }
  .form-table .table {
    width: 100%;
    height: auto;
    overflow: hidden;
  }
  .form-table .table .top {
    padding-top: 0rem;
    padding-left: 1.2rem;
    padding-right: 1.2rem;
  }
  .form-table .table .bottom {
    padding: 0 1rem;
  }
  .form-table .table .bottom .flex-rows {
    flex-direction: row;
  }
  .modal-content .modal-body:has(.form-table) {
    overflow: auto;
  }
  .form-table:has(.form.right) .table {
    width: 100%;
    min-height: 35%;
  }

  .flex-rows {
    flex-direction: column;
  }
  .flex-rows.row {
    flex-direction: row;
  }
  .flex-rows.no-gap {
    gap: 1.2rem;
  }
  .flex-rows .row:not(:last-child) {
    border-right: none;
    padding-right: 0rem;
  }
  .flex-rows .row.wd60 {
    width: 100%;
  }
  .flex-rows .row .box {
    border-radius: 0;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    -ms-border-radius: 0;
    -o-border-radius: 0;
    margin-left: -1rem;
    margin-right: -1rem;
    border-left: none;
    border-right: none;
    background-color: #fff;
    padding: 1.2rem 1rem;
  }
  .flex-rows:has(.row .box) {
    margin-bottom: -1.5rem;
    margin-top: -1.5rem;
  }
  .flex-rows .row .box .title {
    text-align: center;
  }

  nav .topbar,
  nav .navbar .menu .profile {
    display: none;
  }
  nav .navbar {
    height: var(--navbar-height);
  }
  nav .navbar .title {
    display: block;
    font-size: 1rem;
    font-weight: 600;
    color: var(--navbar-primary-color);
  }
  nav .navbar .menu .app-name {
    display: flex;
    flex-direction: row;
    align-items: center;
    background-color: var(--topbar-background);
    justify-content: space-between;
    color: #ffffff;
    padding: 1.07rem 1.1rem;
    position: sticky;
    top: 0;
    z-index: 9999;
    font-weight: 600;
  }
  nav .navbar .menu .app-name .btn-menu-close {
    cursor: pointer;
  }
  nav .navbar .menu li .icon-menu {
    visibility: visible;
    padding-right: 0.4rem;
    font-size: 1.2rem;
  }
  nav .navbar .btn-menu {
    cursor: pointer;
    color: var(--navbar-primary-color);
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  nav .navbar .btn-menu i {
    font-size: 2rem;
  }
  nav .navbar .menu {
    margin-left: 0;
    position: fixed;
    top: 0;
    left: -100%;
    display: block;
    max-width: 340px;
    width: 100%;
    height: 100vh;
    background-color: var(--navbar-background);
    transition: all 0.3s ease;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    overflow-y: auto;
    border-right: solid 1px var(--navbar-sub-menu-border-color);
    z-index: 9999;
  }
  nav .navbar .menu::-webkit-scrollbar {
    width: 0;
    height: 0;
  }
  nav .navbar .menu::-webkit-scrollbar-thumb {
    border-radius: 0.2rem;
    -webkit-border-radius: 0.2rem;
    -moz-border-radius: 0.2rem;
    -ms-border-radius: 0.2rem;
    -o-border-radius: 0.2rem;
    background-color: var(--sidebar-menu-active-border-color);
    visibility: hidden;
  }
  nav .navbar .menu ul {
    display: flex;
    flex-direction: column;
    list-style: none;
    color: var(--text-primary-color);
    gap: 0rem;
  }
  nav .navbar .menu ul li {
    display: block;
    height: auto;
    border-bottom: none;
  }
  nav .navbar .menu li:last-child {
    border-top: solid 1px var(--navbar-sub-menu-border-color);
  }
  nav .navbar .menu li:not(:first-child) {
    border-top: solid 30px #00000036;
  }
  nav .navbar .menu li a {
    padding: 1.1rem 1.1rem;
  }
  nav .navbar .menu li a span {
    flex-grow: 1;
  }
  nav .navbar .menu li a i {
    margin-right: 9px;
  }
  nav .navbar .menu li .arrow {
    visibility: hidden;
  }
  nav .navbar .menu .sub-menu {
    position: relative;
    display: block;
    top: 0;
    border: none;
    padding-left: 54px;
    background-color: var(--navbar-background);
  }
  nav .navbar .menu .sub-menu > li {
    padding: 0;
    cursor: pointer;
    height: auto;
    position: relative;
    border-left: solid 1px rgb(255 255 255 / 11%);
  }
  nav .navbar .menu .sub-menu > li::before {
    content: "";
    position: absolute;
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 1rem;
    -webkit-border-radius: 1rem;
    -moz-border-radius: 1rem;
    -ms-border-radius: 1rem;
    -o-border-radius: 1rem;
    top: 25px;
    background-color: #ffffff36;
    left: -4.5px;
  }
  nav .navbar .menu:hover::-webkit-scrollbar-thumb,
  nav .navbar .menu .sub-menu li .arrow {
    visibility: visible;
  }
  nav .navbar .menu .sub-menu > li:first-child,
  nav .navbar .menu .sub-menu .more-menu > li:first-child {
    margin-top: -8px;
  }
  nav .navbar .menu .sub-menu li,
  nav .navbar .menu .sub-menu li:last-child {
    border-bottom: none;
    border-top: none;
  }
  nav .navbar .menu .sub-menu .more-menu li a {
    padding: 0.9rem 1.1rem 0.9rem 0rem;
  }
  nav .navbar .menu .sub-menu .more-menu {
    position: relative;
    left: 0;
    border: none;
    margin-left: 38px;
    background-color: var(--navbar-background);
  }
  nav .navbar .menu .sub-menu .more-menu > li {
    padding: 0;
    cursor: pointer;
    height: auto;
    position: relative;
  }
  nav .navbar .menu .sub-menu .more-menu > li::before {
    content: "\00BB";
    position: absolute;
    top: 9px;
    left: -22px;
    font-size: 20px;
    color: #ffffff36;
  }
  nav .navbar .menu .sub-menu .more-menu > li::after {
    content: "";
    position: absolute;
    width: 19px;
    height: 1px;
    top: 26px;
    background-color: rgb(0 0 0 / 7%);
    left: -38px;
  }
  nav .navbar .menu .sub-menu > li:hover::before,
  nav .navbar .menu .sub-menu > li.active::before {
    background-color: #ffffff;
  }
  nav .navbar .menu .sub-menu .more-menu > li:hover::before,
  nav .navbar .menu .sub-menu .more-menu > li.active::before {
    color: #ffffff;
  }
  nav .navbar .menu .sub-menu li.active .more-menu {
    display: block;
  }

  .containers {
    height: calc(100vh - 60px);
    background-color: white;
    padding: 0 1.5rem 1.5rem;
  }
  .breadcrumb {
    display: none;
  }
  .contents {
    padding-top: 1.2rem;
  }
  .not-found .image {
    width: 50%;
  }

  .contents:has(.tab-bar) {
    padding-top: 0;
  }
  .contents .side-bars {
    display: none;
  }
  .tab-bar {
    border-bottom: solid 1px #eeeeee;
  }
  .tab-bar .item.selected,
  .tab-bar .item {
    border-top: none;
  }

  .option-selector {
    flex-direction: column;
    gap: 0;
    padding: 0;
  }
  .option-selector input {
    width: 100%;
  }
  .flex-rows.beetween {
    flex-direction: row;
  }

  .not-alowed-device {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: calc(100vh - 150px);
    margin-top: -1rem;
    padding: 0 2.5rem;
    text-align: center;
  }

  .mg-large-top {
    margin-top: 0px;
  }

  .button.responsive span {
    display: none !important;
  }
  .button.responsive i {
    margin-right: 0px !important;
  }
}
@media (max-width: 340px) {
  nav .navbar .menu {
    width: 100%;
  }
  .not-found .image {
    width: 60%;
  }
}

.disabled_opacity {
  opacity: 0.3 !important;
}

.sticky_thead {
  z-index: 2 !important;
}

.bg-white {
  background-color: white;
}
