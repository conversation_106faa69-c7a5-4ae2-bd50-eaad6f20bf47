import React from "react";
import { useTheme } from "@mui/material/styles";
import useMediaQuery from "@mui/material/useMediaQuery";

// Higher-Order Component (HOC) to pass responsive props
const ResponsiveWrapperHOC = (WrappedComponent, options) => {
  return function WithResponsiveProps(props) {
    const theme = useTheme();

    // Define breakpoints using MUI's theme breakpoints
    const isSm = useMediaQuery(theme.breakpoints.between("xs", "sm"), {
      noSsr: true,
    });
    const isMobile = useMediaQuery("(max-width:899px)", {
      noSsr: true,
    });

    // Custom responsive props
    const responsiveProps = {
      // padding: isXs ? "8px" : isSm ? "16px" : isMd ? "24px" : "32px",
      // fontSize: isXs ? "0.8rem" : isSm ? "1rem" : isMd ? "1.2rem" : "1.5rem",
      // color: isXs ? "blue" : isSm ? "green" : isMd ? "orange" : "red",
      isSm,
      isMobile,
    };

    return (
      <WrappedComponent
        {...props} // Pass original props
        {...options} // Pass additional options (like redirectTo)
        responsive={responsiveProps} // Pass the responsive props to the child
      />
    );
  };
};

export default ResponsiveWrapperHOC;
