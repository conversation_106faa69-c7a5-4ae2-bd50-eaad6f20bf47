/*
Created by esoda
Created on Apr, 2025
Contact esoda.id
-- Styled for all pos page
*/

/*? =============================================================== */
/*? LOGIN PAGE SECTIONS =========================================== */
/*? =============================================================== */
.lg_containers { width: 100%; display: grid; text-align: center; background-color: white; position: relative; }
.lg_containers .contents {
    width: 450px;
    height: calc(100vh);
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    justify-self: center;
}
.lg_containers .contents .form {
    background-color: white;
    box-shadow: -5px -5px 50px 0 #f1f1f1, 8px 8px 12px rgb(255 255 255 / 20%);
    border-radius: 5px;
    padding: 2rem 2rem;
}
.lg_containers .contents .form img { width: 100px; }
.lg_containers .contents .form h1 { font-weight: 500; font-size: 2.5rem; color: #2f3640; text-align: left; }
.lg_containers .contents .form h3 { font-weight: 500; font-size: 1rem; line-height: 1.3rem; color: #7a7a7a; margin-bottom: 2rem; text-align: left; }
.lg_containers .contents .form .inputs { display: flex; flex-direction: row; align-items: center; margin-top: 1.3rem; border: solid 1px #dddddd; border-radius: 3px; }
.lg_containers .contents .form .inputs .label,
.lg_containers .contents .form .inputs input,
.lg_containers .contents .form .inputs button { height: 45px; border: none; border-radius: 0; background-color: #FFFFFF; }
.lg_containers .contents .form .inputs .errIcon {
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: #e74c3c;
    cursor: pointer;
    position: relative;
    visibility: hidden;
}
.lg_containers .contents .form .inputs .errIcon i { animation: errorIconAnim 1s ease 1s 50 normal forwards; }
.lg_containers .contents .form .inputs .errIcon span {
    width: 150px;
    height: auto;
    background-color: #e74c3c;
    padding: .3rem .5rem;
    font-size: .75rem;
    color: #FFFFFF;
    position: absolute;
    left: 60px;
    top: 0;
    line-height: 1rem;
    border-radius: 5px;
    text-align: left;
}
.lg_containers .contents .form .inputs .errIcon::before { content: "\25C0"; left: 48px; top: 6px; position: absolute; }
.lg_containers .contents .form .inputs.errors .errIcon { visibility: visible; }
.lg_containers .contents .form .inputs .label,
.lg_containers .contents .form .inputs button { width: 45px; display: flex; align-items: center; justify-content: center; }
.lg_containers .contents .form .inputs .label i { font-size: 1.1rem; color: #808080; }
.lg_containers .contents .form .inputs button i { font-size: 1.2rem; color: #2f3640; }
.lg_containers .contents .form .inputs button:hover i { color: var(--base-color); }
.lg_containers .contents .form .inputs input { flex-grow: 1; color: #2f3640; font-size: 1rem; }
.lg_containers .contents .form .inputs input:focus { outline: none; }
.lg_containers .contents .form .inputs input::placeholder { color: #c1c1c1; }
.lg_containers .contents .form .inputs button { cursor: pointer; }
.lg_containers .contents .form .submit {
    height: 45px;
    padding: 0 1.5rem;
    font-size: 1.1rem;
    background-color: var(--base-color);
    color: #FFFFFF;
    border: solid 1px var(--base-color);
    border-radius: 3px;
    margin-top: 1.5rem;
    width: 100%;
    cursor: pointer;
}
.lg_containers .contents .form .submit:hover { background-color: #FFFFFF; color: var(--base-color); border: solid 1px var(--base-color); }
.lg_containers .contents .form .inputs:has(input:hover) { border-color: var(--base-color); }
.lg_containers .contents .form .inputs:has(input:focus) { border-color: #2f3640; }
.lg_containers .contents .form .inputs:has(input:hover)>.label i { color: var(--base-color); }
.lg_containers .contents .form .inputs:has(input:focus)>.label i { color: #2f3640; }
.lg_containers .contents .form .info { padding-top: 1.2rem; text-align: center; }

/*? =============================================================== */
/*? CONTENTS PAGE SECTIONS ======================================== */
/*? =============================================================== */
.ct_containers { width: 100%; height: 100vh; display: grid; background-color: white; overflow: auto; }
.ct_containers .topbar {
    width: 100%;
    position: sticky;
    top: 0;
    left: 0;
    background-color: white;
    padding: 1rem 12.5%;
    z-index: 90;
}
.ct_containers:has(.contents.pages) .topbar,
.ct_containers:has(.contents.sidebar) .topbar,
.ct_containers:has(.contents.cashier) .topbar { border-bottom: solid 1px rgb(247, 247, 247); height: 70px; }
.ct_containers:has(.contents.cashier) .topbar { padding: 1rem 13.4%; }
.ct_containers .topbar,
.ct_containers .topbar .left,
.ct_containers .topbar .right { display: flex; flex-direction: row; align-items: center; justify-content: space-between; gap: 1.2rem; }
.ct_containers .topbar .left h3,
.ct_containers .topbar .right h3 { line-height: 1.2; font-weight: 600; color: #2f3640; }
.ct_containers .topbar .left { gap: .9rem; text-align: left; }
.ct_containers .topbar .left p { line-height: 1; font-size: .9rem; line-height: 1.4; color: #808080; }
.ct_containers .topbar .left img { width: 40px; }
.ct_containers .topbar .right { text-align: right; gap: .9rem; }
.ct_containers .topbar .right .timepicker { line-height: 1.4; text-align: right; font-size: .9rem; color: #808080; }
.ct_containers .topbar .right button {
    background-color: white;
    border: solid 1px #e74c3c;
    color: #e74c3c;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
    cursor: pointer;
}
.ct_containers .topbar .right button i { font-size: 1.2rem; }
.ct_containers .topbar .right button:hover { background-color: #e74c3c; color: white; }
.ct_containers .topbar .right button.profile { border-color: #2f3640; color: #2f3640; }
.ct_containers .topbar .right button.profile:hover { background-color: #2f3640; color: white; }
.ct_containers .contents { width: 75%; justify-self: center; padding-bottom: 2rem; }
.ct_containers .contents .menus { padding-top: 1.5rem; display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 19.2px; }
.ct_containers .contents .menus.second { padding-top: 1.2rem; grid-template-columns: calc(25% - 14px) calc(25% - 14px) 1fr; }
.ct_containers .contents .menus.third { padding-top: 1.2rem; grid-template-columns: 1fr calc(25% - 14px) calc(25% - 14px); }
.ct_containers .contents .menus .items {
    background-color: whitesmoke;
    border: solid 1px rgb(240, 240, 240);
    padding: 1.5rem;
    height: 185px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #2f3640;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
    position: relative;
    overflow: hidden;
}
.ct_containers .contents .menus .items img { width: 50px; height: 50px; object-fit: contain; }
.ct_containers .contents .menus .items i { font-size: 2rem; }
.ct_containers .contents .menus .items span { font-size: 1rem; margin-top: .5rem; line-height: 1; text-align: center; font-weight: 600; }
.ct_containers .contents .menus .items.pos { gap: .3rem; }
.ct_containers .contents .menus .items.pos i { font-size: 3rem; }
.ct_containers .contents .menus .items.pos img { width: 70px; height: 70px; object-fit: contain; }
.ct_containers .contents .menus .items.pos span { font-size: 2rem; font-weight: 600; }
.ct_containers .contents .menus .items.trx img { width: 32px; height: 32px; object-fit: contain; margin-bottom: 1rem; }
.ct_containers .contents .menus .items.trx p { font-size: 1.5rem; line-height: 1; color: rgb(139, 139, 139); }
.ct_containers .contents .menus .items.trx span { font-size: 2rem; font-weight: 600; }
.ct_containers .contents .menus .items:hover { background-color: white; border-color: var(--base-color); }
.ct_containers .contents .menus .items:hover span { color: var(--base-color); }

/*? =============================================================== */
/*? CONTENTS PAGE WITH SIDEBAR SECTIONS =========================== */
/*? =============================================================== */
.ct_containers .contents.sidebar {
    height: calc(100vh - 70px);
    display: flex;
    background-color: white;
    overflow: hidden;
    border-left: solid 1px rgb(247, 247, 247);
    border-right: solid 1px rgb(247, 247, 247);
}
.ct_containers .contents.sidebar .nav { width: 270px; height: calc(100vh - 70px); overflow: auto; position: relative; }
.ct_containers .contents.sidebar .nav .items { border-top: solid 1px rgb(249, 249, 249); line-height: 1.2; }
.ct_containers .contents.sidebar .nav .items:has(ul) { border-top: solid 10px rgb(249, 249, 249); }
.ct_containers .contents.sidebar .nav .items .menu {
    display: flex;
    align-items: center;
    padding: 1rem;
    text-wrap: nowrap;
    cursor: pointer;
    color: #2f3640;
    font-size: 1rem;
}
.ct_containers .contents.sidebar .nav .items:has(ul) .menu { cursor: default; padding-bottom: .4rem; }
.ct_containers .contents.sidebar .nav .items:has(ul) .menu:hover { color: #2f3640; }
.ct_containers .contents.sidebar .nav .items .menu i { margin-right: .8rem; }
.ct_containers .contents.sidebar .nav .items:first-child { border-top: none; }
.ct_containers .contents.sidebar .nav .items ul { list-style: none; padding-left: 3rem; }
.ct_containers .contents.sidebar .nav .items ul li {
    width: 100%;
    padding: 1rem;
    position: relative;
    border-left: solid 1px rgb(244, 244, 244);
    border-top: solid 1px rgb(244, 244, 244);
    color: #2f3640;
    cursor: pointer;
    font-size: .95rem;
}
.ct_containers .contents.sidebar .nav .items ul li::before {
    content: "";
    position: absolute;
    width: 8px;
    height: 8px;
    background-color: white;
    border: solid 1px rgb(226, 226, 226);
    left: -5px;
    top: 39%;
    border-radius: 8px;
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    -ms-border-radius: 8px;
    -o-border-radius: 8px;
}
.ct_containers .contents.sidebar .nav .items .menu:hover,
.ct_containers .contents.sidebar .nav .items .menu.active,
.ct_containers .contents.sidebar .nav .items ul li:hover,
.ct_containers .contents.sidebar .nav .items ul li.active { color: var(--base-color); }
.ct_containers .contents.sidebar .nav .items ul li:hover::before { border-color: var(--base-color); }
/* .ct_containers .contents.sidebar .nav .items ul li.active, 
.ct_containers .contents.sidebar .nav .items .menu.active { border-right: solid 2px var(--base-color); } */
.ct_containers .contents.sidebar .nav .items ul li:first-child { border-top: none; }
.ct_containers .contents.sidebar .nav .items .menu.active, 
.ct_containers .contents.sidebar .nav .items ul li.active { font-weight: 600; }

.ct_containers .contents.sidebar .ctx {
    width: calc(100% - 270px);
    height: calc(100vh - 70px);
    overflow: auto;
    border-left: solid 1px rgb(247, 247, 247);
}
.tableCustomStyle {
    border: none;
    border-radius: 0;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    -ms-border-radius: 0;
    -o-border-radius: 0;
}

/*? =============================================================== */
/*? CONTENTS CASHIER SECTIONS ===================================== */
/*? =============================================================== */
.ct_containers .contents.cashier {
    height: calc(100vh - 70px);
    display: flex;
    background-color: white;
    overflow: hidden;
    border-left: solid 1px rgb(247, 247, 247);
    border-right: solid 1px rgb(247, 247, 247);
}
.ct_containers .contents.cashier .trx {
    width: calc(100% - 460px);
    height: calc(100vh - 70px);
    overflow: auto;
    border-right: solid 1px rgb(247, 247, 247);
    display: flex;
    flex-direction: column;
}
.ct_containers .contents.cashier .trx .searchbar { display: flex; flex-direction: row; align-items: center; gap: .8rem; padding: .75rem 1rem;
}
.ct_containers .contents.cashier .trx .searchbar a {
    height: 36px;
    width: 36px;
    border-radius: 4px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    -ms-border-radius: 4px;
    -o-border-radius: 4px;
    background-color: #2d3436;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    cursor: pointer;
}
.ct_containers .contents.cashier .trx .searchbar a:not(:first-child) { margin-left: 0; }
.ct_containers .contents.cashier .trx .searchbar a i { font-size: 1.4rem; color: #FFFFFF; }
.ct_containers .contents.cashier .trx .searchbar .input_search {
    flex-grow: 1;
    display: flex;
    align-items: center;
    border: 1px solid #ececec;
    background-color: #fff;
    border-radius: 2px;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    -ms-border-radius: 2px;
    -o-border-radius: 2px;
    overflow: hidden;
}
.ct_containers .contents.cashier .trx .searchbar .input_search label {
    flex-shrink: 0;
    height: 36px;
    width: 36px;
    background-color: #fff;
    color: #2d3436;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-size: 1.2rem;
    font-weight: 500;
    padding-right: 5px;
}
.ct_containers .contents.cashier .trx .searchbar .input_search input {
    box-sizing: border-box;
    flex-grow: 1;
    min-width: 0;
    height: 36px;
    padding: 1em;
    font: inherit;
    transition: 150ms ease;
    -webkit-transition: 150ms ease;
    -moz-transition: 150ms ease;
    -ms-transition: 150ms ease;
    -o-transition: 150ms ease;
    background-color: #fff;
    border: none;
    outline: none;
}
.ct_containers .contents.cashier .trx .searchbar .input_search:has(input:hover)>label,
.ct_containers .contents.cashier .trx .searchbar .input_search:has(input:focus)>label { color: var(--base-color); }
.ct_containers .contents.cashier .trx .searchbar .input_search:has(input:hover),
.ct_containers .contents.cashier .trx .searchbar .input_search:has(input:focus) { border-color: rgba(229, 0, 64, .5); }
.ct_containers .contents.cashier .trx .carts {
    flex-grow: 1;
    margin: 0 1rem;
    background-color: #f3f3f3;
    overflow: auto;
    border-radius: 4px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    -ms-border-radius: 4px;
    -o-border-radius: 4px;
}
.ct_containers .contents.cashier .trx .carts table { width: 100%; }
.ct_containers .contents.cashier .trx .carts table,
.ct_containers .contents.cashier .trx .carts th,
.ct_containers .contents.cashier .trx .carts td { color: #2d3436; border-collapse: collapse; text-align: left; font-size: .95rem; font-weight: normal; line-height: 1.2; }
.ct_containers .contents.cashier .trx .carts thead th {
    padding: .6rem 1rem;
    font-size: .9rem;
    z-index: 1;
    position: sticky;
    top: 0;
    left: 0;
    background-color: #2d3436;
    color: #FFFFFF;
    font-weight: 600;
    text-transform: uppercase;
}
.ct_containers .contents.cashier .trx .carts td { padding: 1rem 1rem; background-color: #FFFFFF; }
.ct_containers .contents.cashier .trx .carts tr:nth-child(odd) td { background-color: #f9f9f9; }
.ct_containers .contents.cashier .trx .carts table td .rows { display: flex; flex-direction: row; align-items: center; justify-content: space-between; }
.ct_containers .contents.cashier .trx .carts table td .rows img { width: 95px; height: 95px; object-fit: contain; background-color: #ffffff; border: solid 1px #e7e7e7; border-radius: 2px; }
.ct_containers .contents.cashier .trx .carts table td .rows div { font-weight: 600; flex-grow: 1; padding-left: 1rem; display: flex; flex-direction: column; }
.ct_containers .contents.cashier .trx .carts table td .rows div span { display: flex; flex-direction: row; align-items: center; padding-top: .1rem; }
.ct_containers .contents.cashier .trx .carts table td .rows div span input { outline: none; border: none; font-size: .8rem; flex: 1; padding-left: .3rem; background-color: inherit; }
.ct_containers .contents.cashier .trx .carts table table tr td:first-child { position: sticky; top: 0; left: 0; }
.ct_containers .contents.cashier .trx .carts table td .rows div span input::placeholder { color: rgb(197, 197, 197); }
.ct_containers .contents.cashier .trx .carts table td .rows div span i { color: rgb(127, 127, 127); font-size: 14px; }
.ct_containers .contents.cashier .trx .carts table td .rows div span:has(input:hover) i,
.ct_containers .contents.cashier .trx .carts table td .rows div span:has(input:focus) i { color: rgba(229, 0, 64, 1); }
.ct_containers .contents.cashier .trx .carts .qty_price { display: flex; flex-direction: row; align-items: center; }
.ct_containers .contents.cashier .trx .carts .qty_price button {
    width: 28px;
    height: 28px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    -ms-border-radius: 2px;
    -o-border-radius: 2px;
}
.ct_containers .contents.cashier .trx .carts .qty_price button i { margin-right: 0; font-size: 1rem; }
.ct_containers .contents.cashier .trx .carts .qty_price>input {
    width: 50px;
    height: 28px;
    margin-left: 10px;
    border: solid 1px #c8c8c8;
    border-radius: 2px;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    -ms-border-radius: 2px;
    -o-border-radius: 2px;
    padding: 0 .3rem;
    outline: none;
}
.ct_containers .contents.cashier .trx .summaries { padding: .8rem 1rem; position: sticky; bottom: 0; z-index: 1; background-color: #fcfcfc; }
.ct_containers .contents.cashier .sum { width: 460px; height: calc(100vh - 70px); overflow: auto; position: relative; }
.ct_containers .contents.cashier .sum .customers,
.ct_containers .contents.cashier .sum .poin {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: .6rem;
    padding: .8rem 1rem;
    border-bottom: solid 1px rgb(247, 247, 247);
}
.ct_containers .contents.cashier .sum .customers { position: sticky; top: 0; left: 0; background-color: white; }
.ct_containers .contents.cashier .sum .customers img {
    width: 36px;
    height: 36px;
    object-fit: cover;
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -ms-border-radius: 3px;
    -o-border-radius: 3px;
}
.ct_containers .contents.cashier .sum .customers .info { flex-grow: 1; }
.ct_containers .contents.cashier .sum .customers .info h3 { line-height: 1; font-weight: 600; }
.ct_containers .contents.cashier .sum .customers .info p { font-size: .9rem; line-height: 1; margin-top: .2rem; }
.ct_containers .contents.cashier .sum .customers button {
    width: 34px;
    height: 34px;
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -ms-border-radius: 3px;
    -o-border-radius: 3px;
    background-color: #fff3f6;
    border: solid 1px var(--base-color);
    color: var(--base-color);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}
.ct_containers .contents.cashier .sum .customers button:hover { background-color: var(--base-color); color: white; }
.ct_containers .contents.cashier .sum .poin { padding: .6rem 1rem; }
.ct_containers .contents.cashier .sum .poin p,
.ct_containers .contents.cashier .sum .poin span { font-size: .9rem; line-height: 1; font-size: .9rem; text-transform: uppercase; }
.ct_containers .contents.cashier .sum .poin p { flex-grow: 1; }
.ct_containers .contents.cashier .sum .poin span { color: var(--base-color); text-decoration: underline; cursor: pointer; }
.ct_containers .contents.cashier .sum .poin h3 { line-height: 1; font-weight: 700; font-size: 1rem; color: #27ae60; }
.ct_containers .contents.cashier .sum .payments {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(133px, 1fr));
    gap: .8rem;
    justify-content: center;
    padding: 1rem 1rem 0;
}
.ct_containers .contents.cashier .sum .payments .items {
    background-color: #fff;
    box-shadow: -8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(222 222 222 / 25%);
    border: solid 1px #ececec;
    border-radius: 2px;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    -ms-border-radius: 2px;
    -o-border-radius: 2px;
    overflow: hidden;
    cursor: pointer;
    padding: .6rem;
    display: flex;
    flex-direction: column;
    align-items: center;
}
.ct_containers .contents.cashier .sum .payments .items img { width: 24px; height: 24px; object-fit: contain; }
.ct_containers .contents.cashier .sum .payments .items .title {
    font-size: .8rem;
    color: #2f3640;
    text-align: center;
    text-transform: uppercase;
    line-height: 1;
    margin-top: .4rem;
    width: 100%;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}
.ct_containers .contents.cashier .sum .payments .items.active,
.ct_containers .contents.cashier .sum .payments .items:hover { border-color: rgba(229, 0, 64, .5); background-color: rgba(229, 0, 64, .1); }
.ct_containers .contents.cashier .sum .payments .items:hover .title,
.ct_containers .contents.cashier .sum .payments .items.active .title { color: rgba(229, 0, 64, 1); }
.ct_containers .contents.cashier .inputs {
    height: 38px;
    border: solid 1px #e8e8e8;
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -ms-border-radius: 3px;
    -o-border-radius: 3px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    overflow: hidden;
    gap: 0;
}
.ct_containers .contents.cashier .inputs.grant { height: 50px; }
.ct_containers .contents.cashier .inputs:not(:first-child) { margin-top: .6rem; }
.ct_containers .contents.cashier .inputs label {
    background-color: #f5f5f5;
    height: 38px;
    width: 133px;
    min-width: 133px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0 .6rem;
    text-align: right;
    text-transform: uppercase;
    font-size: .8rem;
    font-weight: 600;
    border-right: solid 1px #e8e8e8;
    color: #646464;
}
.ct_containers .contents.cashier .inputs input {
    flex-grow: 1;
    height: 38px;
    border: none;
    outline: none;
    padding: 1rem .8rem;
    text-align: right;
    font-weight: 600;
    font-size: 1rem;
}
.ct_containers .contents.cashier .inputs input::placeholder { color: #dedede; font-weight: normal; font-size: 1rem; }
.ct_containers .contents.cashier .inputs.grant input { height: 50px; font-size: 1.8rem; color: var(--base-color); }
.ct_containers .contents.cashier .inputs.grant input::placeholder { color: #dedede; font-weight: 600; font-size: 1.8rem; }
.ct_containers .contents.cashier .inputs button {
    height: 38px;
    width: 40px;
    min-width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    border-right: solid 1px #e8e8e8;
    color: #2f3640;
    cursor: pointer;
}
.ct_containers .contents.cashier .inputs button.clear { background-color: #fde8ee; border-right: none; color: var(--base-color); font-weight: 600; }
.ct_containers .contents.cashier .inputs button.all { background-color: #c1f4d7; border-right: none; color: #27ae60; font-weight: 600; }
.ct_containers .contents.cashier .inputs button.same {
    background-color: var(--base-color);
    text-transform: uppercase;
    border-right: none;
    color: white;
    font-weight: 600;
    width: 65px;
    height: 50px;
}
.ct_containers .contents.cashier .inputs:has(input:not(:disabled):hover),
.ct_containers .contents.cashier .inputs:has(input:not(:disabled):focus) { border-color: rgba(229, 0, 64, .5); }
.ct_containers .contents.cashier .sum .payment_nominal {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(133px, 1fr));
    gap: .8rem;
    justify-content: center;
    padding-top: 1rem;
}
.ct_containers .contents.cashier .sum .payment_nominal .item {
    background-color: #fff;
    box-shadow: -8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(222 222 222 / 25%);
    border: solid 1px #ececec;
    border-radius: 2px;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    -ms-border-radius: 2px;
    -o-border-radius: 2px;
    overflow: hidden;
    cursor: pointer;
    padding: .5rem 1rem;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    font-size: .9rem;
    color: #808080;
}
.ct_containers .contents.cashier .sum .forms .payment_nominal .item:hover { border-color: rgba(229, 0, 64, .5); color: rgba(229, 0, 64, 1); font-weight: 600; }
.ct_containers .contents.cashier .sum .payment_action {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: .8rem 1rem;
    border-top: solid 1px rgb(247, 247, 247);
    position: sticky;
    bottom: 0;
    left: 0;
    background-color: white;
}
.ct_containers .contents.cashier .sum .payment_action button {
    align-items: center;
    justify-content: center;
    border-radius: 2px;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    -ms-border-radius: 2px;
    -o-border-radius: 2px;
    height: 45px;
    width: 45px;
}
.ct_containers .contents.cashier .sum .payment_action button:first-child { flex-grow: 1; }
.ct_containers .contents.cashier .sum .payment_action button:not(:first-child) i { margin-right: 0; }
.ct_containers .contents.cashier .sum .payment_action button:not(:first-child) span { display: none; }

/*? =============================================================== */
/*? CONTENTS PAGE SECTIONS ======================================== */
/*? =============================================================== */
.ct_containers .contents.pages {
    height: calc(100vh - 70px);
    overflow: auto;
}

@keyframes errorIconAnim { 0%, 50%, 100% { opacity: 1; } 25%, 75% { opacity: 0; }}

@media only screen and (max-width: 1024px) {
    .ct_containers .topbar { padding: 1rem 7.5%; }
    .ct_containers .contents { width: 85%; justify-self: center; padding-bottom: 2rem; }
}

@media only screen and (max-width: 800px) {
    .lg_containers .contents { width: 50%; }
}

@media only screen and (max-width: 700px) {
    .lg_containers .contents {  width: 60%; }
}

@media only screen and (max-width: 600px) {
    .lg_containers .contents { width: 90%; }
    .lg_containers .contents .form { padding: 1.5rem; }
    .lg_containers .contents .form .info { padding-top: .5rem; }
    .lg_containers .contents .form .inputs .errIcon span,
    .lg_containers .contents .form .inputs .errIcon::before { visibility: hidden; }
    .lg_containers .contents .form .inputs .errIcon:hover span,
    .lg_containers .contents .form .inputs .errIcon:hover:before { visibility: visible; }
    .lg_containers .contents .form .inputs .errIcon span { left: -154px; width: 200px; height: fit-content; text-align: right; top: 55px; }
    .lg_containers .contents .form .inputs .errIcon::before { content: "\25B2"; left: 10px; top: 40px; }
}