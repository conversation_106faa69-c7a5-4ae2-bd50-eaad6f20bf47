/*
Created by esoda
Created on Nov, 2024
Contact esoda.id
*/

import React from "react";
import style from "@/styles/Table.module.css";
import Pagination from '@mui/material/Pagination';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';
import MenuItem from '@mui/material/MenuItem';
import Menu from '@mui/material/Menu';
import Button from '@mui/material/Button';

export default class Table extends React.Component {
    constructor(props) {
        super(props)
        this.state = { 
            dataHeadings: this.props.dataHeadings,

            pageCount: this.props.pageCount,
            pageSelected: this.props.pageSelected,
            pageRow: this.props.pageRow || 10,
            pageRowIsOpen: false,
            pageRowOptions: [10, 20, 50, 100],

            inputSearch: ""
        }
    }
    // ====================================================================================
    // ========== INITIALIZE, GET DATA ====================================================
    // ====================================================================================

    // ====================================================================================
    // ========== ACTION LISTENERS ========================================================
    // ====================================================================================
    onPageChangeListeners = (event, value) => { 
        this.setState({ pageSelected: value })
        const { pageListeners } = this.props
        if(pageListeners) { pageListeners(value) }
    }
    onPageReloadListeners = () => { alert("onPageReloadListeners") }
    onRowChangeListeners = (value) => {
        this.setState({ pageRow: value, pageRowIsOpen: false })
        const { pageRowListeners } = this.props
        if(pageRowListeners) { pageRowListeners(value) }
    }
    onSortListeners = (item, index) => {
        let dataHeadings = this.state.dataHeadings

        let inputSort = ""
        if (item.sort === "") {
            item.sort = "asc"
            inputSort = item.key
        } else if (item.sort === "asc") {
            item.sort = "desc"
            inputSort = `-${item.key}`;
        } else {
            item.sort = ""
        }

        dataHeadings[index] = item
        dataHeadings.map((val) => {
            if (val.sortable && val.key !== item.key) {
                val.sort = "";
            }
            return val;
        });

        this.setState({ dataHeadings })
        const { sortListeners } = this.props
        if (sortListeners !== undefined) { sortListeners(inputSort) }
    }

    // ====================================================================================
    // ========== RENDER SECTIONS =========================================================
    // ====================================================================================
    render() {
        return(
            <div className={`${style.tableContainer} ${this.props.withTab && style.withTab} ${this.props.addClass}`}>
                {!this.props.disabledHeader && <div className={style.tableHead}>
                    {this.props.title !== undefined && this.props.title !== "" &&
                        <div className={style.topBar}>
                            <div className={style.title}>{this.props.title}</div>
                            {this.props.subtitle !== undefined && this.props.subtitle !== "" &&
                                <div className={style.subtitle}>{this.props.subtitle}</div>
                            }
                        </div>
                    }
                    {this.props.topComponent}
                    <div className={style.searchBar}>
                        {!this.props.disabledAddBtn &&
                            <button onClick={() => {
                                const { addListeners } = this.props
                                if(addListeners) { addListeners() }
                                }}>
                                <i className="ph ph-bold ph-plus-circle"></i>
                                <span>{this.props.addTitle || `Data Baru`}</span>
                            </button>
                        }
                        {this.props.btnComponent}
                        {!this.props.disabledSearch &&
                            <>
                            <div className={`${style.inputSearch} ${this.props.disabledAddBtn && style.disabledAddBtn}`}>
                                <label for="search-input">
                                    <i className="ph ph-bold ph-magnifying-glass"></i>
                                </label>
                                <input type="text" name="search" id="search-input" placeholder="Cari data disini..."/>
                            </div>
                            <button onClick={() => {
                                const { searchListeners } = this.props
                                if(searchListeners) { searchListeners() }
                                }}>
                                <i className="ph ph-bold ph-magnifying-glass"></i>
                                <span>Cari</span>
                            </button>
                            </>
                        }
                        
                        {!this.props.disabledBtnFilter &&
                            <button onClick={() => {
                                const { filterListeners } = this.props
                                if(filterListeners) { filterListeners() }
                                }}>
                                <i className="ph ph-bold ph-faders"></i>
                                <span>Filter</span>
                            </button>
                        }
                        {!this.props.disabledBtnExport &&
                            <button onClick={() => {
                                const { exportListeners } = this.props
                                if(exportListeners) { exportListeners() }
                                }}>
                                <i class="ph ph-bold ph-download"></i>
                                <span>Unduh</span>
                            </button>
                        }
                    </div>
                    {this.props.inputSearch !== undefined && this.props.inputSearch !== "" &&
                        <div className={style.searchResults}>
                            <Tooltip title={"Hapus pencarian ini"}>
                                <div className={style.search}
                                    onClick={() => {
                                        const { searchListeners } = this.props
                                        if(searchListeners) { searchListeners("") }
                                        }}
                                    >
                                    {this.props.inputSearch} 
                                    <i className="ph ph-bold ph-x-circle"></i>
                                </div>
                            </Tooltip> 
                        </div>
                    }
                    {this.props.inputFilter !== undefined && this.props.inputFilter.length > 0 &&
                        <div className={style.searchResults}>
                            {this.props.inputFilter.map((item, index) => {
                                return(
                                    <Tooltip key={index} title={"Hapus filter ini"}>
                                        <div className={style.filter}
                                            onClick={() => {
                                                const { filterListeners } = this.props
                                                if(filterListeners) { filterListeners(item, index) }
                                            }}
                                            >
                                            {item} 
                                            <i className="ph ph-bold ph-x-circle"></i>
                                        </div>
                                    </Tooltip> 
                                )
                            })}
                        </div>
                    }
                </div>}
                <div className={style.tableBody}>
                    <table>
                        {this.renderTableHeadings()}
                        {this.renderTableItems()}
                    </table>
                </div>
                {!this.props.disabledPage &&
                    <div className={style.tablePage}>
                        <div className={style.left}>
                            <div className="dropdown">
                                <Button onClick={() => { this.setState({ pageRowIsOpen: true })}}
                                    className="button"
                                    style={{ height: "32px" }}
                                    >
                                    {this.state.pageRow}
                                    <i class="ph ph-bold ph-caret-down"></i>
                                </Button>
                                <Menu open={this.state.pageRowIsOpen}
                                    onClose={() => { this.setState({ pageRowIsOpen: false }) }}>
                                    {this.state.pageRowOptions.map((item, index) => {
                                        return(
                                            <MenuItem key={index} 
                                                selected={this.state.pageRow === item ? true : false}
                                                onClick={() => { this.onRowChangeListeners(item) }}
                                                >
                                                {item}
                                            </MenuItem>
                                        )
                                    })}
                                </Menu>
                            </div>
                            <small style={{ fontSize: ".95rem", marginLeft: 10, color: "#7a7979" }}>Baris per halaman</small>
                        </div>
                        <div className={style.right}>
                            <Pagination 
                                count={this.state.pageCount} 
                                page={this.state.pageSelected}
                                onChange={this.onPageChangeListeners}
                                shape="rounded" 
                                showFirstButton 
                                showLastButton
                                siblingCount={0} 
                                boundaryCount={1}
                                color="primary"
                            />
                            <Tooltip title="Muat ulang data pada halaman">
                                <IconButton onClick={this.onPageReloadListeners}>
                                    <i className="ph ph-bold ph-arrow-clockwise" style={{ color: "#16a085" }}></i>
                                </IconButton>
                            </Tooltip>
                        </div>
                    </div>
                }
            </div>
        )
    }
    renderTableHeadings() {
        let arrData = this.props.dataHeadings
        if(arrData.length > 0) {
            return(
                <thead>
                    <tr>
                        {arrData.map((item, index) => {
                            return(
                                <th key={index} className={item.className}>
                                    <div className={item.sortable && style.sorts}
                                        onClick={() => {  if(item.sortable) { this.onSortListeners(item, index) } }}
                                        >
                                        {item.label === "" && <i className="ph ph-bold ph-dots-three-outline"></i>}
                                        {item.label !== "" && item.label}
                                        {item.sortable && item.sort === '' && <i class="ph ph-bold ph-arrows-down-up"></i>}
                                        {item.sortable && item.sort === 'asc' && <i class="ph ph-bold ph-sort-ascending"></i>}
                                        {item.sortable && item.sort === 'desc' && <i class="ph ph-bold ph-sort-descending"></i>}
                                    </div>
                                </th>
                            )
                        })}
                    </tr>
                </thead>
            )
        }
    }
    renderTableItems() {
        let arrData = this.props.dataTables
        if(arrData.length > 0) {
            return(
                <tbody>
                    {arrData.map((item, index) => {
                        if(this.props.renderItems) {
                            return this.props.renderItems(item, index)
                        }
                    })}
                </tbody>
            )
        }
    }
}