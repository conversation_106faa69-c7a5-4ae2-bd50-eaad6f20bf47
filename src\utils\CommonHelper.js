/*
Created by esoda
Created on Nov, 2023
Contact esoda.id
*/

import moment from "moment";

const CommonHelper = {
  greating: () => {
    var today = new Date();
    var curHr = today.getHours();
    if (curHr > 3 && curHr < 12) {
      return "Selamat pagi";
    } else if (curHr >= 12 && curHr < 16) {
      return "Selamat siang";
    } else if (curHr >= 16 && curHr < 18) {
      return "Selamat sore";
    } else {
      return "Selamat malam";
    }
  },
  initialName: (value) => {
    if (!value) return "";
    let splitName = value.split(" ");
    let initial = value.charAt(0);
    if (splitName[1] !== undefined) {
      initial += splitName[1].charAt(0);
    }

    return initial.toUpperCase();
  },
  formatNumber: (amount, format = null) => {
    let result;
    let mark = "";
    if (amount !== null) {
      amount = Math.ceil(amount);      
      if (amount < 0) {
        mark = "-";
      }
      amount = Math.abs(amount);
      result = amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
    } else {
      result = amount;
    }
    let prefix;
    if (format === "idr") {
      prefix =  `${mark}Rp`;
    } else {
      prefix = `${mark}`;
    }

    return prefix + result;
  },
  capitalize: (string) => {
    return string.charAt(0).toUpperCase() + string.slice(1);
  },
  getDateRangeInMonth: (month, year) => {
    // Result will return start & end date in a selected month and year
    let dateCount = new Date(year, month, 0).getDate();
    let startDate = "";
    let endDate = "";

    for (let index = 1; index <= dateCount; index++) {
      let date = index > 9 ? index : `0${index}`;
      startDate = `${year}-${month}-01`;
      endDate = `${year}-${month}-${date.toString()}`;
    }
    return { startDate, endDate };
  },
  getPreviousDateRange(startDate, endDate, type) {
    // Parse the dates using Moment.js
    const start = moment(startDate);
    const end = moment(endDate);

    let previousStart, previousEnd, difference;

    // Calculate previous range based on type
    switch (type.toUpperCase()) {
      case "Y": // Previous year
        previousStart = start.clone().subtract(1, "year");
        previousEnd = end.clone().subtract(1, "year");
        difference = end.diff(start, "days");
        break;

      case "M": // Month difference
        // Calculate months difference
        const monthsDiff = end.diff(start, "months") + 1;

        previousStart = start
          .clone()
          .subtract(monthsDiff, "month")
          .startOf("month");
        previousEnd = end.clone().subtract(monthsDiff, "month").endOf("month");

        // Move back from the start date by monthsDiff + 1 to get the previous period
        // previousEnd = start.clone().subtract(1, "days"); // Day before start
        // previousStart = previousEnd
        //   .clone()
        //   .subtract(monthsDiff, "months")
        //   .startOf("day");
        // difference = end.diff(start, "days");
        break;

      case "W": // Week difference
        const weeksDiff = end.diff(start, "weeks");
        previousStart = start.clone().subtract(weeksDiff, "weeks");
        previousEnd = end.clone().subtract(weeksDiff, "weeks");
        difference = end.diff(start, "days");
        break;

      case "D": // Day difference
        const daysDiff = end.diff(start, "days");
        previousEnd = start.clone().subtract(1, "days");
        previousStart = previousEnd.clone().subtract(daysDiff, "days");
        difference = daysDiff;
        break;

      default:
        throw new Error(
          "Invalid type. Use Y (year), M (month), W (week), or D (day)"
        );
    }

    return {
      start_date: previousStart.format("YYYY-MM-DD"),
      end_date: previousEnd.format("YYYY-MM-DD"),
      daysDifference: difference,
    };
  },
  isValidEmail: (email) => {
    const re =
      /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(String(email).toLowerCase());
  },
  isGoogleEmail: (email) => {
    let split1 = email.split("@");
    let hostName = split1[1];
    if (hostName.toLowerCase() === "gmail.com") {
      return true;
    } else {
      return false;
    }
  },
  getStateColor: (status) => {
    let opacity = 0.25;
    let color = {
      background: `rgba(127, 140, 141, ${opacity})`,
      color: "rgba(127, 140, 141, 1)",
    };

    if (
      status === "Menunggu Approval" ||
      status === "Menunggu pembayaran" ||
      status === "Refund" ||
      status.toLowerCase() === "menunggu proses" ||
      status.toLowerCase() === "belum mulai" ||
      status.toLowerCase() === "menunggu konfirmasi" ||
      status.toLowerCase() === "belum bayar" ||
      status === "FREE/TRIAL"
    ) {
      color = {
        background: `rgba(230, 126, 34, ${opacity})`,
        color: "rgba(230, 126, 34, 1)",
      };
    }
    if (status.toLowerCase() === "sedang diproses") {
      color = {
        background: `rgba(52, 152, 219, ${opacity})`,
        color: "rgba(52, 152, 219, 1)",
      };
    }
    if (status.toLowerCase() === "pengiriman") {
      color = {
        background: `rgba(142, 68, 173, ${opacity})`,
        color: "rgba(142, 68, 173, 1)",
      };
    }
    if (
      status === "Ditransfer" ||
      status === "Terbayar" ||
      status === "Diterima" ||
      status === "Berhasil" ||
      status === "Tersedia" ||
      status.toLowerCase() === "mulai" ||
      status.toLowerCase() === "lunas" ||
      status === "AKTIF" ||
      status === "Lite"
    ) {
      color = {
        background: `rgba(22, 160, 133, ${opacity})`,
        color: "rgba(22, 160, 133, 1)",
      };
    }
    if (
      status === "Dibatalkan" ||
      status === "Ditolak" ||
      status === "Dihapus" ||
      status === "Gagal" ||
      status.toLowerCase() === "selesai" ||
      status.toLowerCase() === "habis" ||
      status === "BERAKHIR"
    ) {
      color = {
        background: `rgba(231, 76, 60, ${opacity})`,
        color: "rgba(231, 76, 60, 1)",
      };
    }
    if (status === "Pro") {
      color = {
        background: `rgba(211, 17, 69, ${opacity})`,
        color: "rgba(211, 17, 69, 1)",
      };
    }

    // status pembayaran pembelian ke supplier
    if (status === "Belum Bayar") {
      color = {
        background: `rgba(211, 17, 69, ${opacity})`,
        color: "rgba(211, 17, 69, 1)",
      };
    } else if (status === "Belum Lunas") {
      color = {
        background: `rgba(230, 126, 34, ${opacity})`,
        color: "rgba(230, 126, 34, 1)",
      };
    }

    // status sync accurate
    // sudah sinkronisasi terbayar
    // sudah sinkronisasi void
    // sudah sinkronisasi refund
    // sinkronisasi ulang
    // belum sinkronisasi
    // gagal sinkronisasi
    if (
      status === "sudah sinkronisasi" ||
      status === "sudah sinkronisasi terbayar" ||
      status === "sudah sinkronisasi void" ||
      status === "sudah sinkronisasi refund"
    ) {
      color = {
        background: `rgba(22, 160, 133, ${opacity})`,
        color: "rgba(22, 160, 133, 1)",
      };
    } else if (status === "sinkronisasi ulang") {
      color = {
        background: `rgba(230, 126, 34, ${opacity})`,
        color: "rgba(230, 126, 34, 1)",
      };
    } else if (status === "belum sinkronisasi") {
      color = {
        background: `rgba(127, 140, 141, ${opacity})`,
        color: "rgba(127, 140, 141, 1)",
      };
    } else if (status === "gagal sinkronisasi") {
      color = {
        background: `rgba(211, 17, 69, ${opacity})`,
        color: "rgba(211, 17, 69, 1)",
      };
    }

    return color;
  },
  getStateColorSaleType: (type) => {
    let opacity = 0.25;
    let color = {
      background: `rgba(127, 140, 141, ${opacity})`,
      color: "rgba(127, 140, 141, 1)",
    };
    if (type === "Take Away") {
      color = {
        background: `rgba(243, 156, 18, ${opacity})`,
        color: "rgba(243, 156, 18, 1)",
      };
    } else if (type === "Dine In") {
      color = {
        background: `rgba(39, 174, 96, ${opacity})`,
        color: "rgba(39, 174, 96, 1)",
      };
    }

    return color;
  },
  getStateColorPaymentMethod: (type) => {
    let opacity = 0.25;
    let color = {
      background: `rgba(127, 140, 141, ${opacity})`,
      color: "rgba(127, 140, 141, 1)",
    };
    if (type === "Transfer") {
      color = {
        background: `rgba(52, 152, 219, ${opacity})`,
        color: "rgba(52, 152, 219, 1)",
      };
    } else if (type === "Tunai") {
      color = {
        background: `rgba(39, 174, 96, ${opacity})`,
        color: "rgba(39, 174, 96, 1)",
      };
    } else if (type === "EDC") {
      color = {
        background: `rgba(52, 152, 219, ${opacity})`,
        color: "rgba(52, 152, 219, 1)",
      };
    }

    return color;
  },
  getProductTypeName: (type) => {
    let result = "Produk";
    if (type === "product") {
      result = "Produk";
    }
    if (type === "package") {
      result = "Paket Produk";
    }
    if (type === "ingredient") {
      result = "Bahan Baku";
    }

    return result;
  },
  slugify: (text) => {
    return text
      .toString() // Cast to string (optional)
      .normalize("NFKD") // The normalize() using NFKD method returns the Unicode Normalization Form of a given string.
      .toLowerCase() // Convert the string to lowercase letters
      .trim() // Remove whitespace from both sides of a string (optional)
      .replace(/\s+/g, "-") // Replace spaces with -
      .replace(/[^\w\-]+/g, "") // Remove all non-word chars
      .replace(/\_/g, "-") // Replace _ with -
      .replace(/\-\-+/g, "-") // Replace multiple - with single -
      .replace(/\-$/g, ""); // Remove trailing -
  },
  toBase64: (file) =>
    new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = reject;
    }),
  formatNumberDecimal: (val) => {
    if (!val) {
      return 0;
    }
    if (typeof val === "string") {
      val = Number(val);
    }
    val = val.toString();
    const split = val.split(".");
    // let decimal = 0;
    let numberBeforeDecimal = split[0];
    numberBeforeDecimal = numberBeforeDecimal
      .toString()
      .replace(/\B(?=(\d{3})+(?!\d))/g, ".");
    let numberAfterDecimal = "";
    if (split[1] !== undefined) {
      if (Number(split[1]) > 0) {
        numberAfterDecimal = split[1];
      }
    }

    if (!numberAfterDecimal) {
      return `${numberBeforeDecimal}`;
    }

    return `${numberBeforeDecimal},${numberAfterDecimal}`;
  },
  formatNumberSuffix(number, options = {}, prefix = "") {
    const {
      decimalPlaces = number < 10 ? 1 : 0,
      includeSpace = true,
      useShortForm = false,
    } = options;

    const suffixes = [
      { value: 1e12, suffix: useShortForm ? "T" : "triliun" },
      { value: 1e9, suffix: useShortForm ? "M" : "miliar" },
      { value: 1e6, suffix: useShortForm ? "jt" : "juta" },
      { value: 1e3, suffix: useShortForm ? "rb" : "ribu" },
      { value: 1, suffix: "" },
    ];

    const isNegative = number < 0;
    number = Math.abs(number);

    let formattedNumber = number;
    let selectedSuffix = "";

    for (const tier of suffixes) {
      if (number >= tier.value) {
        formattedNumber = number / tier.value;
        selectedSuffix = tier.suffix;
        break;
      }
    }

    const formatter = new Intl.NumberFormat("id-ID", {
      maximumFractionDigits: decimalPlaces,
      minimumFractionDigits: 0,
    });

    let result = formatter.format(formattedNumber);
    if (selectedSuffix) {
      result += (includeSpace && !useShortForm ? " " : "") + selectedSuffix;
    }

    return isNegative ? prefix + "-" + result : prefix + result;
  },
  roundDecimal(num, decimalPlaces = 0) {
    var p = Math.pow(10, decimalPlaces || 0);

    var n = num * p * (1 + Number.EPSILON);
    return Math.round(n) / p;
  },
  task(name, func = (resolve) => {}) {
    return new Promise((resolve) => {
      func(resolve);
    });
  },
  rounding: (number, type) => {
    let result = 0;
    let sisa_angka = number.toString().substr(-2);
    if (sisa_angka !== "00") {
      if (Number(sisa_angka) < 100) {
        result = 100 - Number(sisa_angka);
      }
    }

    return result;
  },
  periodId: (name) => {
    const ref = {
      Day: "Hari",
      Month: "Bulan",
      Year: "Tahun",
    };

    if (ref[name] !== undefined) {
      return ref[name];
    }
    return name;
  },
  debounce(func, timeout = 600) {
    let timer;
    return (...args) => {
      clearTimeout(timer);
      timer = setTimeout(() => {
        func.apply(this, args);
      }, timeout);
    };
  },
  padNumber: (number, width = 2) => {
    return String(number).padStart(width, '0');
  },
  getAllURLParams() {
    const params = new URLSearchParams(window.location.search);
    const result = {};
    for (const [key, value] of params) {
      result[key] = value;
    }
    return result;
  },
  requestFullscreen(refElement) {
    if (refElement.requestFullscreen) {
      return refElement.requestFullscreen();
    } else if (refElement.mozRequestFullScreen) {
      return refElement.mozRequestFullScreen();
    } else if (refElement.webkitRequestFullscreen) {
      return refElement.webkitRequestFullscreen();
    } else if (refElement.msRequestFullscreen) {
      return refElement.msRequestFullscreen();
    }
  },
  exitFullscreen() {
    if (document.exitFullscreen) {
      return document.exitFullscreen();
    } else if (document.mozCancelFullScreen) {
      return document.mozCancelFullScreen();
    } else if (document.webkitExitFullscreen) {
      return document.webkitExitFullscreen();
    } else if (document.msExitFullscreen) {
      return document.msExitFullscreen();
    }
  },
  updateNestedState(obj, keys, value) {
    if (keys.length === 0) return { ...obj, ...value };

    const [firstKey, ...restKeys] = keys;

    return {
      ...obj,
      [firstKey]: CommonHelper.updateNestedState(
        obj[firstKey] || {},
        restKeys,
        value
      ),
    };
  },
  // sanitizePhoneNumber(phone) {
  //   // Remove all invalid characters except digits and '+'
  //   phone = phone.replace(/[^\d+]/g, "");

  //   // Ensure only one '+' at the start, remove any extra '+'
  //   if (phone.startsWith("+")) {
  //     phone = "+" + phone.slice(1).replace(/\+/g, ""); // Keep first '+', remove others
  //   } else {
  //     phone = phone.replace(/\+/g, ""); // Remove all '+' if not at the start
  //   }

  //   return phone;
  // },
  sanitizePhoneNumber(
    phone,
    options = { allowPlus: true, onlyFirstZero: false }
  ) {
    // Merge default options with provided options
    const settings = {
      allowPlus: true,
      onlyFirstZero: false,
      ...options,
    };

    // Convert to string if input isn't already a string
    phone = String(phone);

    // Remove all invalid characters except digits and '+'
    phone = phone.replace(/[^\d+]/g, "");

    // Handle '+' based on allowPlus option
    if (settings.allowPlus) {
      // Ensure only one '+' at the start, remove any extra '+'
      if (phone.startsWith("+")) {
        phone = "+" + phone.slice(1).replace(/\+/g, "");
      } else {
        phone = phone.replace(/\+/g, ""); // Remove all '+' if not at start
      }
    } else {
      // Remove all '+' if allowPlus is false
      phone = phone.replace(/\+/g, "");
    }

    // Handle onlyFirstZero option
    if (!settings.onlyFirstZero) {
      // Remove all leading zeros
      // phone = phone.replace(/^0+/, "");
    } else {
      // Force first character to be zero and remove additional leading zeros
      if (settings.allowPlus && phone.startsWith("+")) {
        phone = "+0" + phone.slice(1).replace(/^0+/, "");
      } else {
        phone = "0" + phone.replace(/^0+/, "");
      }
    }

    return phone;
  },
  sanitizeZipCode(zip) {
    // Remove all non-digit and non-hyphen characters
    zip = zip.replace(/[^0-9-]/g, "");

    // Ensure hyphen appears only in ZIP+4 format (e.g., "12345-6789")
    zip = zip.replace(/-+/g, "-"); // Replace multiple hyphens with a single one
    zip = zip.replace(/^-/g, ""); // Remove hyphen at the start
    zip = zip.replace(/-$/, ""); // Remove hyphen at the end
    zip = zip.replace(/(\d{5})-(?!\d{4})/, "$1"); // Remove hyphen if not followed by 4 digits

    return zip;
  },
  isValidURL: (str) => {
    const pattern = new RegExp(
      '^(https?:\\/\\/)?' + // protocol
        '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|' + // domain name
        '((\\d{1,3}\\.){3}\\d{1,3}))' + // OR ip (v4) address
        '(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*' + // port and path
        '(\\?[;&a-z\\d%_.~+=-]*)?' + // query string
        '(\\#[-a-z\\d_]*)?$', // fragment locator
      'i'
    );
    return pattern.test(str);
  },
  uint8ArrayToBase64(uint8Array) {
    let binaryString = '';
    uint8Array.forEach(byte => {
        binaryString += String.fromCharCode(byte);
    });
    return btoa(binaryString);
  },
  base64ToUint8Array(base64String) {
    const binaryString = atob(base64String);
    const uint8Array = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
        uint8Array[i] = binaryString.charCodeAt(i);
    }
    return uint8Array;
  },
  base64UrlEncode(str) {
    const encoder = new TextEncoder(); // UTF-8 encoder
    const uint8Array = encoder.encode(str);
    let base64 = CommonHelper.uint8ArrayToBase64(uint8Array);
    // Make it URL-safe
    return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
  },
  base64UrlDecode(str) {
    // Add back padding if it was removed, and convert to standard Base64
    let base64 = str.replace(/-/g, '+').replace(/_/g, '/');
    while (base64.length % 4) {
        base64 += '=';
    }
    const uint8Array = CommonHelper.base64ToUint8Array(base64);
    const decoder = new TextDecoder(); // UTF-8 decoder
    return decoder.decode(uint8Array);
  },
  fullTextSearch(arr, search, key = undefined) {
    search = search.toLowerCase();
    search = search.split(" ");
    if (key == undefined) {
      return [];
    } else {
      return arr.filter((item) =>
        search.every((el) => item[key].toLowerCase().includes(el))
      );
    }
  },
};

export default CommonHelper;
