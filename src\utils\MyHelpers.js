import moment from "moment";

const isToday = (momentDate) => {
  let REFERENCE = moment();
  let TODAY = REFERENCE.clone().startOf("day");
  return momentDate.isSame(TODAY, "d");
};

const MyHelpers = {
  getInitial: (name) => {
    let splitName = name.split(" ");
    let initial = name.charAt(0);
    if (splitName[1] !== undefined) {
      initial += splitName[1].charAt(0);
    }
    return initial.toUpperCase();
  },
  formatNumber: (amount, format = null) => {
    let result;
    if (amount != null) {
      amount = Math.ceil(amount);
      result = amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
    } else {
      result = amount;
    }
    let prefix;
    if (format == "idr") {
      prefix = "Rp";
    } else {
      prefix = "";
    }
    return prefix + result;
  },
  strRegex: (string) => {
    const regex = /(<([^>]+)>)/gi;
    var str = string;
    var strReplace = str.replace(regex, "");
    var strReplace2 = strReplace.replace(/\u00a0/g, " ");
    var strReplace3 = strReplace2.replace(/&amp;/g, " & ");
    var strReplace4 = strReplace3.replace(/&nbsp;/g, " ");
    return strReplace4;
  },
  delBr: (string) => {
    var str = string;
    var strReplace = str.replace(/<p><br[\/]?><[\/]?p>/g, "");
    return strReplace;
  },
  isValidEmail: (email) => {
    const re =
      /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(String(email).toLowerCase());
  },
  isGoogleEmail: (email) => {
    let split1 = email.split("@");
    let hostName = split1[1];
    if (hostName.toLowerCase() === "gmail.com") {
      return true;
    } else {
      return false;
    }
  },
  greating: () => {
    var today = new Date();
    var curHr = today.getHours();
    if (curHr < 12) {
      return "Selamat pagi";
    } else if (curHr < 16) {
      return "Selamat siang";
    } else if (curHr < 18) {
      return "Selamat sore";
    } else {
      return "Selamat malam";
    }
  },
  season: () => {
    var today = new Date();
    var curHr = today.getHours();
    if (curHr < 12) {
      return "pagi";
    } else if (curHr < 16) {
      return "siang";
    } else if (curHr < 18) {
      return "sore";
    } else {
      return "malam";
    }
  },
  myConvertHMS: (value) => {
    let result = "";
    if (value >= 3600) {
      result = new Date(value * 1000).toISOString().substr(11, 8);
    } else {
      result = new Date(value * 1000).toISOString().substr(14, 5);
    }
    return result;
  },
  convertHMS: (value) => {
    const sec = parseInt(value, 10); // convert value to number if it's string
    let hours = Math.floor(sec / 3600); // get hours
    let minutes = Math.floor((sec - hours * 3600) / 60); // get minutes
    let seconds = sec - hours * 3600 - minutes * 60; //  get seconds
    // add 0 if value < 10; Example: 2 => 02
    if (hours < 10) {
      hours = "0" + hours;
    }
    if (minutes < 10) {
      minutes = "0" + minutes;
    }
    if (seconds < 10) {
      seconds = "0" + seconds;
    }
    return hours + ":" + minutes + ":" + seconds; // Return is HH : MM : SS
  },
  secondsToHms: (d) => {
    d = Number(d);
    var h = Math.floor(d / 3600);
    var m = Math.floor((d % 3600) / 60);
    var s = Math.floor((d % 3600) % 60);

    // var hDisplay = h > 0 ? h + (h == 1 ? " hour, " : " hours, ") : "";
    // var mDisplay = m > 0 ? m + (m == 1 ? " minute, " : " minutes, ") : "";
    // var sDisplay = s > 0 ? s + (s == 1 ? " second" : " seconds") : "";

    var hDisplay = h > 0 ? h + (h == 1 ? "h " : "h ") : "";
    var mDisplay = m > 0 ? m + (m == 1 ? "m " : "m ") : "";
    var sDisplay = s > 0 ? s + (s == 1 ? "d" : "d") : "";
    return hDisplay + mDisplay + sDisplay;
  },
  timeToSecond: (time) => {
    let result = 0;
    let timeParts = time.split(":"); //Separate  mm ss
    let secs = Number(timeParts[0]) * 60 + Number(timeParts[1]);
    result = secs;
    return result;
  },
  getDateCountInMonth: (month, year) => {
    // Result will return number of day in a selected month and year
    return new Date(year, month, 0).getDate();
  },
  getDateRangeInMonth: (month, year) => {
    // Result will return start & end date in a selected month and year
    let dateCount = new Date(year, month, 0).getDate();
    let startDate = "";
    let endDate = "";

    for (let index = 1; index <= dateCount; index++) {
      let date = index > 9 ? index : `0${index}`;
      startDate = `${year}-${month}-01`;
      endDate = `${year}-${month}-${date.toString()}`;
    }
    return { startDate, endDate };
  },
  getDaysInMonth: (month, year) => {
    return new Date(year, month, 0).getDate();
  },
  getEndDateInMonth: (month, year) => {
    let dayInMonths = new Date(year, month, 0).getDate();
    let endDate = "";
    for (let index = 1; index <= dayInMonths; index++) {
      let date = index > 9 ? index : `0${index}`;
      endDate = `${year}-${month}-${date.toString()}`;
    }
    return endDate;
  },
  splitName: (str, limit = 20) => {
    let arrName = str.split(" ");
    let result = [];
    let name = arrName[0];
    if (str.length > limit) {
      if (arrName.length > 1) {
        for (let i = 1; i < arrName.length; i++) {
          let name1 = name + " " + arrName[i];
          if (name1.length <= limit) {
            if (i == arrName.length - 1) {
              result.push(name1);
            } else {
              name = name1;
            }
          } else {
            result.push(name);
            if (i == arrName.length - 1) {
              result.push(arrName[i]);
            } else {
              name = arrName[i];
            }
          }
        }
      }
    } else {
      result.push(str);
    }
    return result;
  },
  rounding: (number, type) => {
    let result = 0;
    let sisa_angka = number.toString().substr(-2);
    if (sisa_angka !== "00") {
      if (Number(sisa_angka) < 100) {
        result = 100 - Number(sisa_angka);
      }
    }

    return result;
  },
  getStateColor: (type) => {
    let opacity = 0.2;
    let color = {
      background: `rgba(127, 140, 141, ${opacity})`,
      color: "rgba(127, 140, 141, 1)",
    };

    if (type === "Menunggu Pembayaran" || type === "Menunggu pembayaran") {
      color = {
        background: `rgba(243, 156, 18, ${opacity})`,
        color: "rgba(243, 156, 18, 1)",
      };
    }
    if (type === "belum bayar") {
      color = {
        background: `rgba(243, 156, 18, ${opacity})`,
        color: "rgba(243, 156, 18, 1)",
      };
    }
    if (type === "Menunggu Approval" || type === "Menunggu Proses") {
      color = {
        background: `rgba(243, 156, 18, ${opacity})`,
        color: "rgba(243, 156, 18, 1)",
      };
    }
    if (type === "dikemas" || type === "Sedang Diproses") {
      color = {
        background: `rgba(52, 73, 94, ${opacity})`,
        color: "rgba(52, 73, 94, 1)",
      };
    }
    if (type === "Dikirim" || type === "Pengiriman") {
      color = {
        background: `rgba(52, 152, 219, ${opacity})`,
        color: "rgba(52, 152, 219, 1)",
      };
    }
    if (type === "dikirim") {
      color = {
        background: `rgba(52, 152, 219, ${opacity})`,
        color: "rgba(52, 152, 219, 1)",
      };
    }
    if (type === "Selesai" || type === "Terbayar") {
      color = {
        background: `rgba(39, 174, 96, ${opacity})`,
        color: "rgba(39, 174, 96, 1)",
      };
    }
    if (type === "Diproses") {
      color = {
        background: `rgba(39, 174, 96, ${opacity})`,
        color: "rgba(39, 174, 96, 1)",
      };
    }
    if (type === "Ditransfer") {
      color = {
        background: `rgba(39, 174, 96, ${opacity})`,
        color: "rgba(39, 174, 96, 1)",
      };
    }
    if (type === "masuk") {
      color = {
        background: `rgba(39, 174, 96, ${opacity})`,
        color: "rgba(39, 174, 96, 1)",
      };
    }
    if (type === "selesai") {
      color = {
        background: `rgba(39, 174, 96, ${opacity})`,
        color: "rgba(39, 174, 96, 1)",
      };
    }
    if (type === "Diterima") {
      color = {
        background: `rgba(39, 174, 96, ${opacity})`,
        color: "rgba(39, 174, 96, 1)",
      };
    }
    if (type === "Dibatalkan") {
      color = {
        background: `rgba(231, 76, 60, ${opacity})`,
        color: "rgba(231, 76, 60, 1)",
      };
    }
    if (type === "Ditolak") {
      color = {
        background: `rgba(231, 76, 60, ${opacity})`,
        color: "rgba(231, 76, 60, 1)",
      };
    }
    if (type === "keluar") {
      color = {
        background: `rgba(231, 76, 60, ${opacity})`,
        color: "rgba(231, 76, 60, 1)",
      };
    }
    if (type === "dibatalkan") {
      color = {
        background: `rgba(231, 76, 60, ${opacity})`,
        color: "rgba(231, 76, 60, 1)",
      };
    }
    if (type === "Pengembalian") {
      color = {
        background: `rgba(155, 89, 182, ${opacity})`,
        color: "rgba(155, 89, 182, 1)",
      };
    }
    if (type === "pengembalian") {
      color = {
        background: `rgba(155, 89, 182, ${opacity})`,
        color: "rgba(155, 89, 182, 1)",
      };
    }

    return color;
  },
  debounce(func, timeout = 600) {
    let timer;
    return (...args) => {
      clearTimeout(timer);
      timer = setTimeout(() => {
        func.apply(this, args);
      }, timeout);
    };
  },
  slugify(text) {
    return text
      .toString() // Cast to string (optional)
      .normalize("NFKD") // The normalize() using NFKD method returns the Unicode Normalization Form of a given string.
      .toLowerCase() // Convert the string to lowercase letters
      .trim() // Remove whitespace from both sides of a string (optional)
      .replace(/\s+/g, "-") // Replace spaces with -
      .replace(/[^\w\-]+/g, "") // Remove all non-word chars
      .replace(/\_/g, "-") // Replace _ with -
      .replace(/\-\-+/g, "-") // Replace multiple - with single -
      .replace(/\-$/g, ""); // Remove trailing -
  },
  parseUri(href) {
    let uri = href.replace(/^(https?:\/\/)/i, "");
    uri = uri.split("/");
    return {
      outletCode: uri[1],
      categorySlug: uri[2] !== undefined ? uri[2] : "",
    };
  },
  hanleSodaTenantData(sodaTenantData) {
    if (!sodaTenantData) {
      sodaTenantData = {};
    }

    if (!sodaTenantData?.arrCart) {
      sodaTenantData.arrCart = [];
    }
    if (!sodaTenantData?.productView) {
      sodaTenantData.productView = "card";
    }
    if (!sodaTenantData?.selectedCategory) {
      sodaTenantData.selectedCategory = null;
    }
    if (!sodaTenantData?.selectedSaleType) {
      sodaTenantData.selectedSaleType = {
        id: "1",
        name: "Dine In",
      };
    }

    return sodaTenantData;
  },
  calculateTextWidth(text, fontSize, letterSpacing) {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    
    // Set font size
    context.font = `${fontSize}px sans-serif`;
    
    // Get base width
    const baseWidth = context.measureText(text).width;
    
    // Add letter spacing for each character gap (n-1 gaps for n characters)
    const letterSpacingWidth = (text.length - 1) * parseFloat(letterSpacing);
    
    // Total width is base width plus letter spacing
    const totalWidth = baseWidth + letterSpacingWidth;
    
    return Math.ceil(totalWidth);
  }
};

export default MyHelpers;
