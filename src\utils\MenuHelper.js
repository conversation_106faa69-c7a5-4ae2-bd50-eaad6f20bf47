const arrMenu = [
  {
    title: "",
    menus: [
      {
        label: "Dashboard",
        icon: "home",
        link: "/dashboard",
        haveChild: false,
        active: false,
        packageType: ["Pro", "Lite"],
        roleType: ["owner", "admin", "kasir"],
        openCollapse: false,
        child: [],
      },
      {
        label: "Konfigurasi",
        icon: "manufacturing",
        link: "#",
        haveChild: true,
        active: false,
        packageType: ["Pro", "Lite"],
        roleType: ["owner", "admin", "kasir"],
        openCollapse: false,
        child: [
          {
            label: "Koperasi",
            icon: "groups",
            link: "/under-construction",
            haveChild: false,
            active: false,
            packageType: ["Pro", "Lite"],
            roleType: ["owner", "admin", "kasir"],
            openCollapse: false,
          },
          // {
          //   label: "Unit Koperasi",
          //   icon: "groups",
          //   link: "/config-unit",
          //   haveChild: false,
          //   active: false,
          //   packageType: ["<PERSON>", "Li<PERSON>"],
          //   roleType: ["owner", "admin", "kasir"],
          //   openCollapse: false,
          // },
          {
            label: "Uang Pangkal",
            icon: "manufacturing",
            link: "/config-fee",
            haveChild: false,
            active: false,
            packageType: ["Pro", "Lite"],
            roleType: ["owner", "admin", "kasir"],
            openCollapse: false,
          },
          {
            label: "Pinjaman",
            icon: "manufacturing",
            link: "/config-loan",
            haveChild: false,
            active: false,
            packageType: ["Pro", "Lite"],
            roleType: ["owner", "admin", "kasir"],
            openCollapse: false,
          },
        ],
      },
      {
        label: "Produk",
        icon: "dataset",
        link: "/#",
        haveChild: true,
        active: false,
        packageType: ["Pro", "Lite"],
        roleType: ["owner", "admin", "kasir"],
        openCollapse: false,
        child: [
          {
            label: "Simpanan",
            icon: "groups",
            link: "/product-saving",
            haveChild: false,
            active: false,
            packageType: ["Pro", "Lite"],
            roleType: ["owner", "admin", "kasir"],
            openCollapse: false,
          },
          {
            label: "Pinjaman",
            icon: "groups",
            link: "/product-loan",
            haveChild: false,
            active: false,
            packageType: ["Pro", "Lite"],
            roleType: ["owner", "admin", "kasir"],
            openCollapse: false,
          },
        ],
      },
      {
        label: "Keanggotaan",
        icon: "groups",
        link: "#",
        haveChild: true,
        active: false,
        packageType: ["Pro", "Lite"],
        roleType: ["owner", "admin", "kasir"],
        openCollapse: false,
        child: [
          {
            label: "Calon Anggota",
            icon: "groups",
            link: "/member-candidate",
            haveChild: false,
            active: false,
            packageType: ["Pro", "Lite"],
            roleType: ["owner", "admin", "kasir"],
            openCollapse: false,
          },
          // {
          //   label: "Diksar",
          //   icon: "groups",
          //   link: "/under-construction",
          //   haveChild: false,
          //   active: false,
          //   packageType: ["Pro", "Lite"],
          //   roleType: ["owner", "admin", "kasir"],
          //   openCollapse: false,
          // },
          {
            label: "Anggota",
            icon: "groups",
            link: "/member",
            haveChild: false,
            active: false,
            packageType: ["Pro", "Lite"],
            roleType: ["owner", "admin", "kasir"],
            openCollapse: false,
          },
          {
            label: "Buka Simpanan",
            icon: "groups",
            link: "/member-saving",
            haveChild: false,
            active: false,
            packageType: ["Pro", "Lite"],
            roleType: ["owner", "admin", "kasir"],
            openCollapse: false,
          },
        ],
      },
      {
        label: "Transaksi",
        icon: "payments",
        link: "#",
        haveChild: true,
        active: false,
        packageType: ["Pro", "Lite"],
        roleType: ["owner", "admin", "kasir"],
        openCollapse: false,
        child: [
          {
            label: "Uang Pangkal",
            icon: "groups",
            link: "/under-construction",
            haveChild: false,
            active: false,
            packageType: ["Pro", "Lite"],
            roleType: ["owner", "admin", "kasir"],
            openCollapse: false,
          },
          {
            label: "Setoran",
            icon: "groups",
            link: "/under-construction",
            haveChild: false,
            active: false,
            packageType: ["Pro", "Lite"],
            roleType: ["owner", "admin", "kasir"],
            openCollapse: false,
          },
          {
            label: "Penarikan",
            icon: "groups",
            link: "/under-construction",
            haveChild: false,
            active: false,
            packageType: ["Pro", "Lite"],
            roleType: ["owner", "admin", "kasir"],
            openCollapse: false,
          },
          {
            label: "Pinjaman",
            icon: "groups",
            link: "/under-construction",
            haveChild: false,
            active: false,
            packageType: ["Pro", "Lite"],
            roleType: ["owner", "admin", "kasir"],
            openCollapse: false,
          },
          {
            label: "Pemutihan",
            icon: "groups",
            link: "/under-construction",
            haveChild: false,
            active: false,
            packageType: ["Pro", "Lite"],
            roleType: ["owner", "admin", "kasir"],
            openCollapse: false,
          },
        ],
      },
      {
        label: "Pinjaman",
        icon: "payments",
        link: "/under-construction",
        haveChild: false,
        active: false,
        packageType: ["Pro", "Lite"],
        roleType: ["owner", "admin", "kasir"],
        openCollapse: false,
        child: [],
      },
      {
        label: "Laporan",
        icon: "lab_profile",
        link: "#",
        haveChild: true,
        active: false,
        packageType: ["Pro", "Lite"],
        roleType: ["owner", "admin", "kasir"],
        openCollapse: false,
        child: [
          {
            label: "Laporan Kepesertaan Simpanan",
            icon: "lab_profile",
            link: "/under-construction",
            haveChild: true,
            active: false,
            packageType: ["Pro", "Lite"],
            roleType: ["owner", "admin", "kasir"],
            openCollapse: false,
          },
          {
            label: "Laporan Kepesertaan CU",
            icon: "lab_profile",
            link: "/under-construction",
            haveChild: true,
            active: false,
            packageType: ["Pro", "Lite"],
            roleType: ["owner", "admin", "kasir"],
            openCollapse: false,
          },
          {
            label: "Laporan Kas Harian",
            icon: "lab_profile",
            link: "/under-construction",
            haveChild: true,
            active: false,
            packageType: ["Pro", "Lite"],
            roleType: ["owner", "admin", "kasir"],
            openCollapse: false,
          },
          {
            label: "Laporan Hutang Piutang",
            icon: "lab_profile",
            link: "/under-construction",
            haveChild: true,
            active: false,
            packageType: ["Pro", "Lite"],
            roleType: ["owner", "admin", "kasir"],
            openCollapse: false,
          },
          {
            label: "Laporan SHU",
            icon: "lab_profile",
            link: "/under-construction",
            haveChild: true,
            active: false,
            packageType: ["Pro", "Lite"],
            roleType: ["owner", "admin", "kasir"],
            openCollapse: false,
          },
        ],
      },
    ],
  },
  {
    title: "Administrator",
    menus: [
      {
        label: "User Admin",
        icon: "settings",
        link: "/under-construction",
        haveChild: false,
        active: false,
        packageType: ["Pro", "Lite"],
        roleType: ["owner", "admin", "kasir"],
        openCollapse: false,
        child: [],
      },
      {
        label: "Hak Akses Admin",
        icon: "settings",
        link: "/under-construction",
        haveChild: false,
        active: false,
        packageType: ["Pro", "Lite"],
        roleType: ["owner", "admin", "kasir"],
        openCollapse: false,
        child: [],
      },
    ],
  },
];

const arrMenuInactive = [
  {
    title: "",
    menus: [
      {
        label: "Beranda",
        icon: "home",
        link: "/dashboard",
        haveChild: false,
        active: false,
        packageType: ["Pro", "Lite"],
        roleType: ["owner", "admin", "kasir"],
        openCollapse: false,
        child: [],
      },
    ],
  },
  {
    title: "PENGATURAN OUTLET",
    menus: [
      {
        label: "Konfigurasi Outlet",
        icon: "settings",
        link: "/outlet",
        haveChild: false,
        active: false,
        packageType: ["Pro", "Lite"],
        roleType: ["owner", "admin", "kasir"],
        openCollapse: false,
        child: [],
      },
    ],
  },
];

const getActiveMenu = (storeSelected, user) => {
  let menuActive = [...arrMenuInactive];

  if (storeSelected.active_bool) {
    menuActive = [];
    let warehouseName = "";
    if (
      storeSelected.franchise_id !== undefined &&
      storeSelected.franchise_id !== "" &&
      storeSelected.franchise_name !== undefined &&
      storeSelected.franchise_name !== ""
    ) {
      warehouseName = storeSelected.franchise_name;
      if (!storeSelected.franchise_name.toLowerCase().includes("warehouse")) {
        warehouseName = `Warehouse ${storeSelected.franchise_name}`;
      }
    }
    arrMenu.forEach((m) => {
      let menuItem = [];
      m.menus.forEach((item) => {
        if (item.roleType.includes(user.group_type)) {
          let itemChild = [];
          item.child.forEach((child) => {
            if (child.roleType.includes(user.group_type)) {
              itemChild.push(child);
            }
          });
          if (item.label == "Warehouse") {
            if (warehouseName !== "") {
              let data = { ...item };
              data.label = warehouseName;
              data.child = itemChild;
              menuItem.push(data);
            }
          } else {
            let data = { ...item };
            data.child = itemChild;
            menuItem.push(data);
          }
        }
      });
      let data = { ...m };
      data.menus = menuItem;
      menuActive.push(data);
    });
  }

  return menuActive;
};

const validMenu = (storeSelected, menuLink) => {
  let menuActive = getActiveMenu(storeSelected);
  let valid = false;
  let packageType = "Lite";
  if (
    storeSelected.subcription_type !== undefined &&
    storeSelected.subcription_type === "transaction"
  ) {
    packageType = "Pro";
    if (
      storeSelected.config_object !== undefined &&
      storeSelected.config_object.package !== undefined &&
      storeSelected.config_object.package.type !== undefined &&
      storeSelected.config_object.package.type !== ""
    ) {
      packageType = storeSelected.config_object.package.type;
    }
  } else {
    if (
      storeSelected.config_object !== undefined &&
      storeSelected.config_object.package !== undefined &&
      storeSelected.config_object.package.type !== undefined &&
      storeSelected.config_object.package.type !== ""
    ) {
      packageType = storeSelected.config_object.package.type;
    }
  }
  let selectedMenu = null;
  let selectedMenuChild = null;
  for (let m = 0; m < menuActive.length; m++) {
    for (let i = 0; i < menuActive[m].menus.length; i++) {
      if (menuActive[m].menus[i].link == menuLink) {
        selectedMenu = menuActive[m].menus[i];
        if (
          menuActive[m].menus[i].roleType.includes(storeSelected.group_type)
        ) {
          if (menuActive[m].menus[i].packageType.includes(packageType)) {
            valid = true;
          }
        }
        break;
      } else {
        if (menuActive[m].menus[i].haveChild) {
          for (let c = 0; c < menuActive[m].menus[i].child.length; c++) {
            if (menuActive[m].menus[i].child[c].link == menuLink) {
              selectedMenuChild = menuActive[m].menus[i].child[c];
              selectedMenu = menuActive[m].menus[i];
              if (
                menuActive[m].menus[i].child[c].roleType.includes(
                  storeSelected.group_type
                )
              ) {
                if (
                  menuActive[m].menus[i].packageType.includes(packageType) &&
                  menuActive[m].menus[i].child[c].packageType.includes(
                    packageType
                  )
                ) {
                  valid = true;
                }
              }
              break;
            }
          }
        }
      }
    }
  }
  return valid;
};

export { getActiveMenu, validMenu };
