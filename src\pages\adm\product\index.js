/*
Created by esoda
Created on Mei, 2025
Contact esoda.id
*/

import React from "react";
import styles from "@/styles/Pos.module.css";
import stylesTable from "@/styles/Table.module.css";

import Loading from "@/components/modal/Loading";
import PosComponentTopbar from "@/components/pages/adm/_topbar";
import Router from "next/router";

import Constants from "@/utils/Constants";
import AuthWrapperAdmin from "@/components/wrapper/AuthWrapperAdmin";
import AdminSideBar from "@/components/pages/adm/AdminSideBar";

import CommonHelper from "@/utils/CommonHelper";
import ModalConfirmation from "@/components/modal/ModalConfirmation";
import MySnackbar from "@/components/MySnackbar";
import ProductList from "@/components/pages/adm/product/ProductList";

class AdminProduct extends React.Component {
  constructor(props) {
    super(props);
    this.ref_Loading = null;
    this.ref_MySnackbar = null;
    this.ref_ModalConfirmation = null;
    this.ref_ProductList = null;
  }

  // ====================================================================================
  // ========== LIFECYCLE METHODS =======================================================
  // ====================================================================================
  componentDidUpdate(prevProps, prevState) {
    if (prevProps.isAuthLoading !== this.props.isAuthLoading) {
      if (!this.props.isAuthLoading && this.props.isLoggedIn) {
        this.onInit();
      }
    }
  }

  // ====================================================================================
  // ========== INITIALIZE, GET DATA ====================================================
  // ====================================================================================
  onInit = () => {
    this.onRefresh();
  };

  onRefresh = () => {
    if (this.ref_ProductList) {
      this.ref_ProductList.onRefresh();
    }
  };

  // ====================================================================================
  // ========== ACTION LISTENERS ========================================================
  // ====================================================================================

  // ====================================================================================
  // ========== RENDER SECTIONS =========================================================
  // ====================================================================================
  render() {
    const { isAuthLoading, user, menu } = this.props;
    return (
      <div
        className={`${styles.ct_containers}`}
        style={{ backgroundColor: "rgb(252, 252, 252)" }}
      >
        <PosComponentTopbar
          hideButton
          title={"Produk"}
          subtitle={"Atur data produk"}
          user={user}
          isAuthLoading={isAuthLoading}
        />
        <div className={`${styles.contents} ${styles.sidebar}`}>
          <AdminSideBar isAuthLoading={isAuthLoading} menu={menu} />
          {this.renderContent()}
        </div>

        <Loading ref={(value) => (this.ref_Loading = value)} />
        <MySnackbar ref={(value) => (this.ref_MySnackbar = value)} />
        <ModalConfirmation
          ref={(value) => (this.ref_ModalConfirmation = value)}
        />
      </div>
    );
  }

  renderContent() {
    return (
      <div className={styles.ctx}>
        <ProductList
          ref={(value) => (this.ref_ProductList = value)}
          selectedTabTitle={"Data Produk"}
          isAuthLoading={this.props.isAuthLoading}
          selected_aol={this.props.selected_aol}
        />
      </div>
    );
  }
}

export default AuthWrapperAdmin(AdminProduct, {
  redirectTo: Constants.webUrl.adm.login,
});
