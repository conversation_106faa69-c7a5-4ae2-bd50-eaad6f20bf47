/*
Created by esoda
Created on Nov, 2024
Contact esoda.id
*/

import React from "react";
import {
  Modal,
  Box,
  FormControlLabel,
  Checkbox,
  Skeleton,
} from "@mui/material";
import Loading from "@/components/modal/Loading";
import ApiHelper from "@/utils/ApiHelper";
import MySnackbar from "@/components/MySnackbar";
import Input from "@/components/libs/Input";

const DEFAULT_INPUTS = {
  id: "",
  name: "",
  menu_array: [],
};

export default class AccessRolesModalAddUpdate extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showDialog: false,
      formType: "add",
      formData: null,
      formIndex: -1,

      inputs: structuredClone(DEFAULT_INPUTS),
      errors: {},

      isGetMenu: false,
    };
  }

  // ====================================================================================
  // ========== INITIALIZE, GET DATA ====================================================
  // ====================================================================================
  onShowDialog = (
    formType,
    formData = null,
    formIndex = -1,
    formInputs = null
  ) => {
    let inputs = structuredClone(DEFAULT_INPUTS);
    if (formInputs) {
      inputs = formInputs;
    }
    this.setState(
      {
        formType,
        formData,
        formIndex,
        showDialog: true,

        inputs,
        isGetMenu: false,
      },
      () => {
        if (formType === "edit") {
          this.onFetchDetail();
        } else if (formType === "add") {
          this.onFetchMenu();
        }
        setTimeout(() => {
          var inputNameID = document.getElementById("input-name");
          if (inputNameID) {
            inputNameID.focus();
          }
        }, 250);
      }
    );
  };
  onCloseDialog = () => {
    this.setState({
      showDialog: false,
      formType: "add",
      formData: null,
      formIndex: -1,

      inputs: structuredClone(DEFAULT_INPUTS),
      errors: {},
    });
  };
  onFetchDetail = async () => {
    this.ref_Loading.onShowDialog();
    let response = await ApiHelper.get("kiosk/admin/user/role/detail", {
      id: this.state.formData.id,
    });

    let inputs = structuredClone(this.state.inputs);
    if (response.status === 200) {
      let data = response.results.data;

      // process menu array add selected_status
      // default string false
      // check child if all selected then parent selected_bool = "true"
      // check child if one not selected then parent selected_bool = "partial"
      // process menu array add selected_status
      // default string false
      // check child if all selected then parent selected_bool = "true"
      // check child if one not selected then parent selected_bool = "partial"

      // Recursive function to calculate selected_status
      const calculateMenuStatus = (menuItem) => {
        if (!menuItem.children || menuItem.children.length === 0) {
          // Leaf node: status based on user_role_menu_id
          menuItem.selected_status =
            Number(menuItem.user_role_menu_id) > 0 ? "true" : "false";
        } else {
          // Parent node: status based on children's status
          const childStatuses = menuItem.children.map(calculateMenuStatus);

          const allTrue = childStatuses.every((status) => status === "true");
          const allFalse = childStatuses.every((status) => status === "false");

          if (allTrue) {
            menuItem.selected_status = "true";
          } else if (allFalse) {
            menuItem.selected_status = "false";
          } else {
            menuItem.selected_status = "partial";
          }
        }
        return menuItem.selected_status;
      };

      // Process the top-level menu items
      if (data.menu_array && Array.isArray(data.menu_array)) {
        data.menu_array.forEach((item) => calculateMenuStatus(item));
      }
      inputs = { ...structuredClone(inputs), ...data };
    } else {
      this.onNotify(response.message, "error");
      this.onCloseDialog();
    }
    this.ref_Loading.onCloseDialog();
    this.setState({ inputs });
  };
  onFetchMenu = async () => {
    this.setState({ isGetMenu: true });
    let response = await ApiHelper.get("kiosk/admin/user/role/menu/options");

    let inputs = structuredClone(this.state.inputs);
    if (response.status === 200) {
      const calculateMenuStatus = (menuItem) => {
        menuItem.selected_status = "false";
        const childStatuses = menuItem.children.map(calculateMenuStatus);
        return menuItem.selected_status;
      };

      // Process the top-level menu items
      if (response.results.data && Array.isArray(response.results.data)) {
        response.results.data.forEach((item) => calculateMenuStatus(item));
      }
      inputs.menu_array = response.results.data;
    } else {
      this.onNotify(response.message, "error");
    }
    this.setState({ isGetMenu: false, inputs });
  };

  // ====================================================================================
  // ========== ACTION LISTENERS ========================================================
  // ====================================================================================
  onNotify = (message, severity) => {
    if (this.ref_MySnackbar) {
      this.ref_MySnackbar.onNotify(message, severity);
    }
  };
  onTextInputListeners = (text, input) => {
    let inputs = this.state.inputs;
    inputs[input] = text;
    this.setState((prevState) => ({ ...prevState, inputs }));
  };
  onTextErrorListeners = (error, input) => {
    let errors = this.state.errors;
    errors[input] = error;
    this.setState((prevState) => ({ ...prevState, errors }));
  };
  onValidateListeners = () => {
    if (this.state.formType === "add" || this.state.formType === "edit") {
      let inputs = this.state.inputs;
      let isValid = true;

      let errorArr = [];
      if (!inputs.name) {
        this.onTextErrorListeners("Harus diisi", "name");
        errorArr.push("Nama Hak Akses harus diisi.");
      }

      if (errorArr.length > 0) {
        this.onNotify(errorArr.join("\n"), "error");
        return;
      }
      this.actOnSaveListeners();
    }
  };
  actOnSaveListeners = async () => {
    this.ref_Loading.onShowDialog();

    let params = structuredClone(this.state.inputs);

    const response = await ApiHelper.post("kiosk/admin/user/role/save", params);

    if (response.status === 200) {
      const { onResults } = this.props;
      if (onResults) {
        onResults(
          this.state.formType,
          this.state.formData,
          this.state.formIndex
        );
      }
      this.onCloseDialog();

      this.onNotify(response?.message || "Berhasil Simpan Data", "success");
    } else {
      this.onNotify(response?.message || "Terjadi Kesalahan", "error");
    }

    this.ref_Loading.onCloseDialog();
  };

  // Helper function to set status of all children recursively
  setChildrenStatus = (items, newStatus) => {
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      item.selected_status = newStatus;
      if (item.children && item.children.length > 0) {
        this.setChildrenStatus(item.children, newStatus);
      }
    }
  };

  handleMenuItemChange = (clickedItemId) => {
    let menuArray = structuredClone(this.state.inputs.menu_array);

    const updateParentAndSelfStatus = (item) => {
      if (!item.children || item.children.length === 0) {
        return;
      }

      let trueCount = 0;
      let falseCount = 0;

      for (const child of item.children) {
        if (child.selected_status === "true") {
          trueCount++;
        } else if (child.selected_status === "false") {
          falseCount++;
        }
      }

      if (trueCount === item.children.length) {
        item.selected_status = "true";
      } else if (falseCount === item.children.length) {
        item.selected_status = "false";
      } else {
        item.selected_status = "partial";
      }
    };

    // NEW: Function to propagate a status change downwards to all children and their descendants
    const propagateStatusToChildren = (parentItem, newStatus) => {
      if (!parentItem.children || parentItem.children.length === 0) {
        return; // No children to propagate to
      }

      for (const child of parentItem.children) {
        child.selected_status = newStatus; // Set child's status
        // Recursively propagate to the child's children
        propagateStatusToChildren(child, newStatus);
      }
    };

    // Factory function to create a menu processor
    const createMenuProcessor = (clickedItemId) => {
      let found = false;
      let traversalIdx = []; // Keeps track of the path to the clicked item

      // Inner recursive function
      const processItemRecursive = (menus) => {
        for (let i = 0; i < menus.length; i++) {
          const item = menus[i];

          if (item.id === clickedItemId) {
            // 1. Toggle status of the clicked item
            let newStatus;
            if (item.selected_status === "true") {
              newStatus = "false";
            } else {
              // "false" or "partial" toggles to "true"
              newStatus = "true";
            }
            item.selected_status = newStatus;

            found = true;
            traversalIdx.push(i); // Add current index to path

            // 2. Propagate this new status downwards to children (if any)
            if (item.children && item.children.length > 0) {
              propagateStatusToChildren(item, newStatus);
            }

            break; // Item found, toggled, and propagated (if applicable), stop searching
          }

          if (item.children && item.children.length > 0) {
            traversalIdx.push(i);
            processItemRecursive(item.children);

            if (found) {
              // If a descendant was found and toggled, 'item' is an ancestor (parent).
              // Update 'item' (parent) status based on its children's current statuses.
              updateParentAndSelfStatus(item);
              break; // Stop searching further siblings as the path is found and processed
            } else {
              traversalIdx.pop(); // Backtrack: item not found in this child branch
            }
          }
        }
        return menus; // Return the (modified) array
      };

      // Return a function that will start the process with a given menu array
      return (menus) => {
        found = false; // Reset for each call
        traversalIdx = []; // Reset for each call
        const processedMenus = processItemRecursive(menus);
        // console.log("Path to clicked item:", traversalIdx); // For debugging
        return processedMenus;
      };
    };

    let processMenus = createMenuProcessor(clickedItemId);
    let updatedMenus = processMenus(menuArray);

    // Update the state
    this.setState((prevState) => ({
      inputs: {
        ...prevState.inputs,
        menu_array: menuArray,
      },
    }));
  };

  // ====================================================================================
  // ========== RENDER SECTIONS =========================================================
  // ====================================================================================
  render() {
    let addModalClass = "large";
    return (
      <>
        <Modal open={this.state.showDialog} onClose={this.onCloseDialog}>
          <Box
            sx={{
              flex: 1,
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              display: "flex",
              width: "100wh",
              height: "100vh",
            }}
          >
            <div className={`modal-content ${addModalClass}`}>
              <div className="modal-header">{this.renderHeader()}</div>
              <div className="modal-body">{this.renderBody()}</div>
              <div className="modal-footer">
                {this.renderFooter()}
                <button
                  className="button cancel"
                  onClick={() => this.onCloseDialog()}
                >
                  <i className="ph ph-bold ph-x-circle"></i>
                  <span>
                    {this.state.formType === "info" ? "Tutup" : "Batal"}
                  </span>
                </button>
              </div>
            </div>
          </Box>
        </Modal>

        {/* Loading dialog */}
        <Loading ref={(value) => (this.ref_Loading = value)} />
        <MySnackbar ref={(value) => (this.ref_MySnackbar = value)} />
      </>
    );
  }

  renderHeader() {
    return (
      <>
        <div className="title">
          {this.state.formType === "add" && `Tambah Hak Akses`}
          {this.state.formType === "edit" && `Ubah Hak Akses`}
        </div>
        <span className="close" onClick={() => this.onCloseDialog()}>
          &times;
        </span>
      </>
    );
  }

  renderBody() {
    return (
      <>
        {(this.state.formType === "add" || this.state.formType === "edit") &&
          this.renderForm()}
      </>
    );
  }

  renderForm() {
    return (
      <div className="flex-rows no-right">
        <div className="row no-border">
          <div className="input-form">
            <Input
              id="input-name"
              label="Nama Hak Akses"
              placeholder="Tuliskan disini..."
              value={this.state.inputs.name}
              onChange={(event) => {
                if (
                  event.target !== undefined &&
                  event.target.value !== undefined
                ) {
                  if (event.target.value.length <= 50) {
                    this.onTextInputListeners(event.target.value, "name");
                  }
                }
              }}
              error={
                this.state.errors.name !== undefined &&
                this.state.errors.name !== null
                  ? this.state.errors.name
                  : null
              }
              onFocus={() => this.onTextErrorListeners(null, "name")}
              maxLength={50}
              required
            />
          </div>
          <div className="input-form">
            <div className="label">Menu Hak Akses</div>
            {this.state.isGetMenu && (
              <>
                {/* mui skeleton loading */}

                <div className="flex flex-col gap-4 p-4">
                  {[...Array(5)].map((_, index) => (
                    <div
                      key={index}
                      className="flex flex-row items-center gap-2"
                    >
                      <Skeleton
                        variant="rectangular"
                        width={20}
                        height={20}
                        sx={{ borderRadius: "50%" }}
                      />
                      <Skeleton variant="text" width={100} height={20} />
                    </div>
                  ))}
                </div>
              </>
            )}
            {!this.state.isGetMenu && (
              <>
                <div className="flex flex-col">
                  {this.state.inputs.menu_array.map((item, index) => {
                    return this.renderFormMenuItem(item, index);
                  })}
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    );
  }

  renderFormMenuItem(item, index, tree = 0, isLastChild = false) {
    // render menu tree
    let marginLeft = tree * 0.75; // Increased indentation
    return (
      <>
        <FormControlLabel
          fullWidth
          sx={{
            marginLeft: `${marginLeft}rem`,
            position: "relative", // Needed for absolute positioning
            "&:hover": {
              backgroundColor: "#f0f0f0", // Add hover effect
            },
            ...(isLastChild &&
              tree > 0 && {
                // Conditional styles for last child
                borderLeft: "1px solid #ccc",
                borderBottom: "1px solid #ccc",
                marginBottom: "0.5rem", // Adjust padding to make space for the border
              }),
            ...(!isLastChild &&
              tree > 0 && {
                // Conditional styles for non-last children
                borderLeft: "1px solid #ccc",
              }),
          }}
          control={
            <Checkbox
              checked={item?.selected_status === "true"}
              indeterminate={item?.selected_status === "partial"}
            />
          }
          label={
            <div
              style={{
                display: "flex",
                gap: "0.5rem",
                alignItems: "center",
              }}
            >
              {item.icon_name && <i className={item.icon_name}></i>}
              <span
                style={{
                  textAlign: "left",
                  fontSize: "15px",
                  color: "var(--color-input-label)",
                }}
              >
                {item.name}
              </span>
            </div>
          }
          onChange={() => {
            this.handleMenuItemChange(item.id);
          }}
        />
        {item.children.length > 0 && (
          <Box
            sx={{
              marginLeft: `${marginLeft}rem`, // Adjust margin for children container
              paddingLeft: "0.5rem", // Add padding to the container
            }}
            className="flex flex-col"
          >
            {item.children.map((child, indexChild) => {
              const isLastChild = indexChild === item.children.length - 1;
              return this.renderFormMenuItem(
                child,
                indexChild,
                tree + 1,
                isLastChild
              );
            })}
          </Box>
        )}
      </>
    );
  }

  renderFooter() {
    return (
      <>
        <button
          className={`button ${this.state.formType === "edit" && "warning"} ${
            this.state.formType === "delete" && "danger"
          }`}
          onClick={() => {
            this.onValidateListeners();
          }}
        >
          {this.state.formType === "add" && (
            <i className="ph ph-bold ph-check-circle"></i>
          )}
          {this.state.formType === "edit" && (
            <i className="ph ph-bold ph-pencil"></i>
          )}
          <span>
            {this.state.formType === "add" && "Tambah Data"}
            {this.state.formType === "edit" && "Ubah Data"}
          </span>
        </button>
      </>
    );
  }
}
