/*
Created by esoda
Created on Feb, 2024
Contact esoda.id
Last modified: -
    - Read changelog.md
*/

import React from "react";
import { <PERSON>nac<PERSON>bar, Alert } from "@mui/material";
import Loading from "../modal/Loading";
import ApiHelpers from "@/utils/ApiHelper";

export default class ImageUploadMultiple extends React.Component {
    constructor(props) {
        super(props)
        this.state = {
            dragActive: false
        }
    }
    handleDrag = (e) => {
        e.preventDefault();
        e.stopPropagation();
        if (e.type === "dragenter" || e.type === "dragover") {
            this.setState({ dragActive: true });
        } else if (e.type === "dragleave") {
            this.setState({ dragActive: false });
        }
    }
    handleDrop = (e) => {
        e.preventDefault();
        e.stopPropagation();
        this.setState({ dragActive: false });
        if (e.dataTransfer.files && e.dataTransfer.files[0]) {
            this.onUploadImageListeners(e.dataTransfer.files)
        }
    }
    onUploadImageListeners = async (arrFiles) => {
        this.ref_LoadingDialog.onShowDialog();
        let failedCount = 0
        for (let i = 0; i < arrFiles.length; i++) {
            if (arrFiles[i].size > 1000000) {
                failedCount = failedCount + 1
            } else {
                let formData = new FormData();
                formData.append("image", arrFiles[i]);
                let response = await ApiHelpers.uploadImage(formData);
                if (response.status === 200) {
                    const { onUploaded } = this.props
                    if (onUploaded) { onUploaded(response.data.fileuri) }
                } else {
                    failedCount = failedCount + 1
                }
            }
            console.log(arrFiles[i])
        }
        this.ref_LoadingDialog.onCloseDialog();
        if (failedCount > 0) {
            this.setState({
                showSnackbar: true,
                messageSnackbar: `${failedCount} gambar tidak dapat diunggah. Periksa kembali ukuran atau format gambar.`,
                severitySnackbar: "warning",
            });
        }
    }

    render() {
        return (
            <>
                <label htmlFor="input-image"
                    style={{
                        display: "block",
                        width: this.props.width ? this.props.width : "100%",
                        minWidth: this.props.width ? this.props.width : "100%",
                        height: this.props.height ? this.props.height : "300px",
                        textAlign: "center"
                    }}
                    onDragEnter={(e) => { this.handleDrag(e) }}
                    onDragLeave={(e) => { this.handleDrag(e) }}
                    onDragOver={(e) => { this.handleDrag(e) }}
                    onDrop={async (e) => { this.handleDrop(e) }}
                    >
                    <input type="file" accept="image/*" id="input-image" hidden
                        onChange={async (e) => {
                            this.onUploadImageListeners(e.target.files)
                        }}
                        multiple={true}
                    />
                    <div style={{
                        width: "100%",
                        height: "100%",
                        borderRadius: "3px",
                        border: `2px dashed ${this.state.dragActive ? "var(--third-color)" : "#bdc3c7"}`,
                        backgroundColor: this.props.backgroundColor ? this.props.backgroundColor : "#fff",
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "center",
                        justifyContent: "center"
                        }}>
                        {/* <Icon style={{ fontSize: 70, color: "#bdc3c7" }}>cloud_upload</Icon> */}
                        <i className="ph ph-bold ph-cloud-arrow-up" style={{ fontSize: 70, color: "#bdc3c7" }}></i>
                        <p className="double_line" style={{ fontSize: "13px", color: "#636e72", lineHeight: 1.3 }}>Drag & drop atau tekan disini <br />untuk unggah gambar.</p>
                        <span className="double_line" style={{
                            fontSize: "10px",
                            color: "#b2bec3",
                            marginTop: "10px"
                        }}>Unggah gambar dari perangkat Anda</span>
                    </div>
                </label>

                {/* LOADING DIALOG */}
                <Loading ref={(value) => (this.ref_LoadingDialog = value)} />

                {/* SNACKBAR & ALERT DIALOG */}
                <Snackbar
                    anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
                    style={{ bottom: 80 }}
                    open={this.state.showSnackbar}
                    autoHideDuration={4600}
                    onClose={() => {
                        this.setState({ showSnackbar: false, messageSnackbar: "" });
                    }}>
                    <Alert
                        severity={this.state.severitySnackbar}
                        onClose={() => {
                            this.setState({ showSnackbar: false, messageSnackbar: "" });
                        }}
                        sx={{ width: "100%" }}>
                        {this.state.messageSnackbar}
                    </Alert>
                </Snackbar>
            </>
        )
    }
}