# Detailed Implementation Plan: Affiliate Flow Integration

This plan outlines the modifications required in `src/pages/pos/payment/index.js` and `src/store/kiosk/pos/payment/store.js` to implement the affiliate flow.

#### 1. Overview of Changes

The affiliate flow will involve:
- Adding two new input fields (`affiliate_code` and `affiliate_verif_code`) in the `RenderPaymentSummary` component.
- A "Send OTP" button to trigger OTP sending and conditionally render `affiliate_verif_code`.
- Updating the payment store to manage the state of these new inputs.
- Integrating with the existing snackbar notification system.
- Uncommenting and modifying the existing affiliate section for full functionality.

#### 2. File: `src/store/kiosk/pos/payment/store.js` Modifications

**Goal:** Add new state properties and extend `onTextInputListeners`.

**Steps:**

1.  **Add new state properties:**
    -   Inside the `DEFAULT_INPUTS` object (around line 9), add `affiliate_code` and `affiliate_verif_code` with initial empty string values.
    -   Add a new state property `isAffiliateOTPSent` to control the conditional rendering of `affiliate_verif_code`, initialized to `false`.

2.  **Extend `onTextInputListeners`:**
    -   The existing `onTextInputListeners` function (around line 180) already handles dynamic input updates. No direct modification is needed here, as it will automatically handle the new `affiliate_code` and `affiliate_verif_code` properties once they are added to `DEFAULT_INPUTS`.

3.  **Add `onSendAffiliateOTP` action:**
    -   Create a new asynchronous action `onSendAffiliateOTP` that will:
        -   Show the loading spinner (`onLoading(true)`).
        -   Simulate an API call (for now, just a `setTimeout`).
        -   On success, set `isAffiliateOTPSent` to `true` and show a success snackbar "OTP sent to affiliate" using `onNotify`.
        -   On failure, show an error snackbar.
        -   Hide the loading spinner (`onLoading(false)`).

#### 3. File: `src/pages/pos/payment/index.js` Modifications

**Goal:** Implement UI for affiliate inputs, "Send OTP" button, conditional rendering, and snackbar.

**Steps:**

1.  **Import `useState` and `useRef`:**
    -   Ensure `useState` and `useRef` are imported from `react` at the top of the file (around line 7).

2.  **Update `RenderPaymentSummary` component:**
    -   **Access state from `useTrackedPaymentStore`:**
        -   Destructure `inputs`, `onTextInputListeners`, `onSendAffiliateOTP`, and `isAffiliateOTPSent` from `useTrackedPaymentStore()`.
    -   **Uncomment and modify affiliate section:**
        -   Locate the commented-out affiliate section (lines 554-576).
        -   Uncomment this block.
        -   **Affiliate Code Input Field:**
            -   The existing `Input` component for `affiliate_code` (lines 557-574) already has the correct styling and `onChange` handler. Ensure `maxLength={6}` is present.
            -   Add a "Send OTP" button next to the `affiliate_code` input.
                -   Use the class `button info`.
                -   On `onClick`, call `onSendAffiliateOTP`.
                -   The button should be disabled if `affiliate_code` is empty or `isAffiliateOTPSent` is true.
        -   **Conditional Rendering for Affiliate Verification Code:**
            -   Wrap the `affiliate_verif_code` input field within a conditional block that renders only when `isAffiliateOTPSent` is `true`.
            -   This input field should also use the `Input` component with `maxLength={6}` and connect to `inputs.affiliate_verif_code` via `onTextInputListeners`.
            -   The styling should be similar to `voucher_verif_code`.

#### 4. React Hooks for Conditional Rendering

-   The `isAffiliateOTPSent` state property in `src/store/kiosk/pos/payment/store.js` will be used to control the conditional rendering.
-   In `src/pages/pos/payment/index.js`, within `RenderPaymentSummary`, you will use a simple conditional `&&` or ternary operator to render the `affiliate_verif_code` input:

    ```jsx
    {isAffiliateOTPSent && (
      <div className={styles.nominals}>
        <label>Kode Verifikasi Affiliate</label>
        <div>
          <Input
            className="text-right"
            inputType="text"
            value={inputs.affiliate_verif_code}
            onChange={(event) => {
              if (event.target?.value !== undefined) {
                usePaymentStore
                  .getState()
                  .onTextInputListeners(event.target.value, "affiliate_verif_code");
              }
            }}
            required
            maxLength={6}
          />
        </div>
      </div>
    )}
    ```

#### 5. Snackbar Implementation Details

-   The `ref_MySnackbar` is already set up in `KioskPaymentPage` and accessible via `usePaymentStore.getState().ref_MySnackbar`.
-   The `onNotify` function in `src/store/kiosk/pos/payment/store.js` (around line 160) is the standard way to show snackbar messages.
-   When the "Send OTP" button is clicked and the (simulated) API call is successful, `onNotify("OTP sent to affiliate", "success")` will be called from within the `onSendAffiliateOTP` action in the store.

#### 6. State Management Updates Summary

-   `src/store/kiosk/pos/payment/store.js`:
    -   `DEFAULT_INPUTS`: Add `affiliate_code: ""` and `affiliate_verif_code: ""`.
    -   New state: `isAffiliateOTPSent: false`.
    -   New action: `onSendAffiliateOTP` to handle OTP sending logic, update `isAffiliateOTPSent`, and show snackbar.
-   `src/pages/pos/payment/index.js`:
    -   `RenderPaymentSummary` component will consume `affiliate_code`, `affiliate_verif_code`, `isAffiliateOTPSent` from the store.
    -   It will render the `affiliate_code` input and "Send OTP" button.
    -   It will conditionally render the `affiliate_verif_code` input based on `isAffiliateOTPSent`.
    -   `onTextInputListeners` will be used for both new input fields.

#### 7. Mermaid Diagram for Flow

```mermaid
graph TD
    A[User on Payment Page] --> B{RenderPaymentSummary Component};
    B --> C[Affiliate Code Input Field];
    C --> D[Send OTP Button];
    D -- onClick --> E[onSendAffiliateOTP Action in Store];
    E -- API Call (Simulated) --> F{OTP Sent Successfully?};
    F -- Yes --> G[Set isAffiliateOTPSent = true];
    F -- Yes --> H[Show Snackbar "OTP sent to affiliate"];
    F -- No --> I[Show Error Snackbar];
    G --> J{RenderPaymentSummary Re-renders};
    J -- isAffiliateOTPSent is true --> K[Affiliate Verification Code Input Field (Conditionally Rendered)];
    K -- User enters code --> L[onTextInputListeners in Store];