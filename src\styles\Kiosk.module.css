.loading { width: 100%; height: 100vh; background-color: #ffffff; display: flex; flex-direction: column; align-items: center; justify-content: center; }
.loading h3 { font-size: 5rem; text-align: center; }
.loading h2 { color: #f39c12; font-size: 3rem; margin-top: -1.5rem; text-align: center; }
.loading h4 { font-weight: 500; }
.loading p { margin-top: 1.5rem; font-size: 1.7rem; text-align: center; }
.loading button { margin-top: 4rem; height: 80px; padding-left: 1.6rem; padding-right: 1.6rem; background-color: #f39c12; }
.loading button:hover { background-color: #e67e22; }
.loading button i { font-size: 2.5rem; }
.loading button span { font-size: 2rem; margin-left: .5rem; }
.loading .overlay { position: absolute; top: 0; left: 0; z-index: 0; width: 100%; height: 100vh; object-fit: cover; opacity: .15; }
.loading .top { padding-top: 3rem; z-index: 1; }
.loading .top img { height: 42px; }
.loading .center { flex-grow: 1; display: flex; flex-direction: column; align-items: center; justify-content: center; padding: 0 5rem; z-index: 1; }
.loading .bottom { padding-bottom: 3rem; z-index: 1; }

.kiosk { display: flex; }

.kiosk .categories { width: 260px; height: 100vh; background-color: #fcfcfc; overflow: auto; display: flex; flex-direction: column; }
.kiosk .categories .logo { position: sticky; top: 0; left: 0; background-color: #fff; padding: 1.5rem; display: flex; align-items: center; justify-content: center; z-index: 1; }
.kiosk .categories .logo img { height: 35px; }
.kiosk .categories .item:last-child { border-bottom: solid 1px #f3f3f3; }
.kiosk .categories .item,
.kiosk .categories .subs,
.kiosk .categories .menus { background-color: #ffffff; padding: 1rem; cursor: pointer; color: #2d3436; position: relative; border-top: solid 1px #f3f3f3; }
.kiosk .categories .subs { padding: .5rem 1rem .5rem 1rem; border-left: solid 1px #dedede; margin-left: 18px; }
.kiosk .categories .menus { padding: .5rem 1rem .5rem 1rem; border-left: solid 1px #dedede; margin-left: 37px; }
.kiosk .categories .item .title, 
.kiosk .categories .subs .title,
.kiosk .categories .menus .title { display: flex; flex-direction: row; align-items: center; justify-content: space-between; }
.kiosk .categories .item .title .name,
.kiosk .categories .subs .title .name,
.kiosk .categories .menus .title .name { flex-grow: 1; padding-right: 1rem; }
.kiosk .categories .item .title .name div:first-child,
.kiosk .categories .subs .title .name div:first-child,
.kiosk .categories .menus .title .name div:first-child { font-size: 1.05rem; font-weight: 600; text-transform: capitalize; }
.kiosk .categories .subs .title .name div:first-child,
.kiosk .categories .menus .title .name div:first-child { font-size: 1.0rem; }
.kiosk .categories .item .title .name div:last-child,
.kiosk .categories .subs .title .name div:last-child,
.kiosk .categories .menus .title .name div:last-child { font-size: .75rem; font-weight: 500; margin-top: -.2rem; color: #525252; }
.kiosk .categories .item::after,
.kiosk .categories .subs::after,
.kiosk .categories .menus::after {
    content: "";
    height: 100%;
    width: 4px;
    background-color: #e67e22;
    position: absolute;
    right: 0;
    top: 0;
    visibility: hidden;
}
.kiosk .categories .menus::after {
    background-color: #f39c12;
}
.kiosk .categories .subs::after {
    background-color: #ffac28;
}
.kiosk .categories .subs::before,
.kiosk .categories .menus::before {
    content: "";
    height: 7px;
    width: 7px;
    border-radius: 7px;
    background-color: #dedede;
    position: absolute;
    left: -4px;
    top: 1.5rem;
}
.kiosk .categories .menus::before { left: -4px; }
.kiosk .categories .item:hover::after,
.kiosk .categories .item.active::after,
.kiosk .categories .subs:hover::after,
.kiosk .categories .subs.active::after,
.kiosk .categories .menus:hover::after,
.kiosk .categories .menus.active::after { visibility: visible; }
.kiosk .categories .item:hover,
.kiosk .categories .item.active,
.kiosk .categories .subs:hover,
.kiosk .categories .subs.active,
.kiosk .categories .menus:hover,
.kiosk .categories .menus.active { color: #e67e22; }

.kiosk .topbar { background-color: #fcfcfc; position: sticky; top: 0; padding: 1.5rem 1.5rem; z-index: 1; }
.kiosk .topbar .store { display: flex; flex-direction: row; align-items: center; justify-content: space-between; padding-bottom: .92rem; }
.kiosk .topbar .store .info { flex: 1; padding-right: 1.5rem; }
.kiosk .topbar .store .info .name { font-size: 1rem; color: var(--text-primary-color); font-weight: 700; text-transform: capitalize; }
.kiosk .topbar .store .info .address { font-size: .8rem; color: var(--text-foreign-color); margin-top: -4px; text-transform: capitalize; }
.kiosk .topbar .store .date .time_picker { font-size: 0.85rem; color: var(--text-primary-color); }
.kiosk .topbar .store .date .cashier { color: var(--text-primary-color); margin-bottom: -3px; text-align: right; text-transform: capitalize; font-size: .9rem; font-weight: 600; }
.kiosk .topbar .store .member {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 1rem;
  padding: 0.5rem 1rem;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: -8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(*********** / 25%);
  border: solid 1px #ececec;
  margin-right: 1rem;
}

.kiosk .topbar .store .member .info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding-right: 0px;
}

.kiosk .topbar .store .member .info .name {
  font-size: 1rem;
  font-weight: 600;
  color: #2d3436;
}

.kiosk .topbar .store .member .info .badge {
  font-size: 0.8rem;
  color: #e67e22;
  font-weight: 500;
}

.kiosk .topbar .store .member .actions {
  display: flex;
  gap: 0.5rem;
}

.kiosk .topbar .store .member .actions button {
  height: 36px;
  width: 36px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  background-color: transparent;
  border: 1px solid #e67e22;
  color: #e67e22;
  transition: all 0.2s ease;
}

.kiosk .topbar .store .member .actions button:hover {
  background-color: #e67e22;
  color: #fff;
}

.kiosk .topbar .store .member .actions button i {
  font-size: 1.2rem;
}

.kiosk .topbar .searchbar, 
.kiosk_checkin_input { display: flex; flex-direction: row; align-items: center; gap: 1rem; }
.kiosk_checkin_input { 
    /* margin-top: 1rem; border-top: solid 1px rgb(237, 237, 237); padding-top: 1rem;  */
}

.kiosk .topbar .searchbar a,
.kiosk_checkin_input a {
    height: 50px;
    width: 50px;
    border-radius: 2px;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    -ms-border-radius: 2px;
    -o-border-radius: 2px;
    background-color: #2d3436;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    cursor: pointer;
}
.kiosk_checkin_input a { width: auto; padding: 0 1rem; font-size: 1.2rem; color: #FFFFFF; font-weight: 600; }

.kiosk .topbar .searchbar a:not(:first-child),
.kiosk_checkin_input a:not(:first-child) { margin-left: 0; }

.kiosk .topbar .searchbar a i { font-size: 1.8rem; color: #FFFFFF; }

.kiosk .topbar .searchbar .input_search,
.kiosk_checkin_input .input_search {
    flex-grow: 1;
    display: flex;
    align-items: center;
    border: 1px solid #ececec;
    background-color: #fff;
    box-shadow: -8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(*********** / 25%);
    border-radius: 2px;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    -ms-border-radius: 2px;
    -o-border-radius: 2px;
    overflow: hidden;
}

.kiosk .topbar .searchbar .input_search label,
.kiosk_checkin_input .input_search label {
    flex-shrink: 0;
    height: 50px;
    width: 50px;
    background-color: #fff;
    color: #2d3436;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-weight: 500;
    padding-right: 5px;
}

.kiosk_checkin_input .input_search label { padding-right: 0; justify-content: center; }

.kiosk .topbar .searchbar .input_search label i,
.kiosk_checkin_input .input_search label i { font-size: 1.5rem; }

.kiosk .topbar .searchbar .input_search input,
.kiosk_checkin_input .input_search input {
    box-sizing: border-box;
    flex-grow: 1;
    min-width: 0;
    height: 50px;
    padding: 1em;
    font: inherit;
    transition: 150ms ease;
    -webkit-transition: 150ms ease;
    -moz-transition: 150ms ease;
    -ms-transition: 150ms ease;
    -o-transition: 150ms ease;
    background-color: #fff;
    border: none;
    outline: none;
    font-size: 1.1rem;
}
.kiosk_checkin_input .input_search input { padding: 1em 1em 1em 0; }
.kiosk .topbar .searchbar .input_search:has(input:hover)>label,
.kiosk .topbar .searchbar .input_search:has(input:focus)>label, 
.kiosk_checkin_input .input_search:has(input:hover)>label,
.kiosk_checkin_input .input_search:has(input:focus)>label { color: #e67e22; }
.kiosk .topbar .searchbar .input_search:has(input:hover),
.kiosk .topbar .searchbar .input_search:has(input:focus), 
.kiosk_checkin_input .input_search:has(input:hover),
.kiosk_checkin_input .input_search:has(input:focus) { border-color: rgba(230, 126, 34, .5); }
.kiosk .topbar .search_results { background-color: #f1f1f1; color: #2d3436; padding: .5rem 1rem; margin-top: 1.2rem; border-radius: 2px; font-weight: 600; font-size: 1.1rem; }
.kiosk .topbar .search_results span { font-weight: normal; }

.kiosk .contents {
    width: calc(100% - 260px);
    height: 100vh;
    background-color: #fcfcfc;
    overflow: auto;
    display: flex;
    flex-direction: column;
    border-left: solid 1px #f3f3f3;
}

.kiosk .contents .products { display: grid; grid-template-columns: repeat(auto-fit, minmax(240px, 1fr)); 
    grid-auto-rows: max-content; 
    gap: 1.5rem; justify-content: left; padding-left: 1.5rem; padding-right: 1.5rem; padding-bottom: 1.5rem; flex-grow: 1; }
.kiosk .contents .products[data-length="1"],
.kiosk .contents .products[data-length="2"] { grid-template-columns: repeat(auto-fit, minmax(240px, 240px)); }
.kiosk .contents .products .item {
    background-color: #fff;
    box-shadow: -8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(*********** / 25%);
    border: solid 1px #ececec;
    border-radius: 2px;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    -ms-border-radius: 2px;
    -o-border-radius: 2px;
    overflow: hidden;
    cursor: pointer;
    padding: .8rem;
    display: flex;
    flex-direction: column;
    /* height: 100%; */
}
.kiosk .contents .products .item img { height: 200px; object-fit: cover; border-radius: 2px; -webkit-border-radius: 2px; -moz-border-radius: 2px; -ms-border-radius: 2px; -o-border-radius: 2px; }
.kiosk .contents .products .item .tags { padding: 10px 5px 5px; display: flex; flex-direction: row; align-items: center; flex-wrap: wrap; }
.kiosk .contents .products .item .tags div { font-size: .8rem; color: #2d3436; }
.kiosk .contents .products .item .tags div:not(:first-child)::before { content: "/"; padding: 0 .1rem; font-size: .8rem; }
.kiosk .contents .products .item .title { 
    font-weight: 700; font-size: 1.1rem; line-height: 22px; padding: 0px 5px 5px; 
    line-break: anywhere;
    flex-grow: 1;
    /* flex: 1 auto;  */
    color: #2d3436; text-transform: capitalize; }
.kiosk .contents .products .item .price { display: flex; align-items: center; justify-content: space-between; padding: 0 5px; color: #e67e22; }
.kiosk .contents .products .item .price>div { font-weight: 700; font-size: 1.2rem; }
.kiosk .contents .products .item .price>div:last-child { font-size: .8rem; color: #2d3436; }
.kiosk .contents .products .item button {
    border: none;
    background-color: rgba(230, 126, 34, .1);
    margin: 15px 5px 5px;
    padding: 8px 10px;
    font-weight: 600;
    color: #e67e22;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
    cursor: pointer;
    font-size: 1.1rem;
    text-transform: uppercase;
}
.kiosk .contents .products .item:hover button,
.kiosk .contents .products .item button:hover,
.kiosk .contents .products .item.active button { background-color: #e67e22; color: #fff; }
.kiosk .contents .products .item:hover,
.kiosk .contents .products .item.active { border-color: rgba(230, 126, 34, .5); }
.kiosk .contents .products .item.disabled .title,
.kiosk .contents .products .item.disabled .tags div,
.kiosk .contents .products .item.disabled .price>div { color: #cbcbcb; }
.kiosk .contents .products .item.disabled button { background-color: rgb(247 247 247); color: rgb(214, 214, 214); }

.kiosk .contents .cart { background-color: #fff; position: sticky; bottom: 0; border-top: solid 1px #f3f3f3; padding: .7rem 1.5rem; display: flex; flex-direction: row; align-items: center; gap: 1rem; }

.kiosk .contents .cart .left { 
    /* width: 100%;  */
}
.kiosk .contents .cart .left .qty { font-size: 1.2rem; }
.kiosk .contents .cart .left .qty b { font-size: 1.5rem; }
.kiosk .contents .cart .left .price { font-size: 2rem; font-weight: bold; margin-top: -.5rem; color: #e67e22; }

.kiosk .contents .cart .right { 
    /* width: 100%;  */
    margin-left: auto;
    display: flex; flex-direction: row; align-items: center; justify-content: space-between; gap: .5rem; }
.kiosk .contents .cart .right button { align-items: center; justify-content: center; border-radius: 2px; -webkit-border-radius: 2px; -moz-border-radius: 2px; -ms-border-radius: 2px; -o-border-radius: 2px; height: 60px; 
    /* width: 60px; */
    padding: 0 1rem;
    background-color: #f39c12;
}
.kiosk .contents .cart .right button i { font-size: 2rem; }
.kiosk .contents .cart .right button span { font-size: 1.3rem; font-weight: bold; }
.kiosk .contents .cart .right button:first-child { flex-grow: 1; }
.kiosk .contents .cart .right button:not(:first-child) i { margin-right: 0; }

.kiosk .categories::-webkit-scrollbar,
/* .kiosk .products::-webkit-scrollbar,  */
.kiosk .contents::-webkit-scrollbar, 
.kiosk_payment .payments::-webkit-scrollbar,
.kiosk_payment .catalogs::-webkit-scrollbar { width: 0px; height: 0px; }
.kiosk .categories::-webkit-scrollbar-thumb,
/* .kiosk .products::-webkit-scrollbar-thumb,  */
.kiosk .contents::-webkit-scrollbar-thumb, 
.kiosk_payment .payments::-webkit-scrollbar-thumb, 
.kiosk_payment .catalogs::-webkit-scrollbar-thumb { border-radius: .2rem; -webkit-border-radius: .2rem; -moz-border-radius: .2rem; -ms-border-radius: .2rem; -o-border-radius: .2rem; background-color: #e67e22; }

.kiosk_checkin { display: flex; flex-direction: row; align-items: center; justify-content: center; gap: 1rem; }
.kiosk_checkin>div {
    background-color: #ffffff;
    border-radius: 2px;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    -ms-border-radius: 2px;
    -o-border-radius: 2px;
    box-shadow: -8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(*********** / 25%);
    border: solid 1px #ececec;
    padding: 1rem;
    cursor: pointer;
    color: #2d3436;
    width: 100%;
    text-align: center;
    font-weight: 600;
    font-size: 1.1rem;
}
.kiosk_checkin>div:hover,
.kiosk_checkin>div.active { border-color: rgba(230, 126, 34, .5); color: #e67e22; }

.kiosk_payment { display: flex; flex-direction: column; overflow: auto; }

.kiosk_payment .catalogs { height: 45vh; background-color: #fcfcfc; width: 100%; overflow: auto; display: flex; flex-direction: column; }

.kiosk_payment .catalogs .topbar { background-color: #fcfcfc; position: sticky; top: 0; padding: 1.2rem 2rem 0 2rem; z-index: 1; }
.kiosk_payment .catalogs .topbar .store { display: flex; flex-direction: row; align-items: center; justify-content: space-between; padding-bottom: .92rem; }
.kiosk_payment .catalogs .topbar .store .info { flex: 1; padding: 0 1rem; }
.kiosk_payment .catalogs .topbar .store .info .title { font-size: 1.2rem; color: var(--text-primary-color); font-weight: 600; text-transform: capitalize; }
.kiosk_payment .catalogs .topbar .store .info .subtitle { font-size: 1rem; color: var(--text-foreign-color); margin-top: -5px; text-transform: capitalize; }
.kiosk_payment .catalogs .topbar .store .date .time_picker { font-size: 0.85rem; color: var(--text-primary-color); }
.kiosk_payment .catalogs .topbar .store .date .merchant { color: var(--text-primary-color); margin-bottom: -3px; text-align: right; text-transform: capitalize; font-size: 1rem; font-weight: 600; }
.kiosk_payment .catalogs .topbar .store a {
    height: 40px;
    width: 40px;
    border-radius: 2px;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    -ms-border-radius: 2px;
    -o-border-radius: 2px;
    background-color: #2d3436;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    cursor: pointer;
}
.kiosk_payment .catalogs .topbar .store a:not(:first-child) { margin-left: 0; }
.kiosk_payment .catalogs .topbar .store a i { font-size: 1.4rem; color: #FFFFFF; }

.kiosk_payment .catalogs .carts { flex-grow: 1; margin: 0 2rem; background-color: #f3f3f3; border: 1px solid #ececec; box-shadow: -8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(*********** / 25%); overflow: auto; }
.kiosk_payment .catalogs .carts table { width: 100%; }
.kiosk_payment .catalogs .carts table,
.kiosk_payment .catalogs .carts th,
.kiosk_payment .catalogs .carts td { color: #2d3436; border-collapse: collapse; text-align: left; text-wrap: nowrap; font-size: 1rem; font-weight: normal; }
.kiosk_payment .catalogs .carts thead th { padding: .6rem 1rem; z-index: 1; position: sticky; top: 0; left: 0; background-color: #2d3436; color: #FFFFFF; font-weight: 600; }
.kiosk_payment .catalogs .carts td { padding: 1rem 1rem; background-color: #FFFFFF; }
.kiosk_payment .catalogs .carts tr:nth-child(odd) td { background-color: #f9f9f9; }
.kiosk_payment .catalogs .carts table td .rows { display: flex; flex-direction: row; align-items: center; justify-content: space-between; }
.kiosk_payment .catalogs .carts table td .rows img { width: 50px; height: 50px; object-fit: contain; background-color: #ffffff; border: solid 1px #e7e7e7; border-radius: 2px; }
.kiosk_payment .catalogs .carts table td .rows div { font-weight: 600; flex-grow: 1; padding-left: 1rem; display: flex; flex-direction: column; font-size: 1rem; }
.kiosk_payment .catalogs .carts table td .rows div span { display: flex; flex-direction: row; align-items: center; padding-top: .1rem; }
.kiosk_payment .catalogs .carts table td .rows div span input { outline: none; border: none; font-size: .8rem; flex: 1; padding-left: .3rem; background-color: inherit; }
.kiosk_payment .catalogs .carts table table tr td:first-child { position: sticky; top: 0; left: 0; }
.kiosk_payment .catalogs .carts table td .rows div span input::placeholder { color: rgb(197, 197, 197); }
.kiosk_payment .catalogs .carts table td .rows div span i { color: rgb(127, 127, 127); font-size: 14px; }
.kiosk_payment .catalogs .carts table td .rows div span:has(input:hover) i,
.kiosk_payment .catalogs .carts table td .rows div span:has(input:focus) i { color: #e67e22; }
.kiosk_payment .catalogs .carts .qty_price { display: flex; flex-direction: row; align-items: center; }
.kiosk_payment .catalogs .carts .qty_price button {
    width: 28px;
    height: 28px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    -ms-border-radius: 2px;
    -o-border-radius: 2px;
}
.kiosk_payment .catalogs .carts .qty_price button i { margin-right: 0; font-size: 1rem; }
.kiosk_payment .catalogs .carts .qty_price>input {
    width: 50px;
    height: 28px;
    margin-left: 10px;
    border: solid 1px #c8c8c8;
    border-radius: 2px;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    -ms-border-radius: 2px;
    -o-border-radius: 2px;
    padding: 0 .3rem;
    outline: none;
}

.kiosk_payment .catalogs .empty { display: flex; flex-direction: column; align-items: center; justify-content: center; text-align: center; color: var(--text-foreign-color); padding: 0 2.5rem; height: 100%; }
.kiosk_payment .catalogs .empty .title { font-size: 1.2rem; font-weight: 600; }
.kiosk_payment .catalogs .empty .subtitle { font-size: .85rem; margin-top: .5rem; }

.kiosk_payment .catalogs .summaries { padding: 1rem 2rem; display: flex; flex-direction: row; position: sticky; bottom: 0; z-index: 1; background-color: #fcfcfc; }
.kiosk_payment .catalogs .summaries .left { width: 57%; padding-right: 1rem; }
.kiosk_payment .catalogs .summaries .left .item { width: 100%; display: flex; flex-direction: row; align-items: center; justify-content: space-between; flex-wrap: wrap; }
.kiosk_payment .catalogs .summaries .left .item div { font-weight: 600; font-size: 1.1rem; }
.kiosk_payment .catalogs .summaries .left .item:not(:first-child) { border-top: dotted 1px #9f9f9f; margin-top: .5rem; padding-top: .5rem; }
.kiosk_payment .catalogs .summaries .right {
    width: 43%;
    padding-left: 1rem;
    align-items: flex-end;
    justify-content: flex-end;
    text-align: right;
    border-radius: 3px;
    background-color: #FFFFFF;
    border: 1px solid #ececec;
    box-shadow: -8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(*********** / 25%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
.kiosk_payment .catalogs .summaries .right div { color: #525252; }

.kiosk_payment .payments { height: 55vh; background-color: #ffffff; width: 100%; border-left: solid 1px #ececec; overflow: auto; display: flex; flex-direction: column; }

.kiosk_payment .payments .customers { border-bottom: solid 1px rgb(235, 235, 235); position: sticky; top: 0; background-color: #fff; padding: 1.2rem 2rem; }
.kiosk_payment .payments .customers .detail { display: flex; flex-direction: row; align-items: center; justify-content: space-between; gap: 1rem; }
.kiosk_payment .payments .customers .detail img { width: 50px; height: 50px; object-fit: cover; background-color: #ffffff; border: solid 1px #e7e7e7; border-radius: 50px; }
.kiosk_payment .payments .customers .detail .info { flex-grow: 1; }
.kiosk_payment .payments .customers .detail .info .name { font-weight: 600; font-size: 1.5rem; }
.kiosk_payment .payments .customers .detail .info .badge { font-size: 1rem; margin-top: -8px; }
.kiosk_payment .payments .customers .detail .poin { text-align: right; }
.kiosk_payment .payments .customers .detail .poin .name { font-size: 1rem; }
.kiosk_payment .payments .customers .detail .poin .badge { font-weight: 700; font-size: 1.5rem; margin-top: -8px; color: #27ae60; }

.kiosk_payment .payments .methods { padding: 1.2rem 2rem; }
.kiosk_payment .payments .methods .items { display: grid; grid-template-columns: repeat(auto-fit, minmax(190px, 1fr)); gap: 1.2rem; justify-content: center; padding-top: .5rem; }
.kiosk_payment .payments .methods .items .item {
    background-color: #fff;
    box-shadow: -8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(*********** / 25%);
    border: solid 1px #ececec;
    border-radius: 2px;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    -ms-border-radius: 2px;
    -o-border-radius: 2px;
    overflow: hidden;
    cursor: pointer;
    padding: .9rem .6rem .6rem .6rem;
    display: flex;
    flex-direction: column;
    align-items: center;
}
.kiosk_payment .payments .methods .items .item img { width: 40px; height: 40px; object-fit: contain; background-color: #fcfcfc; }
.kiosk_payment .payments .methods .items .item .title { font-size: .8rem; color: var(--text-primary-color); margin-top: .5rem; text-align: center; font-weight: 600; font-size: 1.2rem; }
.kiosk_payment .payments .methods .items .item.active,
.kiosk_payment .payments .methods .items .item:hover { border-color: rgba(230, 126, 34, .5); background-color: rgba(230, 126, 34, .1); }
.kiosk_payment .payments .methods .items .item:hover .title,
.kiosk_payment .payments .methods .items .item.active .title { color: #e67e22; }

.kiosk_payment .payments .summaries { padding: .5rem 2rem 6rem 2rem; }
.kiosk_payment .payments .summaries .nominals {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    box-shadow: -8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(*********** / 25%);
    border: solid 1px #ececec;
    border-radius: 2px;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    -ms-border-radius: 2px;
    -o-border-radius: 2px;
}
.kiosk_payment .payments .summaries .nominals:has(input:not(:disabled):hover),
.kiosk_payment .payments .summaries .nominals:has(input:not(:disabled):focus) { border-color: rgba(230, 126, 34, .5); }
.kiosk_payment .payments .summaries .nominals:not(:first-child) { margin-top: 1rem; }
.kiosk_payment .payments .summaries .nominals label {
    font-size: .8rem;
    height: 50px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
    background-color: #ebebeb;
    font-weight: 600;
    padding: 0 .7rem;
    color: var(--text-foreign-color);
    width: 300px;
    text-transform: uppercase;
    text-align: right;
    font-size: 1.2rem;
}
.kiosk_payment .payments .summaries .nominals input {
    width: 100%;
    text-align: right;
    font-size: 1.2rem;
    font-weight: 600;
    height: 50px;
    padding: 0 .7rem;
    outline: none;
    border: none;
    color: var(--text-primary-color);
}
.kiosk_payment .payments .summaries .payment_input { display: flex; flex-direction: row; align-items: center; margin: 1rem 0; }
.kiosk_payment .payments .summaries .payment_input input {
    outline: none;
    border: 1px solid #ececec;
    background-color: #fff;
    box-shadow: -8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(*********** / 25%);
    height: 50px;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--base-color);
    flex: 1;
    padding: 0 1rem;
    text-align: right;
    border-radius: 2px 0px 0px 2px;
    -webkit-border-radius: 2px 0px 0px 2px;
    -moz-border-radius: 2px 0px 0px 2px;
    -ms-border-radius: 2px 0px 0px 2px;
    -o-border-radius: 2px 0px 0px 2px;
}
.kiosk_payment .payments .summaries .payment_input input:hover,
.kiosk_payment .payments .summaries .payment_input input:focus { border: solid 1px var(--base-color); }
.kiosk_payment .payments .summaries .payment_input:has(button:hover) input { border: solid 1px var(--base-color); }
.kiosk_payment .payments .summaries .payment_input button {
    height: 50px;
    border-radius: 0px 2px 2px 0px;
    -webkit-border-radius: 0px 2px 2px 0px;
    -moz-border-radius: 0px 2px 2px 0px;
    -ms-border-radius: 0px 2px 2px 0px;
    -o-border-radius: 0px 2px 2px 0px;
    margin-left: 0;
}
.kiosk_payment .payments .summaries .payment_nominal {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: .8rem;
    justify-content: center;
    padding-bottom: .5rem;
}
.kiosk_payment .payments .summaries .payment_nominal .item {
    background-color: #fff;
    box-shadow: -8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(*********** / 25%);
    border: solid 1px #ececec;
    border-radius: 2px;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    -ms-border-radius: 2px;
    -o-border-radius: 2px;
    overflow: hidden;
    cursor: pointer;
    padding: 1rem;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    font-size: .9rem;
    color: var(--text-foreign-color);
}
.kiosk_payment .payments .summaries .payment_nominal .item:hover { border-color: rgba(230, 126, 34, .5); color: #e67e22; font-weight: 600; }

.kiosk_payment .payments .payment_action {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    position: fixed;
    bottom: 0;
    padding: 1.2rem 2rem;
    background-color: #FFFFFF;
}
.kiosk_payment .payments .payment_action button {
    align-items: center;
    justify-content: center;
    border-radius: 2px;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    -ms-border-radius: 2px;
    -o-border-radius: 2px;
    height: 60px;
    width: auto;
    padding: 0 1.5rem;
    background-color: #e67e22;
}
.kiosk_payment .payments .payment_action button span { font-size: 1.2rem; }
.kiosk_payment .payments .payment_action button i { font-size: 1.5rem; margin-right: 1rem; }
.kiosk_payment .payments .payment_action button:first-child { flex-grow: 1; }
.kiosk_payment .payments .payment_action button:not(:first-child) i { margin-right: 0; }

.kiosk_payment .payments .summaries .affiliate_section {
    display: flex;
    flex-direction: row;
    align-items: stretch;
    justify-content: space-between;
    background-color: #fff;
    box-shadow: -8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(*********** / 25%);
    border: solid 1px #ececec;
    border-radius: 2px;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    -ms-border-radius: 2px;
    -o-border-radius: 2px;
    margin-top: 1rem;
    padding: 0;
}

.kiosk_payment .payments .summaries .affiliate_section>label {
    font-size: 1.2rem;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
    background-color: #ebebeb;
    font-weight: 600;
    padding: 0 .7rem;
    color: var(--text-foreign-color);
    width: 300px;
    text-transform: uppercase;
    text-align: right;
}

.kiosk_payment .payments .summaries .affiliate_section .input_and_button_container {
    width: 100%;
    flex-grow: 1;
    display: flex;
    justify-content: flex-end;
    align-items: flex-end;
    padding: .7rem;
    gap: 1rem;
}

.kiosk_payment .payments .summaries .affiliate_section .input_and_button_container div {
    flex-grow: 1;
    max-width: 200px;
}


/*? =============================================================== */
/*? PAYMENT DETAIL ================================================= */
/*? =============================================================== */
.payment_detail {
    padding: 1rem 2rem;
    max-width: 800px;
    margin: 0 auto;
  }
  
  .payment_detail .section {
    background-color: #fff;
    border: 1px solid #ececec;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: -8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(*********** / 25%);
  }
  
  .payment_detail .header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
  }
  
  .payment_detail .header .left {
    display: flex;
    flex-direction: row;
    gap: 1rem;
    align-items: center;
  }
  
  .payment_detail .header .back_button {
    height: 40px;
    width: 40px;
    border-radius: 2px;
    background-color: #2d3436;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    cursor: pointer;
  }
  
  .payment_detail .header .back_button i {
    color: #fff;
    font-size: 1.4rem;
  }
  
  .payment_detail .header .title {
    margin: 0;
  }
  
  .payment_detail .header .title h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2f3640;
    margin: 0;
  }
  
  .payment_detail .header .title p {
    margin: 0;
    color: #808080;
    font-size: 0.9rem;
  }
  
  .payment_detail .header .status {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    text-align: center;
    min-width: 120px;
  }
  
  .payment_detail .header .status.Menunggu_Diproses {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
  }
  
  .payment_detail .header .status.Terbayar {
    background-color: rgba(40, 167, 69, 0.1);
    color: #28a745;
  }
  
  .payment_detail .header .status.Dibatalkan {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
  }
  
  .payment_detail .header .status.default {
    background-color: rgba(108, 117, 125, 0.1);
    color: #6c757d;
  }
  
  .payment_detail .info_grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }
  
  .payment_detail .info_item {
    display: flex;
    flex-direction: column;
  }
  
  .payment_detail .info_item label {
    font-size: 0.8rem;
    color: #808080;
    text-transform: uppercase;
    font-weight: 600;
  }
  
  .payment_detail .info_item .value {
    font-size: 1rem;
    font-weight: 600;
    color: #2f3640;
    margin-top: 0.25rem;
  }
  
  .payment_detail .customer_info {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 1rem;
  }
  
  .payment_detail .customer_info .avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
  }
  
  .payment_detail .customer_info .details {
    flex-grow: 1;
  }
  
  .payment_detail .customer_info .details .name {
    font-size: 1rem;
    font-weight: 600;
    color: #2f3640;
    margin-bottom: 0.25rem;
  }
  
  .payment_detail .customer_info .details .code {
    font-size: 0.9rem;
    color: #808080;
  }
  
  .payment_detail .customer_info .type {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    text-align: center;
    background-color: rgba(40, 167, 69, 0.1);
    color: #28a745;
  }
  
  .payment_detail .product_section {
    margin-bottom: 1rem;
  }
  
  .payment_detail .product_section .title {
    margin: 0 0 1rem 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: #2f3640;
  }
  
  .payment_detail .product_section .items {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }
  
  .payment_detail .product_section .item {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background-color: #fff;
    border: 1px solid #ececec;
    border-radius: 8px;
    box-shadow: -8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(*********** / 25%);
    transition: all 0.2s ease;
  }
  
  .payment_detail .product_section .item:hover {
    border-color: rgba(230, 126, 34, 0.2);
    box-shadow: -8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(*********** / 35%);
  }
  
  .payment_detail .product_section .item .product_image {
    flex-shrink: 0;
    position: relative;
  }
  
  .payment_detail .product_section .item .product_image .image {
    width: 70px;
    height: 70px;
    border-radius: 6px;
    object-fit: cover;
    border: 1px solid #f0f0f0;
  }
  
  .payment_detail .product_section .item .product_info {
    flex-grow: 1;
    min-width: 0;
  }
  
  .payment_detail .product_section .item .product_info .product_header {
    margin-bottom: 0.5rem;
  }
  
  .payment_detail .product_section .item .product_info .product_header .name {
    font-size: 1rem;
    font-weight: 600;
    color: #2f3640;
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .payment_detail .product_section .item .product_info .product_header .code {
    font-size: 0.8rem;
    color: #808080;
    font-weight: 500;
  }
  
  .payment_detail .product_section .item .product_info .product_details {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
  }
  
  .payment_detail .product_section .item .product_info .product_details .quantity_price {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .payment_detail .product_section .item .product_info .product_details .quantity_price .quantity {
    font-size: 0.9rem;
    color: #666;
    font-weight: 500;
  }
  
  .payment_detail .product_section .item .product_info .product_details .quantity_price .discount {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.8rem;
    /* green */
    color: #fff;
    font-weight: 500;
    background-color: #27ae60;
    padding: 0.15rem 0.5rem;
    border-radius: 4px;
  }
  
  .payment_detail .product_section .item .product_info .product_details .quantity_price .discount i {
    font-size: 0.9rem;
  }
  
  .payment_detail .product_section .item .product_info .product_details .quantity_price .total_price {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2f3640;
    text-align: right;
    white-space: nowrap;
  }
  
  .payment_detail .summary_section {
    margin-bottom: 1rem;
    background-color: #fff;
    border: 1px solid #ececec;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: -8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(*********** / 25%);
  }
  
  .payment_detail .summary_section .title {
    margin: 0 0 1rem 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: #2f3640;
  }
  
  .payment_detail .summary_section .grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }
  
  .payment_detail .summary_section .item {
    display: flex;
    flex-direction: column;
  }
  
  .payment_detail .summary_section .item label {
    font-size: 0.8rem;
    color: #808080;
    text-transform: uppercase;
    font-weight: 600;
  }
  
  .payment_detail .summary_section .item .value {
    font-size: 1rem;
    font-weight: 600;
    color: #2f3640;
    margin-top: 0.25rem;
  }
  
  .payment_detail .summary_section .item .value.total {
    font-size: 1.2rem;
    font-weight: 700;
  }
  
  .payment_detail .summary_section .item .value.discount {
    color: #e74c3c;
  }
  
  .payment_detail .summary_section .item .value.tax {
    color: #2ecc71;
  }
  
  .payment_detail .section {
    margin-bottom: 1rem;
    background-color: #fff;
    border: 1px solid #ececec;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: -8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(*********** / 25%);
  }
  
  .payment_detail .section .title {
    margin: 0 0 1rem 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: #2f3640;
  }
  
  .payment_detail .section .buttons {
    display: flex;
    gap: 1rem;
  }
  
  .payment_detail .section .buttons button {
    flex: 1;
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }
  
  .payment_detail .section .buttons button i {
    font-size: 1.2rem;
  }
  
  .payment_detail .section .buttons button.primary {
    background-color: var(--base-color);
    color: white;
  }
  
  .payment_detail .section .buttons button.primary:hover {
    background-color: #d4004d;
  }
  
  .payment_detail .section .buttons button.secondary {
    background-color: #f8f9fa;
    color: #2f3640;
  }
  
  .payment_detail .section .buttons button.secondary:hover {
    background-color: #e9ecef;
  }
  
  .payment_detail .section .buttons button.danger {
    background-color: #dc3545;
    color: white;
  }
  
  .payment_detail .section .buttons button.danger:hover {
    background-color: #c82333;
  }
  
.payment_methods { padding: 1.2rem 2rem; }
.payment_methods .items { display: grid; grid-template-columns: repeat(auto-fit, minmax(190px, 1fr)); gap: 1.2rem; justify-content: center; padding-top: .5rem; }
.payment_methods .items .item {
    background-color: #fff;
    box-shadow: -8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(*********** / 25%);
    border: solid 1px #ececec;
    border-radius: 2px;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    -ms-border-radius: 2px;
    -o-border-radius: 2px;
    overflow: hidden;
    cursor: pointer;
    padding: .9rem .6rem .6rem .6rem;
    display: flex;
    flex-direction: column;
    align-items: center;
}
.payment_methods .items .item img { width: 40px; height: 40px; object-fit: contain; background-color: #fcfcfc; }
.payment_methods .items .item .title { font-size: .8rem; color: var(--text-primary-color); margin-top: .5rem; text-align: center; font-weight: 600; font-size: 1.2rem; }
.payment_methods .items .item.active,
.payment_methods .items .item:hover { border-color: rgba(230, 126, 34, .5); background-color: rgba(230, 126, 34, .1); }
.payment_methods .items .item:hover .title,
.payment_methods .items .item.active .title { color: #e67e22; }

.section .summaries { 
    /* padding: 1rem 2rem;  */
    display: flex; flex-direction: column; position: sticky; bottom: 0; z-index: 1; background-color: #fcfcfc; }
.section .summaries .left { width: 57%; padding-right: 1rem; }
.section .summaries .left .item { width: 100%; display: flex; flex-direction: row; align-items: center; justify-content: space-between; flex-wrap: wrap; }
.section .summaries .left .item div { font-weight: 600; font-size: 1.1rem; }
.section .summaries .left .item:not(:first-child) { border-top: dotted 1px #9f9f9f; margin-top: .5rem; padding-top: .5rem; }
.section .summaries .right {
    width: 43%;
    padding-left: 1rem;
    align-items: flex-end;
    justify-content: flex-end;
    text-align: right;
    border-radius: 3px;
    background-color: #FFFFFF;
    border: 1px solid #ececec;
    box-shadow: -8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(*********** / 25%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
.section .summaries .right div { color: #525252; }

.section .payments .summaries { 
    /* padding: .5rem 2rem 6rem 2rem; */
 }
.section .payments .summaries .nominals {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    box-shadow: -8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(*********** / 25%);
    border: solid 1px #ececec;
    border-radius: 2px;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    -ms-border-radius: 2px;
    -o-border-radius: 2px;
}
.section .payments .summaries .nominals:has(input:not(:disabled):hover),
.section .payments .summaries .nominals:has(input:not(:disabled):focus) { border-color: rgba(230, 126, 34, .5); }
.section .payments .summaries .nominals:not(:first-child) { margin-top: 1rem; }
.section .payments .summaries .nominals label {
    font-size: .8rem;
    height: 50px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
    background-color: #ebebeb;
    font-weight: 600;
    padding: 0 .7rem;
    color: var(--text-foreign-color);
    width: 300px;
    text-transform: uppercase;
    text-align: right;
    font-size: 1.2rem;
}
.section .payments .summaries .nominals input {
    width: 100%;
    text-align: right;
    font-size: 1.2rem;
    font-weight: 600;
    height: 50px;
    padding: 0 .7rem;
    outline: none;
    border: none;
    color: var(--text-primary-color);
}
.section .payments .summaries .payment_input { display: flex; flex-direction: row; align-items: center; margin: 1rem 0; }
.section .payments .summaries .payment_input input {
    outline: none;
    border: 1px solid #ececec;
    background-color: #fff;
    box-shadow: -8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(*********** / 25%);
    height: 50px;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--base-color);
    flex: 1;
    padding: 0 1rem;
    text-align: right;
    border-radius: 2px 0px 0px 2px;
    -webkit-border-radius: 2px 0px 0px 2px;
    -moz-border-radius: 2px 0px 0px 2px;
    -ms-border-radius: 2px 0px 0px 2px;
    -o-border-radius: 2px 0px 0px 2px;
}
.section .payments .summaries .payment_input input:hover,
.section .payments .summaries .payment_input input:focus { border: solid 1px var(--base-color); }
.section .payments .summaries .payment_input:has(button:hover) input { border: solid 1px var(--base-color); }
.section .payments .summaries .payment_input button {
    height: 50px;
    border-radius: 0px 2px 2px 0px;
    -webkit-border-radius: 0px 2px 2px 0px;
    -moz-border-radius: 0px 2px 2px 0px;
    -ms-border-radius: 0px 2px 2px 0px;
    -o-border-radius: 0px 2px 2px 0px;
    margin-left: 0;
}
.section .payments .summaries .payment_nominal {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: .8rem;
    justify-content: center;
    padding-bottom: .5rem;
}
.section .payments .summaries .payment_nominal .item {
    background-color: #fff;
    box-shadow: -8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(*********** / 25%);
    border: solid 1px #ececec;
    border-radius: 2px;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    -ms-border-radius: 2px;
    -o-border-radius: 2px;
    overflow: hidden;
    cursor: pointer;
    padding: 1rem;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    font-size: .9rem;
    color: var(--text-foreign-color);
}
.section .payments .summaries .payment_nominal .item:hover { border-color: rgba(230, 126, 34, .5); color: #e67e22; font-weight: 600; }

.section .payments .payment_action {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-top: 1rem;
    background-color: #FFFFFF;
}
.section .payments .payment_action button {
    align-items: center;
    justify-content: center;
    border-radius: 2px;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    -ms-border-radius: 2px;
    -o-border-radius: 2px;
    height: 60px;
    width: auto;
    padding: 0 1.5rem;
}
.section .payments .payment_action button span { font-size: 1.2rem; }
.section .payments .payment_action button i { font-size: 1.5rem; margin-right: 1rem; }
.section .payments .payment_action button:first-child { flex-grow: 1; }
.section .payments .payment_action button:not(:first-child) i { margin-right: 0; }

/* Payment Info Midtrans Styles */
.payment_info_midtrans {
  background: #fff;
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 1rem;
  box-shadow: -8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(*********** / 25%);
}

.payment_info_midtrans .payment_status {
  margin-bottom: 1.5rem;
}

.payment_info_midtrans .status_label {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--base-color);
  margin-bottom: 0.5rem;
}

.payment_info_midtrans .expiry_info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #e74c3c;
  font-size: 0.9rem;
}

.payment_info_midtrans .va_info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.payment_info_midtrans .va_number label {
  font-size: 0.8rem;
  color: #808080;
  text-transform: uppercase;
  font-weight: 600;
  margin-bottom: 0.5rem;
  display: block;
}

.payment_info_midtrans .va_number .number {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 1.2rem;
  font-weight: 600;
  color: #2f3640;
}

.payment_info_midtrans .copy_button {
  background: none;
  border: none;
  color: var(--base-color);
  cursor: pointer;
  padding: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.payment_info_midtrans .copy_button:hover {
  transform: scale(1.1);
}

.payment_info_midtrans .instructions {
  margin-bottom: 1.5rem;
}

.payment_info_midtrans .instruction_group {
  margin-bottom: 1.5rem;
}

.payment_info_midtrans .instruction_group h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #2f3640;
  margin-bottom: 1rem;
}

.payment_info_midtrans .instruction_group ol {
  padding-left: 1.5rem;
  margin: 0;
}

.payment_info_midtrans .instruction_group li {
  color: #2f3640;
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.payment_info_midtrans .transaction_details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #ececec;
}

.payment_info_midtrans .detail_item label {
  font-size: 0.8rem;
  color: #808080;
  text-transform: uppercase;
  font-weight: 600;
  margin-bottom: 0.25rem;
  display: block;
}

.payment_info_midtrans .detail_item div {
  font-size: 0.9rem;
  color: #2f3640;
}

.searchbar_base {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 1rem;
}

.searchbar_base .input_search {
  flex-grow: 1;
  display: flex;
  align-items: center;
  border: 1px solid #ececec;
  background-color: #fff;
  box-shadow: -8px -8px 12px 0 #fcfcfc, 8px 8px 12px rgb(*********** / 25%);
  border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -ms-border-radius: 2px;
  -o-border-radius: 2px;
  overflow: hidden;
}

.searchbar_base .input_search label {
  flex-shrink: 0;
  height: 50px;
  width: 50px;
  background-color: #fff;
  color: #2d3436;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  font-weight: 500;
  padding-right: 5px;
}

.searchbar_base .input_search label i {
  font-size: 1.5rem;
}

.searchbar_base .input_search input {
  box-sizing: border-box;
  flex-grow: 1;
  min-width: 0;
  height: 50px;
  padding: 1em;
  font: inherit;
  transition: 150ms ease;
  -webkit-transition: 150ms ease;
  -moz-transition: 150ms ease;
  -ms-transition: 150ms ease;
  -o-transition: 150ms ease;
  background-color: #fff;
  border: none;
  outline: none;
  font-size: 1.1rem;
}

.searchbar_base .input_search:has(input:hover)>label,
.searchbar_base .input_search:has(input:focus)>label {
  color: #e67e22;
}

.searchbar_base .input_search:has(input:hover),
.searchbar_base .input_search:has(input:focus) {
  border-color: rgba(230, 126, 34, .5);
}