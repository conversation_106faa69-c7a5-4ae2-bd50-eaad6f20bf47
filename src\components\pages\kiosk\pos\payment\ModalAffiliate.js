import React from "react";
import { Modal, Box } from "@mui/material";
import Loading from "@/components/modal/Loading";
import ApiHelper from "@/utils/ApiHelper";
import CommonHelper from "@/utils/CommonHelper";
import Constants from "@/utils/Constants";
import Input from "@/components/libs/Input";
import MySnackbar from "@/components/MySnackbar";

const DEFAULT_INPUTS = {
  code: "",
  otp: "",
};
export default class ModalAffiliate extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      showDialog: false,
      formType: "",
      formData: null,
      inputs: structuredClone(DEFAULT_INPUTS),
      errors: {},
    };
  }
  onNotify = (message, severity = "info") => {
    this.ref_MySnackbar.onNotify(message, severity);
  };
  onLoading = (show, timout = 300) => {
    if (show) {
      this.ref_Loading.onShowDialog();
      return;
    } else {
      setTimeout(() => {
        this.ref_Loading.onCloseDialog();
      }, timout);
    }
  };
  onShowDialog = (
    formType,
    formData = null,
    formIndex = -1,
    formInputs = null
  ) => {
    let inputs = structuredClone(DEFAULT_INPUTS);
    if (formInputs) {
      inputs = formInputs;
    }
    this.setState(
      {
        formType,
        formData,
        formIndex,
        showDialog: true,

        inputs,
      },
      () => {
        setTimeout(() => {
          var inputNameID = document.getElementById("input-name");
          if (inputNameID) {
            inputNameID.focus();
          }
        }, 250);
      }
    );
  };
  onCloseDialog = () => {
    this.setState({
      showDialog: false,
      formType: "",
      formData: null,
      inputs: structuredClone(DEFAULT_INPUTS),
      errors: {},
    });
  };
  onTextInputListeners = (text, input) => {
    let inputs = { ...this.state.inputs };
    inputs[input] = text;
    this.setState({ inputs });
  };
  onTextErrorListeners = (text, input) => {
    let errors = { ...this.state.errors };
    errors[input] = text;
    this.setState({ errors });
  };
  onSendAffiliateOTP = async () => {
    let isValid = true;

    this.onTextErrorListeners(null, "code");
    if (!this.state.inputs.code) {
      isValid = false;
      this.onTextErrorListeners(
        "Kode harus diisi atau tidak boleh kosong.",
        "code"
      );
    }
    if (!isValid) {
      return;
    }

    this.onLoading(true);
    let apiEndPoint = "kiosk/kiosk/affiliate/otp/send";
    let params = {
      code: this.state.inputs.code,
    };
    let response = await ApiHelper.post(apiEndPoint, params);

    if (response.status === 200) {
      if (response?.results?.data?.otp) {
        this.onTextInputListeners(response?.results?.data?.otp, "otp");
      }
      this.onNotify(response?.message || "Berhasil Kirim OTP", "success");
    } else {
      this.onNotify(response?.message || "Terjadi Kesalahan", "error");
    }
    this.onLoading(false);
  };
  onVerifyAffiliate = async () => {
    let isValid = true;
    this.onTextErrorListeners(null, "otp");
    this.onTextErrorListeners(null, "code");
    if (!this.state.inputs.otp) {
      isValid = false;
      this.onTextErrorListeners(
        "OTP harus diisi atau tidak boleh kosong.",
        "otp"
      );
    }
    if (!this.state.inputs.code) {
      isValid = false;
      this.onTextErrorListeners(
        "Kode harus diisi atau tidak boleh kosong.",
        "code"
      );
    }
    if (!isValid) {
      return;
    }

    this.onLoading(true);
    let apiEndPoint = "kiosk/kiosk/affiliate/otp/verify";
    let params = {
      code: this.state.inputs.code,
      otp: this.state.inputs.otp,
    };
    let response = await ApiHelper.post(apiEndPoint, params);

    if (response.status === 200) {
      this.onNotify(response?.message || "Berhasil Verifikasi", "success");
      this.onCloseDialog();
      this.props.onResults(this.state.inputs.code, response.results.data.name);
    } else {
      this.onNotify(response?.message || "Terjadi Kesalahan", "error");
    }
    this.onLoading(false);
  };

  render() {
    let addModalClass = "small";
    return (
      <>
        <Modal open={this.state.showDialog} onClose={this.onCloseDialog}>
          <Box
            sx={{
              flex: 1,
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              display: "flex",
              width: "100wh",
              height: "100vh",
            }}
          >
            <div className={`modal-content ${addModalClass}`}>
              <div class="modal-header">{this.renderHeader()}</div>
              <div class="modal-body">{this.renderBody()}</div>
              <div class="modal-footer flex-wrap">
                {this.renderFooter()}
                <button
                  className="button cancel"
                  onClick={() => this.onCloseDialog()}
                >
                  <i class="ph ph-bold ph-x-circle"></i>
                  <span>
                    {this.state.formType === "info" ? "Tutup" : "Batal"}
                  </span>
                </button>
              </div>
            </div>
          </Box>
        </Modal>

        {/* Loading dialog */}
        <Loading ref={(value) => (this.ref_Loading = value)} />
        <MySnackbar ref={(value) => (this.ref_MySnackbar = value)} />
      </>
    );
  }

  renderHeader() {
    return (
      <>
        <div class="title">Cari Affiliate</div>
        <span class="close" onClick={() => this.onCloseDialog()}>
          &times;
        </span>
      </>
    );
  }

  renderBody() {
    return <>{this.renderForm()}</>;
  }

  renderFooter() {
    return (
      <>
        <button className="button" onClick={() => this.onVerifyAffiliate()}>
          <i class="ph ph-bold ph-check-circle"></i>
          <span>Verifikasi</span>
        </button>
      </>
    );
  }
  renderForm() {
    return (
      <>
        <div className="flex flex-col gap-4">
          <div className="row no-border">
            <div className="input-form">
              <div className="flex flex-row gap-4 items-center">
                <Input
                  label="Kode Affiliate"
                  inputType="text"
                  value={this.state.inputs.code}
                  onChange={(event) => {
                    if (event.target?.value !== undefined) {
                      this.onTextInputListeners(event.target.value, "code");
                    }
                  }}
                  required
                  maxLength={20}
                  error={
                    this.state.errors.code !== undefined &&
                    this.state.errors.code !== null
                      ? this.state.errors.code
                      : null
                  }
                />
                <button
                  className="button info"
                  style={{
                    marginLeft: "auto",
                    width: "130px",
                    marginTop: ".3rem",
                  }}
                  onClick={() => this.onSendAffiliateOTP()}
                >
                  <span>Kirim OTP</span>
                </button>
              </div>
            </div>
          </div>
          {this.state.inputs.code && (
            <div className="row no-border">
              <div className="input-form">
                <Input
                  label="Kode Verifikasi"
                  inputType="text"
                  value={this.state.inputs.otp}
                  onChange={(event) => {
                    if (event.target?.value !== undefined) {
                      this.onTextInputListeners(event.target.value, "otp");
                    }
                  }}
                  required
                  maxLength={9}
                  error={
                    this.state.errors.otp !== undefined &&
                    this.state.errors.otp !== null
                      ? this.state.errors.otp
                      : null
                  }
                />
              </div>
            </div>
          )}
        </div>
      </>
    );
  }
}
