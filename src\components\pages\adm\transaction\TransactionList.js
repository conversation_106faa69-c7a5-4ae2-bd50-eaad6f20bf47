import React from "react";
import stylesTable from "@/styles/Table.module.css";
import Table from "@/components/libs/Table";
import Loading from "@/components/modal/Loading";
import MySnackbar from "@/components/MySnackbar";
import ModalConfirmation from "@/components/modal/ModalConfirmation";
// import TransactionModalDetail from "@/components/pages/adm/transaction/TransactionModalDetail";
import ApiHelper from "@/utils/ApiHelper";
import Constants from "@/utils/Constants";
import Tooltip from "@mui/material/Tooltip";
import IconButton from "@mui/material/IconButton";
import Switch from "@mui/material/Switch";
import CommonHelper from "@/utils/CommonHelper";
import Router from "next/router";
import MomentHelper from "@/utils/MomentHelper";
import TransactionModalVerify from "@/components/pages/adm/transaction/TransactionModalVerify";

const ARR_HEADING = [
  {
    key: "",
    label: "",
    className: "text-center wd40 sticky_thead",
    sortable: false,
    sort: "",
  },
  {
    key: "input_datetime",
    label: "Tanggal Transaksi",
    className: "",
    sort: "desc",
    sortable: true,
  },
  {
    key: "number",
    label: "No. Transaksi",
    className: "",
    sort: "",
    sortable: true,
  },
  {
    key: "customer_name",
    label: "Nama Pelanggan",
    className: "",
    sort: "",
    sortable: true,
  },
  {
    key: "employee_name",
    label: "Tenaga Penjual",
    className: "",
    sort: "",
    sortable: true,
  },
  {
    key: "affiliate_name",
    label: "Affiliate",
    className: "",
    sort: "",
    sortable: true,
  },
  {
    key: "affiliate_code",
    label: "Kode Affiliate",
    className: "",
    sort: "",
    sortable: true,
  },
  {
    key: "total_product_item",
    label: "Total Item",
    className: "",
    sort: "",
    sortable: true,
  },
  {
    key: "grand_total",
    label: "Total",
    className: "",
    sort: "",
    sortable: true,
  },
];

export default class TransactionList extends React.Component {
  constructor(props) {
    super(props);
    this.ref_Table = React.createRef();
    this.state = {
      tableHeader: [],

      isFetched: true,
      fetchData: [],
      fetchDataShow: 0,
      fetchDataTotal: 0,
      fetchDataLimit: 10,
      fetchPageSelected: 1,
      fetchPageTotal: 0,
      inputSearch: "",
      inputSort: "",
      inputFilter: [],
      inputFilterObj: {},
      filterInput: {},
    };
  }

  componentDidMount() {
    // ...existing code...
  }

  onInit = () => {
    const tableHeader = structuredClone(ARR_HEADING);
    this.setState(
      {
        tableHeader,
        inputSearch: "",
        inputSort: "-input_datetime",
        inputFilter: [],
      },
      () => {
        this.onRefresh();
      }
    );
  };

  onRefresh = () => {
    this.setState(
      {
        isFetched: true,
        fetchData: [],
        fetchDataShow: 0,
        fetchDataTotal: 0,
        fetchDataLimit: 10,
        fetchPageSelected: 1,
        fetchPageTotal: 1,
      },
      this.onFetchData
    );
  };
  onFetchData = async (isPageClicked = false) => {
    if (isPageClicked) {
      this.setState({ isFetched: true });
    }

    let params = {
      limit: this.state.fetchDataLimit,
      page: this.state.fetchPageSelected,
      search: this.state.inputSearch,
      sort: this.state.inputSort,
      ...this.state.inputFilterObj,
      pagination_bool: true,
      status: this.props.selectedTabTitle,
    };

    let response = await ApiHelper.get("kiosk/admin/transaction/data", params);
    if (response.status === 200) {
      this.setState({
        isFetched: false,
        fetchData: response.results.data,
        fetchDataShow:
          this.state.fetchDataShow + response.results.pagination.total_display,
        fetchDataTotal: response.results.pagination.total_data,
      });
    } else {
      this.setState({
        isFetched: false,
        fetchData: [],
        fetchDataShow: 0,
        fetchDataTotal: 0,
      });
    }
  };
  onSearchListeners = (inputSearch) => {
    this.ref_Table.setSearchValue(inputSearch);
    this.setState({ inputSearch }, () => {
      this.onRefresh();
    });
  };
  onSortListeners = (inputSort) => {
    this.setState({ inputSort }, () => {
      this.onRefresh();
    });
  };
  onFilterListeners = () => {
    alert(`Filter Callback`);
  };
  onExportListeners = () => {
    alert(`Export Callback`);
  };

  onShowVerifyModal = (item, index) => {
    if (this.ref_TransactionModalVerify?.onShowDialog) {
      this.ref_TransactionModalVerify.onShowDialog("verify", item, index);
    }
  };

  render() {
    return (
      <>
        <Table
          ref={(value) => (this.ref_Table = value)}
          stylesContainer={{
            maxHeight: "calc(100% - 48px)",
          }}
          customTableContainers={stylesTable.withTab}
          disableTitle={true}
          title={this.props.selectedTabTitle}
          subtitle={"List data dan kelola data transaksi"}
          disabledAddBtn
          searchListeners={(inputSearch) => {
            this.ref_Table.setSearchValue(inputSearch);
            this.setState({ inputSearch }, () => {
              this.onRefresh();
            });
          }}
          sortListeners={(inputSort) => {
            this.setState({ inputSort }, () => {
              this.onRefresh();
            });
          }}
          dataHeadings={ARR_HEADING}
          dataTables={this.state.fetchData}
          renderItems={this.renderItems}
          pageCount={this.state.fetchPageTotal}
          pageSelected={this.state.fetchPageSelected}
          pageListeners={(fetchPageSelected) => {
            this.setState({ fetchPageSelected }, () => this.onFetchData(true));
          }}
          pageRow={this.state.fetchDataLimit}
          pageRowListeners={(fetchDataLimit) => {
            this.setState({ fetchDataLimit }, () => this.onFetchData(true));
          }}
          onReload={this.onRefresh}
          isFetched={this.state.isFetched}
        />

        <TransactionModalVerify
          ref={(value) => (this.ref_TransactionModalVerify = value)}
          onResults={() => {
            this.onRefresh();
          }}
        />
      </>
    );
  }

  renderItems = (item, index) => {
    return (
      <tr key={index}>
        <td>
          <div className="actions">
            <Tooltip title="Lihat Detail">
              <IconButton onClick={() => {}}>
                <i className="ph ph-bold ph-info text-blue"></i>
              </IconButton>
            </Tooltip>
            {
              (item.status = "Menunggu Diproses" && (
                <Tooltip title="Proses Transaksi">
                  <IconButton
                    onClick={() => {
                      this.onShowVerifyModal(item, index);
                    }}
                  >
                    <i className="ph ph-bold ph-check-circle text-green"></i>
                  </IconButton>
                </Tooltip>
              ))
            }
          </div>
        </td>
        <td>
          {MomentHelper.format(
            item.input_datetime,
            MomentHelper.datetimeFormatReadable2
          )}
        </td>
        <td>{item.number}</td>
        <td>{item.customer_name}</td>
        <td>{item.employee_name}</td>
        <td>{item.affiliate_name}</td>
        <td>{item.affiliate_code}</td>
        <td className="text-right">{item.total_product_item}</td>
        <td className="text-right">
          {CommonHelper.formatNumber(item.grand_total, "idr")}
        </td>
      </tr>
    );
  };
}
