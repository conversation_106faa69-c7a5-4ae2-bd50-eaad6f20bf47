/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  output: 'export',
  distDir: 'dist',
  trailingSlash: false,
  images: {
    unoptimized: true,
    remotePatterns: [
      {
        protocol: "https",
        hostname: "sodapos.com",
        port: "",
        pathname: "/media/**",
      },
      {
        protocol: "https",
        hostname: "els.id",
        port: "",
        pathname: "/wp-content/uploads/**",
      },
      {
        protocol: "https",
        hostname: "storage.googleapis.com",
        port: "",
        pathname: "/go-merchant-production.appspot.com/**",
      },
    ],
  },
};

export default nextConfig;
