import { create } from "zustand";
import { createTrackedSelector } from "react-tracked";
import { persist, createJSONStorage } from "zustand/middleware";
import CommonHelper from "@/utils/CommonHelper";
import Constants from "@/utils/Constants";
import { useKioskPosStore } from "./store";
import { usePaymentStore } from "./payment/store";
import { numberFormatIdToNumber } from "@/components/libs/Input";

const kioskPosCartStoreLogic = (set, get) => ({
  // State properties
  isInPayment: false,

  // State persist properties
  arrCart: [],
  refCartProductIds: {},
  totalProductSellingPrice: 0,

  objCustomer: null,

  // Actions
  setState: (newValue, cb = null, keys = []) => {
    set((prevState) => {
      if (keys.length > 0) {
        return CommonHelper.updateNestedState(prevState, keys, newValue);
      }
      // Shallow update if no keys provided
      return {
        ...prevState,
        ...newValue,
      };
    });

    if (cb) {
      setTimeout(() => {
        cb();
      }, 0);
    }
  },

  onRemoveCustomer: () => {
    set({ objCustomer: null });
  },
  onAddToCart: (product) => {
    const currentArrCart = get().arrCart;
    let totalProductSellingPrice = get().totalProductSellingPrice;
    let refCartProductIds = get().refCartProductIds;
    let found = false;

    // It's better to map to a new array for immutability
    const newArrCart = currentArrCart.map((item) => {
      if (item.id === product.id) {
        found = true;
        let qty = item.qty + 1;
        let totalDiscount = item.discount * qty;
        let totalSellingPrice = item.selling_price * qty;
        let subTotal = totalSellingPrice - totalDiscount;
        totalProductSellingPrice -= item.sub_total;
        totalProductSellingPrice += subTotal;
        return {
          ...item,
          qty: item.qty + 1,
          total_discount: totalDiscount,
          total_selling_price: totalSellingPrice,
          sub_total: subTotal,
        };
      }
      return item;
    });

    if (!found) {
      let selling_price = Number(product.selling_price);
      totalProductSellingPrice += selling_price;
      refCartProductIds[product.id] = true;
      newArrCart.push({
        ...product,
        selling_price,
        qty: 1,
        notes: "",
        discount: 0,
        discount_label: "0",
        total_discount: 0,
        total_selling_price: selling_price,
        sub_total: selling_price,
        stock_balance: Number(product.stock_balance),
      });
    }

    set({ arrCart: newArrCart, totalProductSellingPrice, refCartProductIds }); // Set the new array
  },

  // You might want other actions like removeFromCart, updateQuantity, clearCart
  onClearCart: () => {
    set({ arrCart: [], totalProductSellingPrice: 0, refCartProductIds: {} });
    useKioskPosStore.getState().onReset();
  },
  onRemoveCartItem: (index) => {
    set((state) => {
      let product = state.arrCart[index];
      let refCartProductIds = state.refCartProductIds;
      delete refCartProductIds[product.id];

      return {
        refCartProductIds,
        arrCart: state.arrCart.filter((item, i) => i !== index),
      };
    });
    get().onCalculateCart();
  },
  onUpdateCartItemQty: (index, newQty) => {
    set((state) => {
      let item = state.arrCart[index];
      if (item.stock_balance <= 0) {
        return {};
      }

      return {
        arrCart: state.arrCart.map((item, i) => {
          if (i === index) {
            item.qty = Math.max(0, newQty);
            item.total_selling_price = Number(item.selling_price) * item.qty;
            item.total_discount = item.discount * item.qty;
            item.sub_total = item.total_selling_price - item.total_discount;
          }
          return item;
        }),
      };
    });
    get().onCalculateCart();
  },
  onUpdateCartItemNote: (index, note) => {
    set((state) => {
      return {
        arrCart: state.arrCart.map((item, i) => {
          if (i === index) {
            item.notes = note;
          }
          return item;
        }),
      };
    });
    // get().onCalculateCart();
  },
  onUpdateCartItemDiscount: (index, discount) => {
    set((state) => {
      let item = state.arrCart[index];
      if (item.stock_balance <= 0) {
        return {};
      }

      let value = Number(numberFormatIdToNumber(discount));
      let value_label = discount;
      if (value < 0) {
        value = 0;
        value_label = "0";
      }
      // lower then selling_price
      if (value > state.arrCart[index].selling_price) {
        value = state.arrCart[index].selling_price;
        value_label = CommonHelper.formatNumber(value);
      }

      return {
        arrCart: state.arrCart.map((item, i) => {
          if (i === index) {
            item.discount = value;
            item.discount_label = value_label;
            item.total_discount = item.discount * item.qty;
            item.sub_total = item.total_selling_price - item.total_discount;
          }
          return item;
        }),
      };
    });
    get().onCalculateCart();
  },
  onCalculateCart: (isCalculateItem = false) => {
    let totalProductSellingPrice = 0;
    let arrCart = get().arrCart;
    arrCart.map((item) => {
      if (isCalculateItem) {
        item.total_selling_price = Number(item.selling_price) * item.qty;
        item.total_discount = item.discount * item.qty;
        item.sub_total = item.total_selling_price - item.total_discount;
      }

      if (item.stock_balance > 0) {
        totalProductSellingPrice += item.sub_total;
      }
      return item;
    });
    set({ totalProductSellingPrice });

    if (get().isInPayment) {
      usePaymentStore.getState().onCalculateTransaction();
    }
  },
});

// Create the persistent store
export const usePersistKioskPosStore = create(
  persist(
    kioskPosCartStoreLogic, // Your store logic
    {
      name: Constants.appName + "_KIOSK_POS", // Unique name for localStorage key
      storage: createJSONStorage(() => localStorage), // Or sessionStorage, AsyncStorage for RN
      partialize: (state) => ({
        arrCart: state.arrCart,
        refCartProductIds: state.refCartProductIds,
        totalProductSellingPrice: state.totalProductSellingPrice,
        objCustomer: state.objCustomer,
      }),
      // Optional: onRehydrateStorage can be useful for logging or migrations
      onRehydrateStorage: () => (state, error) => {
        if (error) {
          console.log(
            "Zustand persist: an error occurred during hydration",
            error
          );
        } else {
          console.log("Zustand persist: hydration finished", state);
        }
      },
    }
  )
);

// Your tracked selector hook remains the same
const useTrackedPersistKioskPosStore = createTrackedSelector(
  usePersistKioskPosStore
);

export default useTrackedPersistKioskPosStore;
