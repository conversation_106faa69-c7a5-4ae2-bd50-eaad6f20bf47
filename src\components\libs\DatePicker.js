/*
Created by esoda
Created on Apr, 2025
Contact esoda.id
*/

import moment from 'moment';
import React, { useEffect } from 'react';

const DatePicker = ({
    formClass,
    label,
    error,
    icon,
    minLength,
    maxLength,
    value,
    minDate,
    onFocus = () => { },
    onChange = () => { },
    optional,
    required,
    readonly,
    readonlyMessage,
    disablePlaceholder,
    acceptEqual,
    ...props
}) => {
    const [showDate, setShowDate] = React.useState(false);
    const [year, setYear] = React.useState(moment().format("YYYY"));
    const [month, setMonth] = React.useState(moment().format("MM"));
    const [dateArr, setDateArr] = React.useState([]);
    const weekdays = ['Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab', 'Min'];
    const ref = React.useRef(null)

    useEffect(() => {
        generateDate()
    }, [minDate]);

    useEffect(() => {
        function handleClickOutside(event) {
            if (ref.current && !ref.current.contains(event.target)) {
                setShowDate(false)
            }
        }
        // Bind the event listener
        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            // Unbind the event listener on clean up
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [ref]);

    const generateDate = (pMonth = "", pYear = "") => {
        let arrDate = []
        let tmpYear = pYear || moment().format('YYYY')
        let tmpMonth = pMonth || moment().format('MM')

        if (pMonth === "" && pYear === "") {
            if (value) {
                tmpYear = moment(value).format('YYYY')
                tmpMonth = moment(value).format('MM')
            } else {
                if (minDate) {
                    tmpYear = moment(minDate).format('YYYY')
                    tmpMonth = moment(minDate).format('MM')
                }
            }
        }

        let tmpDate = `${tmpYear}-${tmpMonth}-01`

        let firstDay = Number(moment(tmpDate).format('d'))
        if (firstDay === 0) {
            firstDay = 7
        }

        let endDate = new Date(tmpYear, tmpMonth, 0).getDate()
        let startDate = 1
        let itemCount = 35
        if (firstDay >= 6 && endDate >= 30) {
            itemCount = 42
        }
        for (let i = 1; i <= itemCount; i++) {
            if (i >= firstDay) {
                if (startDate <= endDate) {
                    arrDate.push(`${startDate}`)
                    startDate++
                } else {
                    arrDate.push('')
                }
            } else {
                arrDate.push('')
            }
        }

        setDateArr(arrDate)
        setMonth(tmpMonth)
        setYear(tmpYear)
    }
    const onMonthPrev = () => {
        let dateModifier = moment(`${year}-${month}-01`).subtract(1, 'M').format('YYYY-MM-DD')
        let tmpMonth = moment(dateModifier).format('MM')
        let tmpYear = moment(dateModifier).format('YYYY')
        generateDate(tmpMonth, tmpYear)
    }
    const onMonthNext = () => {
        let dateModifier = moment(`${year}-${month}-01`).add(1, 'M').format('YYYY-MM-DD')
        let tmpMonth = moment(dateModifier).format('MM')
        let tmpYear = moment(dateModifier).format('YYYY')
        generateDate(tmpMonth, tmpYear)
    }
    const onShowDatePicker = () => {
        if (!readonly) {
            let isShow = !showDate
            setShowDate(isShow)
            if (isShow) {
                generateDate()
            }
        }
    }
    const onDateSelected = (value) => {
        if (Number(value) < 10) { value = `0${value}` }
        onChange(`${year}-${month}-${value}`)
    }

    // ====================================================================================
    // ========== RENDER SECTIONS =========================================================
    // ====================================================================================
    return (
        <div ref={ref} className={"date-picker-libs"}>
            <div className={`form-input ${formClass}`}>
                <label>{label}</label>
                <div className={`inputs ${error && "error"}`}
                    onClick={onShowDatePicker}
                    >
                    <div className={"icon"} style={{ cursor: !readonly ? "pointer" : "default" }}>
                        <i className={`ph ${icon}`}></i>
                    </div>
                    <input
                        onFocus={onFocus}
                        value={value !== "" ? moment(value).format("D MMMM YYYY") : ""}
                        {...props}
                        readOnly
                        onClick={onShowDatePicker}
                    />
                    <div className={"symbol"} style={{ cursor: !readonly ? "pointer" : "default" }}
                        onClick={onShowDatePicker}
                        >
                        <i className="ph ph-bold ph-caret-down"></i>
                    </div>
                </div>
                <div className={`date-picker ${showDate && "show"}`}>
                    <div className="month">
                        <div className={`selector active`} onClick={onMonthPrev}>
                            <i class="ph ph-caret-left"></i>
                        </div>
                        <div className="value">{moment(month).format("MMMM")} {year}</div>
                        <div className={`selector active`} onClick={onMonthNext}>
                            <i class="ph ph-caret-right"></i>
                        </div>
                    </div>
                    <div className="weekdays">
                        {weekdays.map((weekday) => <div className="items">{weekday}</div>)}
                    </div>
                    <div className="date">
                        {dateArr.map((item, index) => {
                            let selectedClass = ""
                            let isToday = false
                            let isLessThanEqualMinDate = false;
                            if(value) {
                                if (moment(value).format("YYYY-MM") === `${year}-${month}`) {
                                    if (moment(value).format("D") === item) {
                                        selectedClass = " selected"
                                    }
                                }
                            }
                            if (moment().format("YYYY-MM") === `${year}-${month}`) {
                                if (moment().format("D") === item) {
                                    isToday = true
                                }
                            }
                            if(minDate) {
                                const date1 = moment(`${year}-${month}-${item.toString().padStart(2, '0')}`);
                                const date2 = moment(minDate);

                                isLessThanEqualMinDate = !date1.isAfter(date2);
                                if (acceptEqual) {
                                    isLessThanEqualMinDate = !date1.isSameOrAfter(date2);
                                }
                            }
                            return (
                                <div key={index} className={`items${selectedClass} ${!item && "empty"}`}
                                    style={{ 
                                        cursor: item && !isLessThanEqualMinDate ? "pointer" : "default", 
                                        opacity: isLessThanEqualMinDate ? 0.3 : 1,
                                    }}
                                    onClick={() => {
                                        if (item !== "" && !isLessThanEqualMinDate) {
                                            onDateSelected(item)
                                            setShowDate(!showDate)
                                        }
                                    }}
                                    >
                                    {item}
                                    {isToday && !selectedClass && <div className="today" />}
                                </div>
                            )
                        })}
                    </div>
                    <div className="actions">
                        <div className="clear" onClick={() => { onChange(""); onShowDatePicker() }}>HAPUS</div>
                        <div className="today" onClick={() => { onChange(moment().format("YYYY-MM-DD")); onShowDatePicker() }}>HARI INI</div>
                    </div>
                </div>
                {!disablePlaceholder && <div className="placeholder">
                    {required && !readonly && <span className="required">Wajib</span>}
                    {optional && !readonly && <span className="optional">Opsional</span>}
                    {readonly && <span className="read-only">tidak dapat diubah</span>}
                    {!readonly && <span className="note">{!error ? <>Pilih tanggal</> : error}</span>}
                    {readonlyMessage && <span className="note">{readonlyMessage}</span>}
                </div>}
            </div>
        </div>
    )
}

export default DatePicker