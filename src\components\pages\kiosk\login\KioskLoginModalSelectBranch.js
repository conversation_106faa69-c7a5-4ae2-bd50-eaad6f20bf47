import React from "react";
import { Modal, Box, Checkbox, Chip, Radio } from "@mui/material";
import Loading from "@/components/modal/Loading";
import Table from "@/components/libs/Table";
import ApiHelper from "@/utils/ApiHelper";
import useTrackedLoginStore from "@/store/kiosk/login/store";
import useTrackedSelectBranchStore from "@/store/kiosk/login/modal/storeSelectBranch";
import { getUntrackedObject } from "react-tracked";

export default function KioskLoginModalSelectBranch() {
  const { showDialog, onCloseDialog } = useTrackedSelectBranchStore();

  let addModalClass = "small";

  return (
    <>
      <Modal open={showDialog} onClose={onCloseDialog}>
        <Box
          sx={{
            flex: 1,
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            display: "flex",
            width: "100wh",
            height: "100vh",
          }}
        >
          <div className={`modal-content ${addModalClass}`}>
            <div class="modal-header">
              <RenderHeader />
            </div>
            <div class="modal-body">
              <RenderBody />
            </div>
            <div class="modal-footer flex-wrap">
              <RenderFooter />
            </div>
          </div>
        </Box>
      </Modal>
    </>
  );
}

function RenderHeader() {
  const { formType } = useTrackedSelectBranchStore();

  return (
    <>
      <div class="title" style={{ marginBottom: "5px", marginTop: ".7rem" }}>
        Pilih Cabang
      </div>
      {/* <span class="close" onClick={() => onCloseDialog()}>
        &times;
      </span> */}
    </>
  );
}

function RenderBody() {
  const { formData, selected, onSelect } = useTrackedSelectBranchStore();

  if (!formData?.branches) {
    return <></>;
  }

  return (
    <div className="grid grid-cols-1 gap-4">
      {formData.branches.map((item, index) => {
        return (
          <div
            key={index}
            className="detail_container flex flex-row items-center mt-0 justify-between"
            style={{ cursor: "pointer" }}
            onClick={() => onSelect(getUntrackedObject(item))}
          >
            <div>{item.name}</div>
            <Radio
              checked={selected?.id === item.id}
              value={item.id}
              name="radio-buttons"
            />
          </div>
        );
      })}
    </div>
  );
}

function RenderFooter() {
  const { onSubmit } = useTrackedSelectBranchStore();

  return (
    <>
      <button className="button" onClick={() => onSubmit()}>
        <i className="ph ph-bold ph-check-circle"></i>
        <span>Pilih</span>
      </button>
    </>
  );
}
