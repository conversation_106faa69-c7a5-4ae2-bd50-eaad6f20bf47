import React from "react";
import styles from "@/styles/Pos.module.css";
import stylesTable from "@/styles/Table.module.css";

import Loading from "@/components/modal/Loading";
import PosComponentTopbar from "@/components/pages/adm/_topbar";
import Router from "next/router";

import Constants from "@/utils/Constants";
import AuthWrapperAdmin from "@/components/wrapper/AuthWrapperAdmin";
import AdminSideBar from "@/components/pages/adm/AdminSideBar";

import CommonHelper from "@/utils/CommonHelper";
import ModalConfirmation from "@/components/modal/ModalConfirmation";
import MySnackbar from "@/components/MySnackbar";
import TransactionList from "@/components/pages/adm/transaction/TransactionList";

const ARR_TAB = [
  {
    label: "Menunggu Diproses",
    value: "Menunggu Diproses",
  },
  {
    label: "Menunggu Pembayaran",
    value: "Menunggu pembayaran",
  },
  {
    label: "Terbayar",
    value: "Terbayar",
  },
];

class AdminTransaction extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      selectedTabIdx: 0,
      selectedTabTitle: ARR_TAB[0].label,
      selectedTabValue: ARR_TAB[0].value,
    };
  }

  // ====================================================================================
  // ========== LIFECYCLE METHODS =======================================================
  // ====================================================================================
  componentDidUpdate(prevProps, prevState) {
    if (prevProps.isAuthLoading !== this.props.isAuthLoading) {
      if (!this.props.isAuthLoading && this.props.isLoggedIn) {
        this.onInit();
      }
    }
  }

  // ====================================================================================
  // ========== INITIALIZE, GET DATA ====================================================
  // ====================================================================================
  onInit = () => {
    this.ref_TransactionList.onInit();
  };
  onSelectedTabListener = (index) => {
    this.setState(
      {
        selectedTabIdx: index,
        selectedTabTitle: ARR_TAB[index].label,
        selectedTabValue: ARR_TAB[index].value,
      },
      this.onInit
    );
  };

  // ====================================================================================
  // ========== RENDER SECTIONS =========================================================
  // ====================================================================================
  render() {
    const { isAuthLoading, user, menu } = this.props;
    return (
      <div
        className={`${styles.ct_containers}`}
        style={{ backgroundColor: "rgb(252, 252, 252)" }}
      >
        <PosComponentTopbar
          hideButton
          title={"Transaksi"}
          subtitle={"Atur data transaksi"}
          user={user}
          isAuthLoading={isAuthLoading}
        />
        <div className={`${styles.contents} ${styles.sidebar}`}>
          <AdminSideBar isAuthLoading={isAuthLoading} menu={menu} />
          {this.renderContent()}
        </div>
      </div>
    );
  }

  renderContent() {
    return (
      <div className={styles.ctx}>
        {/* tab */}
        <div className="tab-bar">
          {ARR_TAB.map((item, index) => (
            <div
              key={index}
              className={`item ${
                this.state.selectedTabIdx === index && "selected"
              }`}
              onClick={() => {
                this.onSelectedTabListener(index);
              }}
            >
              {item.label}
            </div>
          ))}
        </div>
        <TransactionList
          ref={(value) => (this.ref_TransactionList = value)}
          selectedTabTitle={this.state.selectedTabTitle}
          isAuthLoading={this.props.isAuthLoading}
        />
      </div>
    );
  }
}

export default AuthWrapperAdmin(AdminTransaction, {
  redirectTo: Constants.webUrl.adm.login,
});
