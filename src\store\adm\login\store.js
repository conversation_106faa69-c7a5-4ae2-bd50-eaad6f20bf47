import { create } from "zustand";
import { createTrackedSelector } from "react-tracked";
import Router from "next/router";
import CommonHelper from "@/utils/CommonHelper";
import ApiHelper from "@/utils/ApiHelper";
import Constants from "@/utils/Constants";

const page = (set, get) => ({
  setState: (newValue, cb = null, keys = []) => {
    set((prevState) => {
      if (keys.length > 0) {
        return CommonHelper.updateNestedState(prevState, keys, newValue);
      }

      // Shallow update if no keys provided
      return {
        ...prevState,
        ...newValue,
      };
    });

    if (cb) {
      setTimeout(() => {
        cb();
      }, 0);
    }
  },

  inputs: { username: "superuser", password: "1234567890" },
  errors: {},
  showPassword: false,
  ref_Loading: null,
  ref_MySnackbar: null,
  onLoading: (show) => {
    if (get().ref_Loading) {
      if (show) {
        get().ref_Loading.onShowDialog();
      } else {
        get().ref_Loading.onCloseDialog();
      }
    }
  },
  onNotify: (message, severity) => {
    if (get().ref_MySnackbar.current?.onNotify) {
      get().ref_MySnackbar.current?.onNotify(message, severity);
    }
  },
  setLoadingRef: (ref) => set({ ref_Loading: ref }),
  setInputs: (inputs) => set({ inputs }),
  setErrors: (errors) => set({ errors }),
  onTextInputListeners: (text, input) => {
    set((state) => ({ inputs: { ...state.inputs, [input]: text } }));
  },
  onTextErrorListeners: (error, input) => {
    set((state) => ({ errors: { ...state.errors, [input]: error } }));
  },
  toggleShowPassword: () =>
    set((state) => ({ showPassword: !state.showPassword })),
  onValidateInputs: () => {
    let isValid = true;
    if (!get().inputs.username) {
      get().onTextErrorListeners(
        "Username harus diisi atau tidak boleh kosong.",
        "username"
      );
      isValid = false;
    }
    if (!get().inputs.password) {
      get().onTextErrorListeners(
        "Password harus diisi atau tidak boleh kosong.",
        "password"
      );
      isValid = false;
    }

    if (isValid) {
      get().onLoginListeners();
    }
  },
  onLoginListeners: async () => {
    get().onLoading(true);

    let params = {
      username: get().inputs.username,
      password: get().inputs.password,
      device: "web",
    };

    let response = await ApiHelper.post(`kiosk/admin/auth/login/basic`, params);

    if (response.status === 200) {
      get().onNotify(response?.message || "Berhasil Login", "success");
      get().onLoading(false);

      Router.replace({ pathname: Constants.webUrl.adm.home });
    } else {
      get().onLoading(false);
      get().onNotify(response?.message || "Terjadi Kesalahan", "error");
    }
  },
  // END: PAGE CONTENT
});

export const useLoginStore = create((...a) => ({
  ...page(...a),
}));

const useTrackedLoginStore = createTrackedSelector(useLoginStore);

export default useTrackedLoginStore;
