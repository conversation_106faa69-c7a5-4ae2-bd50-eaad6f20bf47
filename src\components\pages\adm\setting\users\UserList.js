// import
import React from "react";
import stylesTable from "@/styles/Table.module.css";
import Table from "@/components/libs/Table";
import Loading from "@/components/modal/Loading";
import MySnackbar from "@/components/MySnackbar";
import ModalConfirmation from "@/components/modal/ModalConfirmation";
import UsersModalAddUpdate from "@/components/pages/adm/setting/users/UsersModalAddUpdate";
import ApiHelper from "@/utils/ApiHelper";
import Constants from "@/utils/Constants";
import Tooltip from "@mui/material/Tooltip";
import IconButton from "@mui/material/IconButton";
import Switch from "@mui/material/Switch";
import UserModalListFilter from "@/components/pages/adm/setting/users/UserModalListFilter";
import FilterHelper from "@/utils/FilterHelper";
import { FILTER_COMP_EQUAL, FILTER_TYPE_STRING } from "@/utils/const/FILTER";

const ARR_HEADING = [
  {
    key: "",
    label: "",
    className: "text-center sticky_thead",
    sort: "",
    sortable: false,
  },
  {
    key: "name",
    label: "Nama Pengguna",
    className: "",
    sort: "asc",
    sortable: true,
  },
  {
    key: "username",
    label: "Username",
    className: "",
    sort: "",
    sortable: true,
  },
  {
    key: "email",
    label: "Email",
    className: "",
    sort: "",
    sortable: true,
  },
  {
    key: "mobilephone",
    label: "No. HP",
    className: "",
    sort: "",
    sortable: true,
  },
  {
    key: "role_name",
    label: "Hak Akses",
    className: "",
    sort: "",
    sortable: true,
  },
  {
    key: "type",
    label: "Tipe Pengguna",
    className: "text-center",
    sort: "",
    sortable: true,
  },
];

export default class ListUser extends React.Component {
  constructor(props) {
    super(props);
    this.ref_Table = React.createRef();
    this.state = {
      isFetched: true,
      fetchData: [],
      fetchDataShow: 0,
      fetchDataTotal: 0,
      fetchDataLimit: 10,
      fetchPageSelected: 1,
      fetchPageTotal: 0,
      inputSearch: "",
      inputSort: "name",
      inputFilter: [],
      inputFilterObj: {},
      filterInput: {},
    };
  }

  componentDidMount() {
    // ...existing code...
  }

  onInit = () => {
    this.setState(
      {
        inputSearch: "",
        inputSort: "",
        inputFilter: [],
      },
      () => {
        this.onRefresh();
      }
    );
  };

  onRefresh = () => {
    this.setState(
      {
        isFetched: true,
        fetchData: [],
        fetchDataShow: 0,
        fetchDataTotal: 0,
        fetchDataLimit: 10,
        fetchPageSelected: 1,
        fetchPageTotal: 1,
      },
      this.onFetchData
    );
  };

  onFetchData = async (isPageClicked = false) => {
    if (isPageClicked) {
      this.setState({ isFetched: true });
    }
    let response = await ApiHelper.get("kiosk/admin/user/data", {
      limit: this.state.fetchDataLimit,
      page: this.state.fetchPageSelected,
      search: this.state.inputSearch,
      sort: this.state.inputSort,
      ...this.state.inputFilterObj,
      pagination_bool: true,
    });
    if (response.status === 200) {
      this.setState({
        isFetched: false,
        fetchData: response.results.data,
        fetchDataShow:
          this.state.fetchDataShow + response.results.pagination.total_display,
        fetchDataTotal: response.results.pagination.total_data,
      });
    } else {
      this.setState({
        isFetched: false,
        fetchData: [],
        fetchDataShow: 0,
        fetchDataTotal: 0,
      });
    }
  };

  onSubmitActivate = async (item, index) => {
    this.ref_Loading.onShowDialog();
    const response = await ApiHelper.post("kiosk/admin/user/activate", {
      id: item.id,
      active_bool: !item.active_bool,
    });
    if (response.status === 200) {
      this.onRefresh();
      this.ref_ModalConfirmation.onCloseDialog();
    } else {
      this.ref_MySnackbar.onNotify(
        response?.message || "Terjadi Kesalahan",
        "error"
      );
    }
    this.ref_Loading.onCloseDialog();
  };

  onSubmitDelete = async (item, index) => {
    this.ref_Loading.onShowDialog();
    const response = await ApiHelper.post("kiosk/admin/user/delete", {
      id: item.id,
    });
    if (response.status === 200) {
      this.onRefresh();
      this.ref_ModalConfirmation.onCloseDialog();
    } else {
      this.ref_MySnackbar.onNotify(
        response?.message || "Terjadi Kesalahan",
        "error"
      );
    }
    this.ref_Loading.onCloseDialog();
  };

  // ====================================================================================
  // ========== FILTER ==================================================================
  // ====================================================================================
  onFilterListeners = () => {
    this.ref_UserModalListFilter.onShowDialog(
      "filter",
      {
        inputs: this.state.filterInput,
        dataParams: {
          aol_id: this.props.selected_aol.aol_id,
          aol_session_database: this.props.selected_aol.database,
        },
      },
      -1
    );
  };

  onRemoveFilterListeners = (index) => {
    let filterInput = structuredClone(this.state.filterInput);
    let inputFilterObj = structuredClone(this.state.inputFilterObj);
    let inputFilter = structuredClone(this.state.inputFilter);

    inputFilter.forEach((filter, filterIdx) => {
      if (filterIdx === index) {
        for (const key in filter.removeFieldObj) {
          const element = filter.removeFieldObj[key];
          delete inputFilterObj[element];
          delete filterInput[element];
        }
        inputFilter.splice(filterIdx, 1);
      }
    });

    this.setState({ inputFilter, inputFilterObj, filterInput }, this.onRefresh);
  };

  onFilterChangeListeners = (filterInput) => {
    // refFilter
    const refFilter = {
      role: {
        field: (filter, key) => {
          return "role_id";
        },
        fieldObj: (filter, key) => {
          if (!filter?.id) {
            return {};
          }
          return {
            [`role_id`]: filter.id,
          };
        },
        removeFieldObj: [`role`, `role_id`],
        value: (filter, key) => {
          if (!filter?.id) {
            return "";
          }
          return `${filter.id}`;
        },
        title: (filter, key) => {
          if (!filter?.name) {
            return ``;
          }
          return `Hak Akses: ${filter.name}`;
        },
        type: FILTER_TYPE_STRING,
        comparison: FILTER_COMP_EQUAL,
      },
      type: {
        field: (filter, key) => {
          return "type";
        },
        fieldObj: (filter, key) => {
          if (!filter) {
            return {};
          }
          return {
            [`type`]: filter,
          };
        },
        removeFieldObj: [`type`],
        value: (filter, key) => {
          if (!filter) {
            return "";
          }
          return `${filter}`;
        },
        title: (filter, key) => {
          return `Tipe Pengguna: ${filter}`;
        },
        type: FILTER_TYPE_STRING,
        comparison: FILTER_COMP_EQUAL,
      },
    };
    const filterHelper = new FilterHelper();
    const { inputFilter, inputFilterObj } = filterHelper.generateFilter(
      filterInput,
      refFilter
    );

    this.setState({ inputFilter, inputFilterObj, filterInput }, this.onRefresh);
  };

  // ====================================================================================
  // ========== FILTER ==================================================================
  // ====================================================================================

  render() {
    return (
      <>
        <Table
          ref={(value) => (this.ref_Table = value)}
          customTableContainers={stylesTable.withTab}
          stylesContainer={{
            maxHeight: "calc(100% - 48px)",
          }}
          disableTitle={true}
          title={this.props.selectedTabTitle}
          subtitle={"List data dan kelola data tipe penjualan"}
          addTitle={"Data Baru"}
          addListeners={() => {
            if (this.ref_UsersModalAddUpdate?.onShowDialog) {
              this.ref_UsersModalAddUpdate.onShowDialog("add");
            }
          }}
          searchListeners={(inputSearch) => {
            this.ref_Table.setSearchValue(inputSearch);
            this.setState({ inputSearch }, () => {
              this.onRefresh();
            });
          }}
          exportListeners={() => {
            alert(`Export Callback`);
          }}
          inputSearch={this.state.inputSearch}
          onSearch={(inputSearch) => {
            this.ref_Table.setSearchValue(inputSearch);
            this.setState({ inputSearch }, () => {
              this.onRefresh();
            });
          }}
          disabledBtnExport
          sortListeners={(inputSort) => {
            this.setState({ inputSort }, () => {
              this.onRefresh();
            });
          }}
          dataHeadings={ARR_HEADING}
          dataTables={this.state.fetchData}
          renderItems={this.renderItems}
          pageCount={this.state.fetchPageTotal}
          pageSelected={this.state.fetchPageSelected}
          pageListeners={(fetchPageSelected) => {
            this.setState({ fetchPageSelected }, () => this.onFetchData(true));
          }}
          pageRow={this.state.fetchDataLimit}
          pageRowListeners={(fetchDataLimit) => {
            this.setState({ fetchDataLimit }, () => this.onFetchData(true));
          }}
          onReload={this.onRefresh}
          isFetched={this.state.isFetched || this.props.isAuthLoading}
          // filter
          inputFilter={this.state.inputFilter}
          onRemoveFilterItem={this.onRemoveFilterListeners}
          filterListeners={() => {
            this.onFilterListeners();
          }}
        />

        <Loading ref={(value) => (this.ref_Loading = value)} />
        <MySnackbar ref={(value) => (this.ref_MySnackbar = value)} />
        <ModalConfirmation
          ref={(value) => (this.ref_ModalConfirmation = value)}
        />

        <UsersModalAddUpdate
          ref={(value) => (this.ref_UsersModalAddUpdate = value)}
          onResults={(formType, formData, formIndex) => {
            this.onRefresh();
          }}
        />

        <UserModalListFilter
          ref={(value) => (this.ref_UserModalListFilter = value)}
          onFilter={this.onFilterChangeListeners}
        />
      </>
    );
  }

  renderItems = (item, index) => {
    return (
      <tr key={index} style={{ opacity: item.active_bool ? 1 : 0.4 }}>
        <td className="text-center">
          <div className="actions">
            <Tooltip title="Ubah Data">
              <IconButton
                onClick={() => {
                  this.ref_UsersModalAddUpdate.onShowDialog(
                    "edit",
                    item,
                    index
                  );
                }}
              >
                <i className="ph ph-bold ph-pencil-simple text-blue"></i>
              </IconButton>
            </Tooltip>
            <Tooltip title="Hapus Data">
              <IconButton
                onClick={() => {
                  this.ref_ModalConfirmation.onShowDialog("delete", {
                    text: {
                      title: "Pengguna",
                      action: "Hapus",
                      info: item.name,
                    },
                    onConfirmed: (formType, formData, formIndex) => {
                      this.onSubmitDelete(item, index);
                    },
                  });
                }}
              >
                <i className="ph ph-bold ph-trash-simple text-red"></i>
              </IconButton>
            </Tooltip>
            <Tooltip
              title={item.active_bool ? "Non-aktifkan Data" : "Aktifkan Data"}
            >
              <Switch
                checked={item.active_bool}
                onClick={() => {
                  this.ref_ModalConfirmation.onShowDialog("activated", {
                    text: {
                      title: "Pengguna",
                      action: item.active_bool ? "Non-Aktifkan" : "Aktifkan",
                      info: item.name,
                    },
                    onConfirmed: (formType, formData, formIndex) => {
                      this.onSubmitActivate(item, index);
                    },
                  });
                }}
              />
            </Tooltip>
          </div>
        </td>
        <td style={{ width: "100%" }}>
          <img
            src={item.image_url || Constants.image_default.empty}
            style={{
              border: "none",
              backgroundColor: "transparent",
              width: 30,
              height: 30,
              objectFit: "contain",
            }}
          />
          {item.name || "-"}
        </td>
        <td>{item.username || "-"}</td>
        <td>{item.email || "-"}</td>
        <td>{item.mobilephone || "-"}</td>
        <td>{item.role_name || "-"}</td>
        <td className="text-center">{item.type || "-"}</td>
      </tr>
    );
  };
}
