/*
Created by esoda
Created on Nov, 2024
Contact esoda.id
*/

import React from "react";
import PageLayout from "@/components/PageLayout";

export default class Custom404 extends React.Component {
  onBack = () => {
    window.history.back();
  };

  render() {
    return (
      <div
        className="flex flex-col gap-4"
        style={{ height: "100dvh", width: "100%" }}
      >
        <div className="not-found gap-4">
          <img
            className="image"
            alt="404 Not Found"
            src="/assets/images/img_logo_404.png"
          />
          <div className="title">Halaman Tidak Ditemukan</div>
          <div className="description">
            <PERSON>hon maaf halaman yang Anda cari tidak ditemukan atau belum
            tersedia.
          </div>
          <button className="button" onClick={this.onBack}>
            <i class="ph ph-bold ph-arrow-left"></i>
            <span><PERSON><PERSON><PERSON> ke <PERSON> Sebelumnya</span>
          </button>
        </div>
      </div>
    );
  }
}
