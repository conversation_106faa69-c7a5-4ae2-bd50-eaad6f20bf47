/*
Created by esoda
Created on Apr, 2025
Contact esoda.id
*/

import React, { useEffect, useRef } from "react";
import styles from "@/styles/Pos.module.css";
import Loading from "@/components/modal/Loading";
import Constants from "@/utils/Constants";
import AuthWrapper from "@/components/wrapper/AuthWrapper";
import useTrackedLoginStore from "@/store/kiosk/login/store";
import MySnackbar from "@/components/MySnackbar";
import KioskLoginModalSelectBranch from "@/components/pages/kiosk/login/KioskLoginModalSelectBranch";
import Router from "next/router";

const KioskLogin = ({ isAuthLoading, isLoggedIn }) => {
  const { setLoadingRef, setState } = useTrackedLoginStore();
  const loadingRef = useRef(null);
  const ref_MySnackbar = useRef(null);

  useEffect(() => {
    if (isAuthLoading) {
      loadingRef.current.onShowDialog();
      return;
    } else {
      loadingRef.current.onCloseDialog();
    }

    // Set loading ref in the store
    if (loadingRef.current) {
      setLoadingRef(loadingRef.current);
    }

    setState({ ref_MySnackbar: ref_MySnackbar });

    if (isLoggedIn) {
      Router.replace({ pathname: "/pos" });
      return;
    }

    setTimeout(() => {
      const inputUsername = document.getElementById("input-username");
      if (inputUsername) {
        inputUsername.focus();
      }
    }, 250);
  }, [isAuthLoading]);

  return (
    <div className={`${styles.lg_containers}`}>
      <div className={styles.contents}>
        <RenderForm />
      </div>

      <Loading ref={loadingRef} />
      <MySnackbar ref={ref_MySnackbar} />
      <KioskLoginModalSelectBranch />
    </div>
  );
};

// RenderForm
const RenderForm = () => {
  const { onValidateInputs } = useTrackedLoginStore();

  return (
    <div className={styles.form}>
      <img alt={Constants.appName} src="/assets/images/logo-small.png" />

      <h1 className="text-center">Kiosk Els</h1>

      <RenderFormInputUsername />
      <RenderFormInputPassword />

      <button className={styles.submit} onClick={onValidateInputs}>
        Login Kiosk
      </button>

      <div className={styles.info}>
        <p className="text-center">2025 © ELS</p>
      </div>
    </div>
  );
};

const RenderFormInputUsername = () => {
  const { inputs, errors, onTextInputListeners, onTextErrorListeners } =
    useTrackedLoginStore();

  return (
    <div className={`${styles.inputs} ${errors.username && styles.errors}`}>
      <div className={styles.label}>
        <i className="ph ph-bold ph-envelope-simple"></i>
      </div>
      <input
        id="input-username"
        placeholder="Username Anda"
        value={inputs.username || ""}
        onFocus={() => onTextErrorListeners(null, "username")}
        onChange={(e) => onTextInputListeners(e.target.value, "username")}
        autoComplete="off"
      />
      {errors.username && (
        <div className={styles.errIcon}>
          <i className={`ph ph-bold ph-warning-circle`}></i>
          <span>{errors.username || "Username tidak terdaftar."}</span>
        </div>
      )}
    </div>
  );
};

const RenderFormInputPassword = () => {
  const {
    inputs,
    errors,
    showPassword,
    onTextInputListeners,
    onTextErrorListeners,
    toggleShowPassword,
  } = useTrackedLoginStore();

  return (
    <div className={`${styles.inputs} ${errors.password && styles.errors}`}>
      <div className={styles.label}>
        <i className="ph ph-bold ph-lock"></i>
      </div>
      <input
        id="input-password"
        placeholder="Password Anda"
        value={inputs.password || ""}
        onFocus={() => onTextErrorListeners(null, "password")}
        onChange={(e) => onTextInputListeners(e.target.value, "password")}
        type={showPassword ? "text" : "password"}
      />
      {errors.password ? (
        <div className={styles.errIcon}>
          <i className={`ph ph-bold ph-warning-circle`}></i>
          <span>{errors.password || "Password tidak sesuai."}</span>
        </div>
      ) : (
        <button onClick={toggleShowPassword}>
          <i
            className={`ph ph-bold ${
              !showPassword ? "ph-eye-slash" : "ph-eye"
            }`}
          ></i>
        </button>
      )}
    </div>
  );
};

export default AuthWrapper(KioskLogin, {
  redirectTo: Constants.webUrl.home,
  redirectIfFound: true,
});
