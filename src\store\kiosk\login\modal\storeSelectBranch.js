import { create } from "zustand";
import { createTrackedSelector } from "react-tracked";
import CommonHelper from "@/utils/CommonHelper";
import ApiHelper from "@/utils/ApiHelper";
import { useLoginStore } from "../store";
import Router from "next/router";

const state = (set, get) => ({
  setState: (newValue, cb = null, keys = []) => {
    set((prevState) => {
      if (keys.length > 0) {
        return CommonHelper.updateNestedState(prevState, keys, newValue);
      }
      return { ...prevState, ...newValue };
    }, cb);
  },
  selected: null,
  showDialog: false,
  formType: "",
  formData: null,

  onShowDialog: (formType, formData) => {
    let selected = null;
    if (formData?.branches?.length > 0) {
      selected = formData.branches[0];
    }
    
    set({ showDialog: true, formType, formData, selected });
  },
  onCloseDialog: () => {
    set({ showDialog: false, formType: "", formData: null });
  },
  onSelect: (selected) => {
    set({ selected });
  },
  onSubmit: async () => {
    useLoginStore.getState().ref_Loading.onShowDialog();
    let params = {
      selected_branch: get().selected,
    };
    let response = await ApiHelper.post(
      `kiosk/kiosk/change_accurate_branch`,
      params
    );
    if (response.status === 200) {
      useLoginStore
        .getState()
        .onNotify(response?.message || "Berhasil Login", "success");
      useLoginStore.getState().ref_Loading.onCloseDialog();
      get().onCloseDialog();
      Router.replace({ pathname: "/pos" });
    } else {
      useLoginStore
        .getState()
        .onNotify(response?.message || "Terjadi Kesalahan", "error");
      useLoginStore.getState().ref_Loading.onCloseDialog();
    }
  },
});

export const useSelectBranchStore = create((...a) => ({
  ...state(...a),
}));

const useTrackedSelectBranchStore = createTrackedSelector(useSelectBranchStore);

export default useTrackedSelectBranchStore;
