/*
Created by AI
Created on Nov, 2023
Component for uploading single generic files like XLSX, CSV.
Focuses on file selection and providing the File object to the parent.
*/

import React, { useState, useRef, useCallback } from "react";
import Tooltip from "@mui/material/Tooltip";
import MySnackbar from "@/components/MySnackbar"; // Assuming MySnackbar is available for notifications

/**
 * FileUploadSingle2 Component
 *
 * @param {object} props - The component's props.
 * @param {string} props.id - (Required) Unique ID for the file input element.
 * @param {string} [props.title] - Optional title displayed above the upload area.
 * @param {string} [props.label="Pilih File atau Jatuhkan di sini"] - Text for the upload button/dropzone.
 * @param {string} [props.accept=".xlsx,.xls,.csv"] - Comma-separated string of accepted file extensions or MIME types.
 * @param {number} [props.maxSizeMB=5] - Maximum allowed file size in Megabytes.
 * @param {function} props.onCompleted - (Required) Callback function `(fileObject: File) => void` triggered on successful file selection and validation.
 * @param {function} [props.onError] - Optional callback function `(errorMessage: string) => void` triggered on validation error.
 * @param {boolean} [props.optional] - If true, displays "Opsional" helper text.
 * @param {boolean} [props.required] - If true, displays "Wajib" helper text and can be used for form validation.
 * @param {boolean} [props.disabled=false] - If true, disables the file uploader.
 * @param {string} [props.formClass] - Custom class for the main form-input div.
 * @param {React.ReactNode} [props.icon={<i className="ph ph-bold ph-file-arrow-up" />}] - Icon for the upload area.
 * @param {string} [props.selectedFileText="File terpilih:"] - Text prefix for displaying the selected file name.
 * @param {string} [props.clearButtonText="Hapus Pilihan"] - Text for the clear selection button.
 * @param {string} [props.dropzoneHint="Jatuhkan file di sini"] - Hint text for the dropzone.
 * @param {string} [props.fileSizeErrorText="Ukuran file melebihi batas maksimal ({maxSizeMB}MB)."] - Error text for file size.
 * @param {string} [props.fileTypeErrorText="Tipe file tidak diizinkan. Mohon pilih file {accept}."] - Error text for file type.
 * @returns {JSX.Element}
 */
const FileUploadSingle2 = ({
  id, // Required
  title,
  label = "Pilih File atau Jatuhkan di sini",
  accept = ".xlsx,.xls,.csv,.doc,.docx,.pdf,.txt", // More generic default
  maxSizeMB = 10, // Default to 10MB
  onCompleted, // Required
  onError,
  optional,
  required,
  disabled = false,
  formClass = "",
  icon = (
    <i
      className="ph ph-bold ph-file-arrow-up"
      style={{ fontSize: "3rem", color: "#bdc3c7" }}
    />
  ),
  selectedFileText = "File terpilih:",
  clearButtonText = "Hapus Pilihan",
  dropzoneHint = "Jatuhkan file atau klik di sini",
  fileSizeErrorText = "Ukuran file melebihi batas maksimal ({maxSizeMB}MB).",
  fileTypeErrorText = "Tipe file tidak diizinkan. Mohon pilih file {accept}.",
  ...props // Spread any other native input props
}) => {
  const [selectedFile, setSelectedFile] = useState(null);
  const [error, setError] = useState("");
  const [dragActive, setDragActive] = useState(false);
  const inputRef = useRef(null);
  const snackbarRef = useRef(null);

  const notify = (message, severity = "error") => {
    if (snackbarRef.current) {
      snackbarRef.current.onNotify(message, severity);
    } else {
      // Fallback if snackbar is not available (e.g., during SSR or if not included in parent)
      if (severity === "error") console.error(message);
      else console.log(message);
    }
    if (onError && severity === "error") {
      onError(message);
    }
  };

  const validateFile = (file) => {
    if (!file) return false;

    // Validate file type
    const acceptedTypes = accept
      .split(",")
      .map((type) => type.trim().toLowerCase());
    const fileExtension = `.${file.name.split(".").pop().toLowerCase()}`;
    const fileMimeType = file.type.toLowerCase();

    const isTypeValid = acceptedTypes.some((acceptedType) => {
      if (acceptedType.startsWith(".")) {
        // It's an extension
        return fileExtension === acceptedType;
      }
      // It's a MIME type
      return (
        fileMimeType === acceptedType ||
        (acceptedType.endsWith("/*") &&
          fileMimeType.startsWith(acceptedType.replace("/*", "")))
      );
    });

    if (!isTypeValid) {
      const formattedErrorText = fileTypeErrorText.replace("{accept}", accept);
      setError(formattedErrorText);
      notify(formattedErrorText);
      return false;
    }

    // Validate file size
    const maxSizeInBytes = maxSizeMB * 1024 * 1024;
    if (file.size > maxSizeInBytes) {
      const formattedErrorText = fileSizeErrorText.replace(
        "{maxSizeMB}",
        maxSizeMB.toString()
      );
      setError(formattedErrorText);
      notify(formattedErrorText);
      return false;
    }

    setError(""); // Clear previous errors
    return true;
  };

  const handleFileChange = (event) => {
    const file = event.target.files && event.target.files[0];
    if (file) {
      if (validateFile(file)) {
        setSelectedFile(file);
        if (onCompleted) {
          onCompleted(file);
        }
      } else {
        // Clear the input if validation fails
        if (inputRef.current) {
          inputRef.current.value = "";
        }
        setSelectedFile(null);
      }
    }
  };

  const handleDrag = useCallback(
    (e) => {
      e.preventDefault();
      e.stopPropagation();
      if (disabled) return;
      if (e.type === "dragenter" || e.type === "dragover") {
        setDragActive(true);
      } else if (e.type === "dragleave") {
        setDragActive(false);
      }
    },
    [disabled]
  );

  const handleDrop = useCallback(
    (e) => {
      e.preventDefault();
      e.stopPropagation();
      setDragActive(false);
      if (disabled) return;

      const file = e.dataTransfer.files && e.dataTransfer.files[0];
      if (file) {
        if (validateFile(file)) {
          setSelectedFile(file);
          if (onCompleted) {
            onCompleted(file);
          }
          // Manually set input value for consistency if needed, though not strictly necessary for drop
          if (inputRef.current) {
            const dataTransfer = new DataTransfer();
            dataTransfer.items.add(file);
            inputRef.current.files = dataTransfer.files;
          }
        } else {
          setSelectedFile(null);
        }
      }
    },
    [disabled, onCompleted, validateFile]
  );

  const openFileDialog = () => {
    if (inputRef.current && !disabled) {
      inputRef.current.click();
    }
  };

  const clearSelection = () => {
    setSelectedFile(null);
    setError("");
    if (inputRef.current) {
      inputRef.current.value = ""; // Clear the file input
    }
    // Optionally, notify parent that selection was cleared
    if (onCompleted) {
      onCompleted(null);
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  return (
    <div className={`form-input ${formClass}`}>
      {title && (
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {title}
        </label>
      )}
      <div
        className={`
          mt-1 flex flex-col items-center justify-center px-6 pt-5 pb-6 border-2 
          ${dragActive ? "border-blue-500 bg-blue-50" : "border-gray-300"} 
          border-dashed rounded-md transition-colors duration-150 ease-in-out
          ${
            disabled
              ? "bg-gray-100 cursor-not-allowed opacity-70"
              : "cursor-pointer"
          }
        `}
        onClick={openFileDialog}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        role="button"
        tabIndex={disabled ? -1 : 0}
        onKeyDown={(e) => {
          if (e.key === "Enter" || e.key === " ") openFileDialog();
        }}
        {...props}
      >
        <input
          ref={inputRef}
          id={id}
          name={id} // Use id as name if not provided otherwise
          type="file"
          className="sr-only hidden" // Hidden, controlled by label/div click
          accept={accept}
          onChange={handleFileChange}
          disabled={disabled}
        />
        <div className="space-y-1 text-center">
          {icon}
          <div className="flex text-sm text-gray-600">
            {/* <span className={`relative font-medium ${disabled ? 'text-gray-400' : 'text-indigo-600 hover:text-indigo-500'}`}>
              {label}
            </span> */}
          </div>
          {!selectedFile && (
            <p className="text-xs text-gray-500">{dropzoneHint}</p>
          )}
          {selectedFile && (
            <div className="mt-2 text-sm text-gray-700 flex flex-col justify-center items-center gap-4">
              <p>
                <span className="font-semibold">{selectedFileText}</span>{" "}
                {selectedFile.name} ({formatFileSize(selectedFile.size)})
              </p>
              {!disabled && (
                <Tooltip title={clearButtonText}>
                  <button
                    type="button"
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent opening file dialog
                      clearSelection();
                    }}
                    className="button danger mt-1 text-xs text-red-600 hover:text-red-800 font-medium"
                  >
                    <i className="ph ph-bold ph-trash"></i>
                    {clearButtonText}
                  </button>
                </Tooltip>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Helper text and error display */}
      <div className="placeholder mt-1">
        {required && !disabled && <span className="required">Wajib</span>}
        {optional && !disabled && <span className="optional">Opsional</span>}
        {disabled && <span className="read-only">Tidak aktif</span>}
        <span className={`note ${error ? "text-red-600" : "text-gray-500"}`}>
          {!error ? (
            <>
              Maks. {maxSizeMB}MB. Tipe: {accept}
            </>
          ) : (
            error
          )}
        </span>
      </div>
      <MySnackbar ref={snackbarRef} />
    </div>
  );
};

export default FileUploadSingle2;
