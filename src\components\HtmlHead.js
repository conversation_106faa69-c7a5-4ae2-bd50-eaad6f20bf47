/*
Created by esoda
Created on Nov, 2024
Contact esoda.id
*/

import React from "react";
import Head from 'next/head';
import Constants from "@/utils/Constants";

export default function HtmlHead({ title, description }) {
    return(
        <Head>
            <title>{`${title} :: ${Constants.appName}`}</title>
            <meta name="description" content={description !== undefined ? description : Constants.appDescription} />
            <meta name="HandheldFriendly" content="True"/>
            <meta name="MobileOptimized" content="320"/>
            <meta name="viewport" content="width=device-width, initial-scale=1" />
            <meta name="apple-mobile-web-app-capable" content="yes"/>
            <meta name="apple-mobile-web-app-status-bar-style" content="black"/>
            <meta name="mobile-web-app-capable" content="yes"/>
            <meta name="theme-color" content={"#fff"}/>
            <link rel="icon" href="/favicon.ico" />
        </Head>
    )
}