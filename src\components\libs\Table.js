/*
Created by esoda
Created on Mar, 2025
Contact esoda.id
*/

import React from "react";
import Pagination from "@mui/material/Pagination";
import IconButton from "@mui/material/IconButton";
import Tooltip from "@mui/material/Tooltip";
import Skeleton from "@mui/material/Skeleton";

export default class Table extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      dataHeadings: this.props.dataHeadings,
      pageRowIsOpen: false,
      pageRowOptions: [10, 20, 50, 100],
      inputSearch: "",
    };
  }
  // ====================================================================================
  // ========== INITIALIZE, GET DATA ====================================================
  // ====================================================================================

  // ====================================================================================
  // ========== ACTION LISTENERS ========================================================
  // ====================================================================================
  onPageChangeListeners = (event, value) => {
    this.setState({ pageSelected: value });
    const { pageListeners } = this.props;
    if (pageListeners) {
      pageListeners(value);
    }
  };
  onPageReloadListeners = () => {
    const { onReload } = this.props;
    if (onReload) {
      onReload();
    }
  };
  onRowChangeListeners = (value) => {
    this.setState({ pageRowIsOpen: false });
    const { pageRowListeners } = this.props;
    if (pageRowListeners) {
      pageRowListeners(value);
    }
  };
  onSortListeners = (item, index) => {
    let dataHeadings = this.state.dataHeadings;

    let inputSort = "";
    if (item.sort === "") {
      item.sort = "asc";
      inputSort = item.key;
    } else if (item.sort === "asc") {
      item.sort = "desc";
      inputSort = `-${item.key}`;
    } else {
      item.sort = "";
    }

    dataHeadings[index] = item;
    dataHeadings.map((val) => {
      if (val.sortable && val.key !== item.key) {
        val.sort = "";
      }
      return val;
    });

    this.setState({ dataHeadings });
    const { sortListeners } = this.props;
    if (sortListeners !== undefined) {
      sortListeners(inputSort);
    }
  };

  setSearchValue = (val) => {
    this.setState({ inputSearch: val });
  };

  // ====================================================================================
  // ========== RENDER SECTIONS =========================================================
  // ====================================================================================
  render() {
    const { stylesContainer = {}, disableTitle = false, renderHeadTop = null, disabledBtnImport = true } = this.props
    let resultPadingTop = 0;
    if (this.props.inputSearch && this.props.inputSearch !== "") {
      resultPadingTop = "1rem";
    }
    if (this.props.inputFilter && this.props.inputFilter.length > 0) {
      resultPadingTop = "1rem";
    }
    return (
      <div
        className={`libs-table-containers ${this.props.customTableContainers}`}
        style={{ ...stylesContainer }}
      >
        {!this.props.disabledHeader && (
          <div className="head">
            {renderHeadTop && renderHeadTop()}
            {!disableTitle && this.props.title !== undefined && this.props.title !== "" && (
              <div
                className="topbar"
                style={{
                  paddingBottom: !this.props.disabledSearch ? ".9rem" : 0,
                }}
              >
                <div className="title">{this.props.title}</div>
                {this.props.subtitle !== undefined &&
                  this.props.subtitle !== "" && (
                    <div className="subtitle">{this.props.subtitle}</div>
                  )}
              </div>
            )}
            {this.props.topComponent}
            <div className="searchbar">
              {!this.props.disabledAddBtn && (
                <button
                  className={`${this.props.isFetched && "disabled_opacity"}`}
                  onClick={() => {
                    if (this.props.isFetched) {
                      return;
                    }
                    const { addListeners } = this.props;
                    if (addListeners) {
                      addListeners();
                    }
                  }}
                >
                  <i className="ph ph-bold ph-plus-circle"></i>
                  <span>{this.props.addTitle || `Data Baru`}</span>
                </button>
              )}
              {this.props.btnComponent}
              {!this.props.disabledSearch && (
                <>
                  <div className="search">
                    <label for="search-input">
                      <i className="ph ph-bold ph-magnifying-glass"></i>
                    </label>
                    <input
                      type="text"
                      name="search"
                      id="search-input"
                      placeholder="Cari data disini..."
                      value={this.state.inputSearch}
                      onChange={(event) => {
                        if (
                          event.target !== undefined &&
                          event.target.value !== undefined
                        ) {
                          this.setState({ inputSearch: event.target.value });
                        }
                      }}
                      onKeyDown={(e) => {
                        if (!this.props.isFetched) {
                          if (e.key === "Enter") {
                            if (this.state.inputSearch) {
                              this.props.onSearch(this.state.inputSearch);
                            }
                          }
                          if (e.key === "Delete" || e.key === "Backspace") {
                            if (this.state.inputSearch.length === 1) {
                              this.props.onSearch("");
                            }
                          }
                        }
                      }}
                    />
                    {this.state.inputSearch !== undefined &&
                      this.state.inputSearch !== "" && (
                        <Tooltip title={"Hapus pencarian ini"}>
                          <i
                            className="ph ph-bold ph-x-circle"
                            onClick={() => {
                              this.props.onSearch("");
                            }}
                          ></i>
                        </Tooltip>
                      )}
                  </div>
                </>
              )}

              {!this.props.disabledBtnFilter && (
                <button
                  onClick={() => {
                    const { filterListeners } = this.props;
                    if (filterListeners) {
                      filterListeners();
                    }
                  }}
                >
                  <i className="ph ph-bold ph-faders"></i>
                  <span>Filter</span>
                </button>
              )}
              {/* import */}
              {!disabledBtnImport && (
                <button
                  onClick={() => {
                    const { importListeners } = this.props;
                    if (importListeners) {
                      importListeners();
                    }
                  }}
                >
                  <i className="ph ph-bold ph-upload"></i>
                  <span>Import</span>
                </button>
              )}
              {!this.props.disabledBtnExport && (
                <button
                  onClick={() => {
                    const { exportListeners } = this.props;
                    if (exportListeners) {
                      exportListeners();
                    }
                  }}
                >
                  <i className="ph ph-bold ph-download"></i>
                  <span>Unduh</span>
                </button>
              )}
            </div>
            {!this.props.disabledSearch && (
              <div className="results" style={{ paddingTop: resultPadingTop }}>
                {this.props.inputSearch !== undefined &&
                  this.props.inputSearch !== "" && (
                    <Tooltip title={"Hapus pencarian ini"}>
                      <div
                        className="search"
                        onClick={() => {
                          const { searchListeners } = this.props;
                          if (searchListeners) {
                            searchListeners("");
                          }
                        }}
                      >
                        {this.props.inputSearch}
                        <i className="ph ph-bold ph-x-circle"></i>
                      </div>
                    </Tooltip>
                  )}
                {this.props.inputFilter !== undefined &&
                  this.props.inputFilter.length > 0 &&
                  this.props.inputFilter.map((item, index) => {
                    return (
                      <Tooltip key={index} title={"Hapus filter ini"}>
                        <div
                          className="filter"
                          onClick={() => {
                            // const { filterListeners } = this.props
                            // if (filterListeners) { filterListeners(item, index) }
                            this.props.onRemoveFilterItem(index);
                          }}
                        >
                          {item.title}
                          <i className="ph ph-bold ph-x-circle"></i>
                        </div>
                      </Tooltip>
                    );
                  })}
              </div>
            )}
          </div>
        )}
        <div className="body">
          <table>
            {this.renderheadings()}
            {this.renderTableItems()}
          </table>
          {this.renderEmpty()}
        </div>
        {!this.props.disabledPage && (
          <div className="page">
            <div className="left">
              <select
                name="row-page"
                id="row-page"
                onChange={(e) => {
                  const { pageRowListeners } = this.props;
                  if (pageRowListeners) {
                    pageRowListeners(e.target.value);
                  }
                }}
              >
                {this.state.pageRowOptions.map((rowOpt, rowIdx) => {
                  return (
                    <option key={rowIdx} value={rowOpt}>
                      {rowOpt}
                    </option>
                  );
                })}
              </select>
              <span>Baris per halaman</span>
            </div>
            <div className="right">
              <Pagination
                count={this.props.pageCount}
                page={this.props.pageSelected || 1}
                onChange={this.onPageChangeListeners}
                shape="rounded"
                showFirstButton
                showLastButton
                siblingCount={0}
                boundaryCount={0}
                color="primary"
                sx={{ zIndex: 0 }}
              />
              <Tooltip title="Muat ulang data pada halaman">
                <IconButton onClick={this.onPageReloadListeners}>
                  <i
                    className="ph ph-bold ph-arrow-clockwise"
                    style={{ color: "#16a085" }}
                  ></i>
                </IconButton>
              </Tooltip>
            </div>
          </div>
        )}
      </div>
    );
  }
  renderheadings() {
    let arrData = this.props.dataHeadings;
    if (arrData.length > 0) {
      return (
        <thead>
          <tr>
            {arrData.map((item, index) => {
              return (
                <th key={index} className={item.className}>
                  <div
                    className={item.sortable && "sorts"}
                    onClick={() => {
                      if (item.sortable) {
                        this.onSortListeners(item, index);
                      }
                    }}
                  >
                    {item.label === "" && (
                      <i className="ph ph-bold ph-dots-three-outline"></i>
                    )}
                    {item.label !== "" && item.label}
                    {item.sortable && item.sort === "" && (
                      <i className="ph ph-bold ph-arrows-down-up"></i>
                    )}
                    {item.sortable && item.sort === "asc" && (
                      <i className="ph ph-bold ph-sort-ascending"></i>
                    )}
                    {item.sortable && item.sort === "desc" && (
                      <i className="ph ph-bold ph-sort-descending"></i>
                    )}
                  </div>
                </th>
              );
            })}
          </tr>
        </thead>
      );
    }
  }
  renderTableItems() {
    if (this.props.isFetched) {
      let arrHeading = this.props.dataHeadings;
      return (
        <tbody>
          {[1, 2, 3].map((_, index) => {
            return (
              <tr key={index}>
                {arrHeading.map((_, key) => {
                  return (
                    <td key={key}>
                      <Skeleton
                        variant="rounded"
                        height={14}
                        style={{ marginTop: 0 }}
                      />
                    </td>
                  );
                })}
              </tr>
            );
          })}
        </tbody>
      );
    } else {
      let arrData = this.props.dataTables;
      if (arrData.length > 0) {
        return (
          <tbody>
            {arrData.map((item, index) => {
              if (this.props.renderItems) {
                return this.props.renderItems(item, index);
              }
            })}
          </tbody>
        );
      }
    }
  }
  renderEmpty() {
    if (!this.props.isFetched) {
      if (this.props.dataTables.length <= 0) {
        return (
          <div className="libs-table-containers-empty">
            <img src="/assets/images/img_empty.png" alt="Tidak Ada Data" />
            <h3>Tidak Ada Data</h3>
            <p>
              Tidak ada {this.props.title}. Atau data yang Anda cari tidak
              ditemukan.
            </p>
          </div>
        );
      }
    }
  }
}
