/*
Created by esoda
Created on Mar, 2025
Contact esoda.id
-- Styled for Table.js libs components
USE: Import on _app.js
*/

.libs-table-containers {
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    font: inherit;
    background-color: white;
    border-radius: 2px 2px 0 0;
    -webkit-border-radius: 2px 2px 0 0;
    -moz-border-radius: 2px 2px 0 0;
    -ms-border-radius: 2px 2px 0 0;
    -o-border-radius: 2px 2px 0 0;
    box-shadow: 0 .4rem 1rem #ededed55;
    border: solid 1px #eeeeee;
}

/*? SECTION FOR TABLE HEAD ==== */
.libs-table-containers .head {
    padding: 1.2rem 1.2rem 1rem;
}

.libs-table-containers .head .topbar {
    padding-bottom: .9rem;
}

.libs-table-containers .head .topbar .title {
    font-size: 1.1rem;
    color: #232f45;
    font-weight: 600;
    text-transform: uppercase;
}

.libs-table-containers .head .topbar .subtitle {
    font-size: .8rem;
    color: var(--text-foreign-color);
    line-height: 1;
}

.libs-table-containers .head .searchbar {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: .9rem;
}

.libs-table-containers .head .searchbar .search {
    flex-grow: 1;
    min-width: 35%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    height: 40px;
    border: solid 1px rgb(159, 159, 159);
    border-radius: 3px;
    overflow: hidden;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -ms-border-radius: 3px;
    -o-border-radius: 3px;
    padding: 0 .6rem;
}

.libs-table-containers .head .searchbar .search label {
    width: fit-content;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgb(159, 159, 159);
}

.libs-table-containers .head .searchbar .search label i {
    font-size: 1.1rem;
}

.libs-table-containers .head .searchbar .search input {
    flex-grow: 1;
    color: #232f45;
    border: none;
    outline: none;
    padding-left: .5rem;
}

.libs-table-containers .head .searchbar .search input::placeholder {
    color: rgb(189, 189, 189);
}

.libs-table-containers .head .searchbar .search:has(input:hover) {
    border-color: var(--base-color);
}

.libs-table-containers .head .searchbar .search:has(input:focus) {
    border-color: #232f45;
}

.libs-table-containers .head .searchbar .search:has(input:hover) label i {
    color: var(--base-color);
}

.libs-table-containers .head .searchbar .search:has(input:focus) label i {
    color: #232f45;
}

.libs-table-containers .head .searchbar button {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0 .8rem;
    height: 40px;
    background-color: #232f45;
    border: none;
    color: #ffffff;
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    -ms-border-radius: 3px;
    -o-border-radius: 3px;
    font: inherit;
    font-weight: 600;
    text-transform: uppercase;
    cursor: pointer;
}

.libs-table-containers .head .searchbar button span {
    font-size: .95rem;
}

.libs-table-containers .head .searchbar button:first-child {
    background-color: var(--base-color);
}

.libs-table-containers .head .searchbar button i {
    margin-right: .5rem;
    font-size: 1.1rem
}

.libs-table-containers .head .searchbar button:hover,
.libs-table-containers .head .searchbar button:focus {
    background-color: rgb(35, 41, 43);
}

.libs-table-containers .head .searchbar button:first-child:hover,
.libs-table-containers .head .searchbar button:first-child:focus {
    background-color: var(--base-hover-color);
}

.libs-table-containers .head .results {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: wrap;
    gap: .6rem;
    padding-top: 1rem;
}

.libs-table-containers .head .results .search,
.libs-table-containers .head .results .filter {
    background-color: #8d8d8d;
    color: #fff;
    font-size: .8rem;
    display: flex;
    flex-direction: row;
    align-items: center;
    border-radius: 100px;
    -webkit-border-radius: 100px;
    -moz-border-radius: 100px;
    -ms-border-radius: 100px;
    -o-border-radius: 100px;
    padding: 0rem .7rem;
    text-transform: uppercase;
    font-weight: 600;
    cursor: pointer;
}

.libs-table-containers .head .results .filter {
    background-color: #3498db;
}

.libs-table-containers .head .results .search i,
.libs-table-containers .head .results .filter i {
    font-size: 1.6rem;
    margin-right: -.7rem;
    margin-left: .5rem;
}

/*? SECTION FOR TABLE BODY ==== */
.libs-table-containers .body {
    flex-grow: 1;
    background-color: #f5f5f5;
    width: 100%;
    margin: 0 auto;
    overflow: auto;
}

.libs-table-containers .body table {
    width: 100%;
}

.libs-table-containers .body td img {
    width: 50px;
    height: 50px;
    margin-right: .8rem;
    vertical-align: middle;
    border: solid 1px rgb(237, 237, 237);
    object-fit: scale-down;
}

.libs-table-containers .body table,
.libs-table-containers .body th,
.libs-table-containers .body td {
    color: #232f45;
    border-collapse: collapse;
    text-align: left;
    text-wrap: nowrap;
    font-size: .95rem;
    font-weight: normal;
}

.libs-table-containers .body thead th {
    z-index: 1;
    position: sticky;
    top: 0;
    left: 0;
    background-color: #232f45;
}

.libs-table-containers .body thead th>div {
    text-transform: uppercase;
    color: white;
    font-weight: 600;
    font-size: .9rem;
}

.libs-table-containers .body thead th>.sorts {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
}

.libs-table-containers .body thead th>.sorts>i {
    margin-left: 1rem;
    margin-right: -5px;
}

.libs-table-containers .body thead th,
.libs-table-containers .body table td {
    padding: .6rem 1.2rem;
}

.libs-table-containers .body table td .rows {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
}

.libs-table-containers .body table tr td:first-child {
    position: sticky;
    top: 0;
    left: 0;
}

.libs-table-containers .body table td .actions {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: .3rem;
}

.libs-table-containers .body tbody tr:nth-child(odd),
.libs-table-containers .body table tr:nth-child(odd) td:first-child {
    background-color: #ffffff;
}

.libs-table-containers .body tbody tr:nth-child(even),
.libs-table-containers .body table tr:nth-child(even) td:first-child {
    background-color: #f9f9f9;
}

.libs-table-containers .body tbody tr:hover {
    background-color: rgb(236, 236, 236);
    cursor: pointer;
}

.libs-table-containers .body tbody tr:hover td:first-child {
    background-color: rgb(236, 236, 236);
    cursor: pointer;
}

/*? SECTION FOR TABLE PAGE ==== */
.libs-table-containers .page {
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: .6rem 1.2rem;
    border-top: solid 1px #ededed;
}

.libs-table-containers .page .left {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: .8rem;
}

.libs-table-containers .page .left select {
    cursor: pointer;
    border: none;
    outline: none;
    background-color: white;
    height: 30px;
    border: solid 1px rgb(214, 214, 214);
    border-radius: 3px;
    min-width: 70px;
    padding: 0 5px;
}

.libs-table-containers .page .left select:hover {
    border-color: var(--base-color);
}

.libs-table-containers .page .left span {
    font-size: .85rem;
    color: #8d8d8d;
}

.libs-table-containers .page .right {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: .8rem;
}

@media only screen and (max-width: 900px) {
    .libs-table-containers .page .left span {
        display: none;
    }
}

@media only screen and (max-width: 720px) {
    .libs-table-containers {
        background-color: #fff;
        border-radius: 0;
        -webkit-border-radius: 0;
        -moz-border-radius: 0;
        -ms-border-radius: 0;
        -o-border-radius: 0;
        -webkit-border-radius: 0;
        box-shadow: none;
        border: none;
    }

    .libs-table-containers .head {
        padding: 0rem;
    }

    .libs-table-containers .head .topbar {
        padding: .8rem 1rem;
    }

    .libs-table-containers .head .searchbar {
        gap: 0;
    }

    .libs-table-containers .head .searchbar .search {
        flex-grow: 1;
        border: none;
        border-top: solid 1px rgb(197, 197, 197);
        border-bottom: solid 1px rgb(197, 197, 197);
    }

    .libs-table-containers .head .searchbar .search input,
    .libs-table-containers .head .searchbar button {
        border-radius: 0px;
        -webkit-border-radius: 0px;
        -moz-border-radius: 0px;
        -ms-border-radius: 0px;
        -o-border-radius: 0px;
        border: none;
    }

    .libs-table-containers .head .searchbar button i {
        margin-right: 0;
    }

    .libs-table-containers .head .results {
        padding: .6rem 1rem;
    }

    .libs-table-containers .body table tr td:first-child {
        position: relative;
    }

    .libs-table-containers .page {
        justify-content: center;
    }

    .libs-table-containers .page .left,
    .libs-table-containers .head .searchbar button span {
        display: none;
    }
}

.libs-table-containers-empty {
    width: 100%;
    height: 85%;
    align-items: center;
    justify-content: center;
    display: flex;
    flex-direction: column;
    padding: 35px 0;
}
.libs-table-containers-empty img {
    width: 150px;
    height: 150px;
}
.libs-table-containers-empty h3 {
    font-size: 1.2rem;
    line-height: 1;
    margin-top: 2rem;
    text-align: center;
}
.libs-table-containers-empty p {
    font-size: .9rem;
    margin-top: .5rem;
    text-align: center;
}