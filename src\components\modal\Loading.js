/*
Created by esoda
Created on Nov, 2024
Contact esoda.id
*/

import React from "react";
import { Modal, Box } from '@mui/material';
import Image from 'next/image';
import CircularProgress from '@mui/material/CircularProgress';

export default class Loading extends React.Component {
    constructor(props) {
        super(props)
        this.state = { showDialog: false }
    }

    // ====================================================================================
    // ========== INITIALIZE, GET DATA ====================================================
    // ====================================================================================

    // ====================================================================================
    // ========== ACTION LISTENERS ========================================================
    // ====================================================================================
    onShowDialog = () => { this.setState({ showDialog:true }) }
    onCloseDialog = () => { this.setState({ showDialog:false }) }

    // ====================================================================================
    // ========== RENDER SECTIONS =========================================================
    // ====================================================================================
    render() {
        return(
            <Modal
                open={this.state.showDialog}
                onClose={this.onCloseDialog}
                >
                <Box sx={{
                    flex: 1,
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                    display: 'flex',
                    width: '100wh',
                    height: '100vh',
                    }}>
                    <div style={{
                        width:80,
                        height:80,
                        borderRadius:40,
                        overflow:"hidden",
                        display:"flex",
                        alignItems:"center",
                        justifyContent:"center",
                        backgroundColor:"#fff",
                        position:"relative"
                        }}>
                        <CircularProgress size={70} />
                        <div style={{
                            position:"absolute",
                            width:"100%",
                            height:"100%",
                            overflow:"hidden",
                            display:"flex",
                            alignItems:"center",
                            justifyContent:"center"
                            }}>
                            <Image src="/assets/images/logo-small.png"
                                alt="sodapos.com"
                                width={32}
                                height={32}
                                priority
                                style={{ objectFit: "contain" }}
                            />
                        </div>
                    </div>
                </Box>
            </Modal>
        )
    }
}