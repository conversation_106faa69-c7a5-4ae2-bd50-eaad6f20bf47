/*
Created by esoda
Created on Apr, 2025
Contact esoda.id
*/

import React from "react";
import styles from "@/styles/Pos.module.css";
import stylesTable from "@/styles/Table.module.css";

import Loading from "@/components/modal/Loading";
import PosComponentTopbar from "@/components/pages/adm/_topbar";
import Router from "next/router";

import Constants from "@/utils/Constants";
import AuthWrapperAdmin from "@/components/wrapper/AuthWrapperAdmin";
import AdminSideBar from "@/components/pages/adm/AdminSideBar";

import CommonHelper from "@/utils/CommonHelper";
import ModalConfirmation from "@/components/modal/ModalConfirmation";
import MySnackbar from "@/components/MySnackbar";
import UserList from "@/components/pages/adm/setting/users/UserList";
import AccessRoleList from "@/components/pages/adm/setting/users/AccessRoleList";

class AdminUserSetting extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      selectedTabIdx: 0,
      selectedTabTitle: "Data Pengguna",
    };
  }

  // ====================================================================================
  // ========== INITIALIZE, GET DATA ====================================================
  // ====================================================================================
  componentDidUpdate(prevProps, prevState) {
    if (prevProps.isAuthLoading !== this.props.isAuthLoading) {
      if (!this.props.isAuthLoading && this.props.isLoggedIn) {
        let query = CommonHelper.getAllURLParams();
        if (!query.slug) {
          this.onSelectedTabListener(0, "Data Pengguna");
        } else {
          let index = 0;
          let title = "Data Pengguna";
          if (query.slug === "user") {
            index = 0;
            title = "Data Pengguna";
          } else if (query.slug === "access") {
            index = 1;
            title = "Data Hak Akses";
          }
          this.onSelectedTabListener(index, title);
        }
      }
    }
  }

  onRefresh = () => {
    if (this.ref_UserList?.onInit) {
      this.ref_UserList?.onInit();
    } else if (this.ref_AccessRoleList?.onInit) {
      this.ref_AccessRoleList?.onInit();
    }
  };
  // ====================================================================================
  // ========== ACTION LISTENERS ========================================================
  // ====================================================================================

  onSelectedTabListener = (index, title) => {
    let query = {};
    if (index === 0) {
      query.slug = "user";
    } else if (index === 1) {
      query.slug = "access";
    }
    Router.replace({ pathname: window.location.pathname, query }, undefined, {
      shallow: true,
    });
    this.setState(
      {
        selectedTabIdx: index,
        selectedTabTitle: title,
      },
      this.onRefresh
    );
  };
  // ====================================================================================
  // ========== RENDER SECTIONS =========================================================
  // ====================================================================================
  render() {
    const { isAuthLoading, user, menu } = this.props;
    return (
      <div
        className={`${styles.ct_containers}`}
        style={{ backgroundColor: "rgb(252, 252, 252)" }}
      >
        <PosComponentTopbar
          hideButton
          title={"Pengguna & Hak Akses"}
          subtitle={"Atur pengguna dan hak akses pengguna"}
          user={user}
          isAuthLoading={isAuthLoading}
        />
        <div className={`${styles.contents} ${styles.sidebar}`}>
          <AdminSideBar isAuthLoading={isAuthLoading} menu={menu} />
          {this.renderContent()}
        </div>

        <Loading ref={(value) => (this.ref_Loading = value)} />
        <MySnackbar ref={(value) => (this.ref_MySnackbar = value)} />
        <ModalConfirmation
          ref={(value) => (this.ref_ModalConfirmation = value)}
        />
      </div>
    );
  }

  renderContent() {
    return (
      <div className={styles.ctx}>
        <div className="tab-bar">
          <div
            className={`item ${this.state.selectedTabIdx === 0 && "selected"}`}
            onClick={() => {
              this.onSelectedTabListener(0, "Data Pengguna");
            }}
          >
            Data Pengguna
          </div>
          <div
            className={`item ${this.state.selectedTabIdx === 1 && "selected"}`}
            onClick={() => {
              this.onSelectedTabListener(1, "Data Hak Akses");
            }}
          >
            Data Hak Akses
          </div>
        </div>
        {this.state.selectedTabIdx === 0 && (
          <UserList
            ref={(value) => (this.ref_UserList = value)}
            selectedTabTitle={this.state.selectedTabTitle}
            isAuthLoading={this.props.isAuthLoading}
            selected_aol={this.props.selected_aol}
          />
        )}
        {this.state.selectedTabIdx === 1 && (
          <AccessRoleList
            ref={(value) => (this.ref_AccessRoleList = value)}
            selectedTabTitle={this.state.selectedTabTitle}
            isAuthLoading={this.props.isAuthLoading}
            selected_aol={this.props.selected_aol}
          />
        )}
      </div>
    );
  }
}

export default AuthWrapperAdmin(AdminUserSetting, {
  redirectTo: Constants.webUrl.adm.login,
});
