import { FILTER_VIEW_DATA } from "./const/FILTER";

class FilterHelper {
  constructor() {}

  // {
  //   objectFilter: {
  //     field: (filter, key) => {
  //       return "date";
  //     },
  //     fieldObj: (filter, key) => {
  //       if (!filter.start_date) {
  //         return {};
  //       }
  //       return {
  //         [`date[gte]`]: filter.start_date,
  //         [`date[lte]`]: filter.end_date,
  //       };
  //     },
  //     removeFieldObj: [`date[gte]`, `date[lte]`],
  //     value: (filter, key) => {
  //       if (!filter.start_date) {
  //         return "";
  //       }
  //       return `${filter.start_date}::${filter.end_date}`;
  //     },
  //     title: (filter, key) => {
  //       return `Tanggal Produksi: ${filter.title}`;
  //     },
  //     type: FILTER_TYPE_DATE,
  //     comparison: FILTER_COMP_BETWEEN,
  //   }
  // }
  generateFilter(filter, refFilter) {
    let inputFilter = [];
    let inputFilterObj = {};
    for (const key in filter) {
      const element = filter[key];
      if (refFilter[key] === undefined) {
        continue;
      }

      let newFilter = { ...FILTER_VIEW_DATA };
      newFilter.comparison = refFilter[key]?.comparison;

      if (typeof refFilter[key]?.field === "function") {
        newFilter.field = refFilter[key]?.field(element, key);
      } else {
        newFilter.field = refFilter[key]?.field;
      }

      if (typeof refFilter[key]?.title === "function") {
        newFilter.title = `${refFilter[key]?.title(element, key)}`;
      } else {
        newFilter.title = `${refFilter[key]?.title}`;
        // newFilter.title = `${refFilter[key]?.label} ${element?.name}`;
      }

      newFilter.type = refFilter[key]?.type;

      if (typeof refFilter[key]?.value === "function") {
        newFilter.value = `${refFilter[key]?.value(element, key)}`;
      } else {
        newFilter.value = `${refFilter[key]?.value}`;
      }

      if (!newFilter.value) {
        continue;
      }

      if (typeof refFilter[key]?.fieldObj === "function") {
        inputFilterObj = {
          ...inputFilterObj,
          ...refFilter[key]?.fieldObj(element, key),
        };
      } else if (refFilter[key]?.fieldObj) {
        inputFilterObj[refFilter[key]?.fieldObj] = newFilter.value;
      } else {
        inputFilterObj[refFilter[key]?.field] = newFilter.value;
      }

      if (refFilter[key]?.removeFieldObj) {
        newFilter.removeFieldObj = refFilter[key]?.removeFieldObj;
      } else if (refFilter[key]?.fieldObj) {
        newFilter.removeFieldObj = [refFilter[key]?.fieldObj];
      } else {
        newFilter.removeFieldObj = [refFilter[key]?.field];
      }
      newFilter.filterParams = element;

      inputFilter.push(newFilter);
      // inputFilterObj[refFilter[key]?.field] = element?.id;
    }

    return { inputFilter, inputFilterObj };
  }
}

// Export the class (for ES6 modules)
export default FilterHelper;
