/*
Created by esoda
Created on Nov, 2023
Contact esoda.id
*/

import moment from "moment/moment"

const MomentHelper = {
    dateFormatDefault: 'YYYY-MM-DD',
    dateFormatReadable: 'DD MMM YYYY',
    dateFormatReadable2: 'DD MMMM YYYY',
    datetimeFormatDefault: 'YYYY-MM-DD HH:mm:ss',
    datetimeFormatReadable: 'DD MMM YYYY, HH:mm:ss',
    datetimeFormatReadable2: 'DD MMMM YYYY, HH:mm',
    monthYearFormatReadable: 'MMMM YYYY',
    getDateNow: (format = 'YYYY-MM-DD') => {
        return moment().format(format)
    },
    getDatetimeNow: (format = 'YYYY-MM-DD HH:mm:ss') => {
        return moment().format(format)
    },
    format: (date, format) => {
        return moment(date).format(format)
    },
    difference: (date1, date2, diff, truncate = false) => {
        let arrDiff = ['years', 'months', 'weeks', 'days', 'hours', 'minutes', 'seconds']
        if (!arrDiff.includes(diff)) {
            diff = 'days'
        }
        let aDate = moment(date1)
        let bDate = moment(date2)
        return aDate.diff(bDate, diff, truncate)
    }
}

export default MomentHelper