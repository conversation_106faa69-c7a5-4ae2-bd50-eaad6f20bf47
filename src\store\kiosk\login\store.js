import { create } from "zustand";
import { createTrackedSelector } from "react-tracked";
import Router from "next/router";
import CommonHelper from "@/utils/CommonHelper";
import ApiHelper from "@/utils/ApiHelper";
import { useSelectBranchStore } from "./modal/storeSelectBranch";

const page = (set, get) => ({
  setState: (newValue, cb = null, keys = []) => {
    set((prevState) => {
      if (keys.length > 0) {
        return CommonHelper.updateNestedState(prevState, keys, newValue);
      }

      // Shallow update if no keys provided
      return {
        ...prevState,
        ...newValue,
      };
    });

    if (cb) {
      setTimeout(() => {
        cb();
      }, 0);
    }
  },

  inputs: { username: "kiosky", password: "123456789" },
  errors: {},
  showPassword: false,
  ref_Loading: null,
  ref_MySnackbar: null,
  setLoadingRef: (ref) => set({ ref_Loading: ref }),
  setInputs: (inputs) => set({ inputs }),
  setErrors: (errors) => set({ errors }),

  onNotify: (message, severity) => {
    if (get().ref_MySnackbar.current?.onNotify) {
      get().ref_MySnackbar.current?.onNotify(message, severity);
    }
  },
  onTextInputListeners: (text, input) => {
    set((state) => ({ inputs: { ...state.inputs, [input]: text } }));
  },
  onTextErrorListeners: (error, input) => {
    set((state) => ({ errors: { ...state.errors, [input]: error } }));
  },
  toggleShowPassword: () =>
    set((state) => ({ showPassword: !state.showPassword })),
  onValidateInputs: () => {
    let isValid = true;
    if (!get().inputs.username) {
      get().onTextErrorListeners(
        "Username harus diisi atau tidak boleh kosong.",
        "username"
      );
      isValid = false;
    }
    if (!get().inputs.password) {
      get().onTextErrorListeners(
        "Password harus diisi atau tidak boleh kosong.",
        "password"
      );
      isValid = false;
    }

    if (isValid) {
      get().onLoginListeners(get().inputs);
    }
  },
  onLoginListeners: async (params) => {
    get().ref_Loading.onShowDialog();

    let apiParams = {
      username: params.username,
      password: params.password,
      device: "web",
    };
    let response = await ApiHelper.post(
      `kiosk/kiosk/auth/login/basic`,
      apiParams
    );
    if (response.status === 200) {
      if (response.results.selected_branch === null) {
        useSelectBranchStore
          .getState()
          .onShowDialog("", { branches: response.results.branch_array });
      } else {
        // TODO:
        // Auto login if only one branch
        get().onNotify(response?.message || "Berhasil Login", "success");
      }
    } else {
      get().onNotify(response?.message || "Terjadi Kesalahan", "error");
    }

    get().ref_Loading.onCloseDialog();
    // setTimeout(() => {
    //   get().ref_Loading.onCloseDialog();
    //   Router.replace({ pathname: "/pos" });
    // }, 500);
  },
  // END: PAGE CONTENT
});

export const useLoginStore = create((...a) => ({
  ...page(...a),
}));

const useTrackedLoginStore = createTrackedSelector(useLoginStore);

export default useTrackedLoginStore;
