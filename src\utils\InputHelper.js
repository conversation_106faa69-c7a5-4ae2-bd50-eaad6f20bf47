import ApiHelper from "./ApiHelper";
import { FILTER_VIEW_DATA } from "./const/FILTER";

export const INPUT_HELPER_DEFAULT = "";
export const INPUT_HELPER_SUBMIT = "SUBMIT";

class InputHelper {
  #state;
  #setState;
  #onLoading;
  #onNotify;
  constructor(state, setState) {
    this.#state = state;
    this.#setState = setState;
  }

  setOnloading = (onLoading) => {
    this.#onLoading = onLoading;
  };
  setOnNotify = (onNotify) => {
    this.#onNotify = onNotify;
  };

  onTextInputListeners = (text, input, callback = () => {}) => {
    this.#setState(
      (prevState) => ({
        ...prevState,
        inputs: {
          ...prevState.inputs,
          [input]: text,
        },
      }),
      () => {
        if (callback) {
          callback();
        }
      }
    );
  };
  onInputBulkChangeListener = (newInput, callback = () => {}) => {
    this.#setState(
      (prevState) => ({
        ...prevState,
        inputs: { ...prevState.inputs, ...newInput },
      }),
      () => {
        if (callback) {
          callback();
        }
      }
    );
  };
  onTextErrorListeners = (error, input) => {
    this.#setState((prevState) => ({
      ...prevState,
      errors: {
        ...prevState.errors,
        [input]: error,
      },
    }));
  };
  onNextInputListeners = (
    e,
    current,
    next,
    isRequired = false,
    onSubmit = () => {}
  ) => {
    this.onTextErrorListeners(null, current);
    if (e.key === "Enter") {
      if (next === INPUT_HELPER_SUBMIT) {
        onSubmit();
      } else {
        if (isRequired) {
          if (!this.#state.inputs[current]) {
            this.onTextErrorListeners("Harus diisi", current);
            return;
          }
        }

        if (next) {
          document.getElementById(next).focus();
        }
      }
    }
  };
  onInputFilterChangeListener = (val, name, callback = () => {}) => {
    this.#setState(
      (prevState) => ({
        ...prevState,
        inputsFilter: { ...prevState.inputsFilter, [name]: val },
      }),
      () => {
        if (callback) {
          callback();
        }
      }
    );
  };
  onInputFilterBulkChangeListener = (newInput, callback = () => {}) => {
    this.#setState(
      (prevState) => ({
        ...prevState,
        inputsFilter: { ...prevState.inputsFilter, ...newInput },
      }),
      () => {
        if (callback) {
          callback();
        }
      }
    );
  };
  onUploadImageListeners = async (event, stateName) => {
    this.#onLoading(true);
    const formData = new FormData();
    formData.append("image", event.target.files[0]);
    let response = await ApiHelper.uploadImage(formData);
    if (response.status === 200) {
      this.onTextInputListeners(response.data.fileuri, stateName);
    } else {
      this.#onNotify(response.message, "warning");
    }
    this.#onLoading(false);
  };
  onProcessFilter = (filter, callback = () => {}) => {
    const inputFilter = [];
    const inputFilterObj = {};
    for (const key in filter) {
      const element = filter[key];
      if (element.exception && element.exceptionValue === element?.value) {
        continue;
      }

      let newFilter = { ...FILTER_VIEW_DATA };
      newFilter.comparison = element.comparison;
      newFilter.field = element?.field;
      newFilter.title = `${element.label} ${element?.name}`;
      newFilter.type = element.fieldType;
      newFilter.value = element?.value;
      inputFilter.push(newFilter);
      inputFilterObj[element?.field] = element?.value;
    }

    this.#setState({ inputFilter, inputFilterObj }, callback);
  };
  onFocusElement = (elId) => {
    const elementId = document.getElementById(elId);

    if (elementId?.focus) {
      elementId?.focus();
    }
  };
}

// Export the class (for ES6 modules)
export default InputHelper;
